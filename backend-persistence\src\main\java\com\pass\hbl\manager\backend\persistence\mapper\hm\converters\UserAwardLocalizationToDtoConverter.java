package com.pass.hbl.manager.backend.persistence.mapper.hm.converters;

import com.pass.hbl.manager.backend.persistence.dto.hm.AwardLocalizationDto;
import com.pass.hbl.manager.backend.persistence.entity.shared.SharedLocalization;
import com.pass.hbl.manager.backend.persistence.util.LocalizationConstants;
import com.pass.hbl.manager.backend.persistence.util.Util;
import org.modelmapper.AbstractConverter;
import org.springframework.data.util.Pair;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class UserAwardLocalizationToDtoConverter extends AbstractConverter<List<SharedLocalization>, Map<String, AwardLocalizationDto>> {
    @Override
    protected Map<String, AwardLocalizationDto> convert(List<SharedLocalization> sharedLocalizations) {
        HashMap<String, AwardLocalizationDto> result = new HashMap<>();
        Util.toStream(sharedLocalizations)
                .map(d -> Pair.of(Util.getLanguageTag(d.getLocale()), d))
                .forEach(p -> {
                    AwardLocalizationDto dto = result.getOrDefault(p.getFirst(), new AwardLocalizationDto());
                    if (Objects.equals(p.getSecond().getKey(), LocalizationConstants.NAME)) {
                        dto.setName(p.getSecond().getValue());
                    } else {
                        dto.setDescription(p.getSecond().getValue());
                    }
                    result.put(p.getFirst(), dto);
                });
        return result;
    }
}
