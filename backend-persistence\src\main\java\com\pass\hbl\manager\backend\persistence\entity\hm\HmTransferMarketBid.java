package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.BidStatus;
import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.UUID;

import static javax.persistence.EnumType.STRING;

@Table(name = "transfer_market_bid", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@Entity
@NoArgsConstructor
@ToString
@SQLDelete(sql = "UPDATE hm.transfer_market_bid SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmTransferMarketBid extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "transfer_market_id", referencedColumnName = "id", nullable = false, updatable = false)
    private HmTransferMarket offer;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "bidder_id", referencedColumnName = "id", nullable = false, updatable = false)
    private HmUserProfile bidder;

    @NotNull
    @ToString.Exclude
    @Min(0)
    @Column(name = "bid")
    private int bid;

    @Enumerated(STRING)
    @Column(name = "status")
    private BidStatus status;

    public HmTransferMarketBid(HmTransferMarket offer, HmUserProfile bidder, int bid, BidStatus status) {
        this.offer = offer;
        this.bidder = bidder;
        this.bid = bid;
        this.status = status;
    }
}
