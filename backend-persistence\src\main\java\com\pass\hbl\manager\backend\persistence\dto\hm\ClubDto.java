package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@JsonRootName("Club")
@Getter
@Setter
@Schema(description = "Club info")
public class ClubDto extends AbstractPictureDto<ClubDto, String> {

    @Schema(description = "Club id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Size(max = 64)
    @NotBlank
    @Schema(description = "Name of the club", required = true)
    private String name;

    @Size(max = 8)
    @NotBlank
    @Schema(description = "3 char abbreviation of the club", required = true)
    private String abbreviation;
}
