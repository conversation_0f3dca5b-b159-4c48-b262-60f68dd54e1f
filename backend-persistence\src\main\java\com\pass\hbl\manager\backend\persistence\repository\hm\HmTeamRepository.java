package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmTeamDO;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmTeam;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface HmTeamRepository extends PagingAndSortingRepository<HmTeam, UUID> {

    @Query("select t from HmTeam t where t.owner.id = :ownerId and t.league.id = :leagueId")
    List<HmTeam> findByOwnerIdAndLeagueIdAndLeftIsNull(@Param("ownerId") UUID ownerId, @Param("leagueId") UUID leagueId);

    @Query(value = "select * from hm.team where owner_id = :ownerId and league_id = :leagueId and created_at < :closing and (deleted = false or deleted_at >= :closing)", nativeQuery = true)
    List<HmTeam> findByOwnerIdAndLeagueIdAndRound(@Param("ownerId") UUID ownerId, @Param("leagueId") UUID leagueId, @Param("closing") LocalDateTime closing);

    @Query(value = "select count(*) from hm.team t where t.league_id = :leagueId and t.owner_id = :ownerId and t.left is null and t.deleted = false", nativeQuery = true)
    int countByOwnerIdAndLeagueIdAndLeftIsNull(@Param("ownerId") UUID ownerId, @Param("leagueId") UUID leagueId);

    @Query("select count(p) from HmPlayer p where p.active=true and p.deleted=false and p.id not in " +
            "(select distinct t.player.id from HmTeam t " +
            "inner join HmLeagueMembership lm on lm.league.id = t.league.id and lm.deleted=false " +
            "where lm.league.id = :leagueId and t.left is null and t.deleted=false)")
    long countByLeagueIdNotAndLeftIsNull(@Param("leagueId") UUID leagueId);

    @Query(value = "SELECT * FROM hm.team t WHERE t.league_id = :leagueId and t.owner_id = :ownerId and deleted = true and deleted_at > :date", nativeQuery = true)
    List<HmTeam> findDeletedByLeagueAndOwnerAfterDate(@Param("leagueId") UUID leagueId, @Param("ownerId") UUID ownerId, @Param("date") LocalDateTime date);

    @Query(value = "SELECT Cast(id as varchar) as id, Cast(player_id as varchar) as playerId FROM hm.team t WHERE t.league_id = :leagueId and t.owner_id = :ownerId and deleted = true and deleted_at > :date", nativeQuery = true)
    List<HmTeamDO> findDeletedInfoByLeagueAndOwnerAfterDate(@Param("leagueId") UUID leagueId, @Param("ownerId") UUID ownerId, @Param("date") LocalDateTime date);

    Optional<HmTeam> findFirstByOwnerIdAndLeagueIdAndPlayerIdAndLeftIsNull(UUID ownerId, UUID leagueId, UUID playerId);

    Optional<HmTeam> findFirstByLeagueAndPlayerIdAndLeftIsNull(HmLeague league, UUID playerId);

    boolean existsByOwnerIdAndLeagueIdAndPlayerIdAndLeftIsNull(UUID ownerId, UUID leagueId, UUID playerId);

    boolean existsByLeagueIdAndPlayerIdAndLeftIsNull(UUID leagueId, UUID playerId);

    @Query("select t.player.id from HmTeam t where t.league.id = :leagueId and t.left is null and t.deleted = false")
    List<UUID> findPlayerIdsByLeagueIdAndLeftIsNull(UUID leagueId);

    @Modifying
    @Query("update HmTeam t set t.deleted = true, t.deletedAt = CURRENT_TIMESTAMP, t.left = CURRENT_TIMESTAMP where t.id = :id")
    void deleteById(@NotNull @Param("id") UUID id);

    @Modifying
    @Query("update HmTeam m set m.deleted = true, m.deletedAt = CURRENT_TIMESTAMP where m.league.id = :leagueId and m.deleted = false")
    void deleteByLeague(@Param("leagueId") UUID leagueId);

    @Modifying
    @Query("update HmTeam m set m.deleted = true, m.deletedAt = CURRENT_TIMESTAMP where (m.league.id in :leagueIds and m.deleted = false)")
    int deleteByLeagueIdIn(@Param("leagueIds") List<UUID> leagueIds);

    @Modifying
    @Query("update HmTeam m set m.deleted = true, m.deletedAt = CURRENT_TIMESTAMP where (m.owner.id = :ownerId and m.deleted = false)")
    void deleteByOwner(@Param("ownerId") UUID ownerId);

    @Modifying
    @Query("update HmTeam m set m.deleted = true, m.deletedAt = CURRENT_TIMESTAMP where (m.owner.id = :ownerId and m.league.id = :leagueId and m.deleted = false)")
    void deleteByLeagueAndOwner(@Param("leagueId") UUID leagueId, @Param("ownerId") UUID ownerId);
}
