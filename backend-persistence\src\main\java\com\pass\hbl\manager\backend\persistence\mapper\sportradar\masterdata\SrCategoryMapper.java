package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrCategory;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.sportradar.handball.v2.model.Category;
import org.springframework.stereotype.Component;

@Component
public class SrCategoryMapper extends AbstractMapper<SrCategory, Category> {
    public SrCategoryMapper() {
        super(SrCategory.class, Category.class);
    }
}
