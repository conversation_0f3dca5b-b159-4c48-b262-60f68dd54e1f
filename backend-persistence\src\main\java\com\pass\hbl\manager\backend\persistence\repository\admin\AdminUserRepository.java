package com.pass.hbl.manager.backend.persistence.repository.admin;

import com.pass.hbl.manager.backend.persistence.entity.admin.AdminUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import java.util.Optional;
import java.util.UUID;

public interface AdminUserRepository extends PagingAndSortingRepository<AdminUser, UUID> {

    Optional<AdminUser> findByUsername(String username);

    @Query(value = "SELECT * FROM admin.user WHERE lower(email_address) = lower(:email) and deleted = false LIMIT 1", nativeQuery = true)
    Optional<AdminUser> findByEmail(String email);

    boolean existsByEmailAddressIgnoreCase(String emailAddress);

    Optional<AdminUser> findByEmailAddressIgnoreCase(String email);

    Page<AdminUser> findAllByUsernameContainingIgnoreCaseOrEmailAddressContainingIgnoreCase(String username, String email, Pageable pageable);

}
