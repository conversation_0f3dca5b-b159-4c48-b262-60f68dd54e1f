package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.util.Map;

@JsonRootName("Lineup")
@Getter
@Setter
@Schema(description = "Lineup for a round")
public class LineupDto extends AbstractDto<LineupDto, String> {

    @JsonIgnore
    private String id;

    @Schema(description = "lineup for the given round. Not all positions must be set")
    private Map<Position, PlayerDto> players;

    @NotNull
    @Schema(description = "the round for this lineup", required = true)
    private RoundDto round;

    @NotNull
    @Schema(description = "the league the lineup was made for", required = true)
    private LeagueInfoDto league;

    @NotNull
    @Schema(description = "the team manager for this lineup", required = true)
    private UserDto user;

    @NotNull
    @Schema(description = "if this lineup can be modified, players could be changed etc.", required = true)
    private boolean isModifiable;

    @NotNull
    @Schema(description = "if this lineup is for past rounds and can contain former players of the team", required = true)
    private boolean isHistoric;

    @JsonCreator
    public LineupDto() {
    }

    public LineupDto(Map<Position, PlayerDto> players, RoundDto round, LeagueInfoDto league, UserDto user, boolean isModifiable, boolean isHistoric) {
        this.players = players;
        this.round = round;
        this.league = league;
        this.user = user;
        this.isModifiable = isModifiable;
        this.isHistoric = isHistoric;
    }
}
