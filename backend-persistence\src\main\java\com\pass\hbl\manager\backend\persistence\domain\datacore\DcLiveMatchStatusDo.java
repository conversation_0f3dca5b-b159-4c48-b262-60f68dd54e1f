package com.pass.hbl.manager.backend.persistence.domain.datacore;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * Represents a live match status Object received by the DataCore Streaming Api (Mqtt)
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@ToString
public class DcLiveMatchStatusDo {

    private String type;

    private String fixtureId;

    @NotNull
    private DataObject data;

    @JsonCreator
    public DcLiveMatchStatusDo() {
    }
}

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@NoArgsConstructor
class Options {
    private String reason;
    private LocalDateTime newTime;
}

