package com.pass.hbl.manager.backend.persistence.repository.datacore.streaming;

import com.pass.hbl.manager.backend.persistence.entity.datacore.DcLiveMatch;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.time.LocalDateTime;
import java.util.List;

public interface DcLiveMatchRepository extends PagingAndSortingRepository<DcLiveMatch, String> {

    @Query(value = "select max(h.modifiedAt) from DcLiveMatch h")
    LocalDateTime getLatestUpdate();

    List<DcLiveMatch> getDcLiveMatchByModifiedAtAfter(LocalDateTime after);
}
