package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@JsonRootName("Bid")
@Getter
@Setter
@Schema(description = "Reduced transfer market bid info object")
public class BidDto extends AbstractDto<BidDto, String> {

    @Schema(description = "Bid id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true, accessMode = Schema.AccessMode.READ_ONLY)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @NotNull
    @Schema(description = "User offering the bid", required = true)
    private UserDto bidder;

    @Min(0)
    @Schema(description = "Value of the bid", required = true)
    private int bid;

    @NotNull
    @Schema(description = "Status of the bid", example = "ACCEPTED", required = true )
    private BidStatus status;

    @NotNull
    @Schema(description = "creation date")
    private LocalDateTime createdAt;

}
