package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.UserProfileDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.springframework.stereotype.Component;

@Component
public class HmUserProfileMapper extends AbstractMapper<HmUserProfile, UserProfileDto> {

    public HmUserProfileMapper() {
        super(HmUserProfile.class, UserProfileDto.class);
    }
}
