package com.pass.hbl.manager.backend.persistence.mapper.datacore.live.converter;

import com.pass.hbl.manager.backend.persistence.domain.datacore.DcSportEvent;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

public abstract class DcLivePlayByPlayScoreConverter extends AbstractConverter<DcSportEvent, Integer> {

    // team number is 1 for the first team, 2 for the second team
    private final int teamNumber;

    protected DcLivePlayByPlayScoreConverter(int teamNumber) {
        this.teamNumber = teamNumber;
    }

    @Override
    public Integer convert(DcSportEvent source) {
        // Map of key = entityId (matchId), value = Entity data (score ..)
        Map<String, Integer> scores = source.getScores();
        if (scores.isEmpty()) {
            return null;
        }
        // TODO HBLMAN-565 check if the first item is home and second is away
        Set<String> keys = scores.keySet();
        List<String> keyList = new ArrayList<>(keys);
        if (keyList.size() == 2) {
            // Get the score of the match id
            String key = keyList.get(teamNumber - 1);
            return scores.get(key);
        } else {
            return null;
        }
    }

    @Component
    public static class HomeTeamScoreConverter extends DcLivePlayByPlayScoreConverter {
        public HomeTeamScoreConverter() {
            super(1);
        }
    }

    @Component
    public static class AwayTeamScoreConverter extends DcLivePlayByPlayScoreConverter {
        public AwayTeamScoreConverter() {
            super(2);
        }
    }

}
