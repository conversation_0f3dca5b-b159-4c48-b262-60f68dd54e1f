package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;

@JsonRootName("UserRanking")
@EqualsAndHashCode
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "User info object used for live manager ranking by league and round")
public class UserLiveRankingDto {

    @Schema(description = "Name of the member in the given league", required = true)
    @NotNull
    private UserDto user;

    @Schema(description = "Total score of the player in the given league", example = "846", required = true)
    private int totalScore;

    @Schema(description = "Current round score of the player in the given league", example = "846", required = true)
    private int roundScore;

}
