{"groups": [{"name": "handball-manager", "type": "com.pass.hbl.manager.backend.admin.config.HandballManagerAdminConfigurationProperties"}, {"name": "handball-manager.importer", "type": "com.pass.hbl.manager.backend.persistence.config.AbstractHandballManagerConfigurationProperties$Importer", "sourceMethod": "getImporter()"}, {"name": "handball-manager.http-proxy", "type": "com.pass.hbl.manager.backend.persistence.config.AbstractHandballManagerConfigurationProperties$HttpProxy", "sourceMethod": "getHttpProxy()"}], "properties": [{"name": "handball-manager.importer.sportradar-stage", "type": "java.lang.String", "description": "Stage of sportrader. Either trail or production.", "sourceType": "com.pass.hbl.manager.backend.persistence.config.AbstractHandballManagerConfigurationProperties$Importer"}, {"name": "handball-manager.importer.api-key", "type": "java.lang.String", "description": "API Key of sportradar.", "sourceType": "com.pass.hbl.manager.backend.persistence.config.AbstractHandballManagerConfigurationProperties$Importer"}, {"name": "handball-manager.security.admin-user", "type": "java.lang.String", "description": "Username for admin login.", "sourceType": "com.pass.hbl.manager.backend.admin.config.HandballManagerAdminConfigurationProperties$Security"}, {"name": "handball-manager.security.admin-password", "type": "java.lang.String", "description": "Password for admin login.", "sourceType": "com.pass.hbl.manager.backend.admin.config.HandballManagerAdminConfigurationProperties$Security"}, {"name": "handball-manager.http-proxy.enabled", "type": "java.lang.Bo<PERSON>an", "description": "If proxy shall be used.", "sourceType": "com.pass.hbl.manager.backend.persistence.config.AbstractHandballManagerConfigurationProperties$HttpProxy"}, {"name": "handball-manager.http-proxy.url", "type": "java.lang.String", "description": "The proxy URL.", "sourceType": "com.pass.hbl.manager.backend.persistence.config.AbstractHandballManagerConfigurationProperties$HttpProxy"}]}