package com.pass.hbl.manager.backend.persistence.mapper.sportradar.live;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrLiveMatch;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.xml.MatchXml;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.live.converter.IdConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.live.converter.LastGoalConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.live.converter.ScoreConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.live.converter.WinnerLiveConverter;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;

@Slf4j
@Component
public class XmlToSrLiveMatchMapper extends AbstractMapper<MatchXml, SrLiveMatch> {

    private final IdConverter.MatchIdConverter matchIdConverter;
    private final IdConverter.CompetitorIdConverter competitorIdConverter;
    private final ScoreConverter.Team1CurrentScoreConverter team1CurrentScoreConverter;
    private final ScoreConverter.Team2CurrentScoreConverter team2CurrentScoreConverter;
    private final ScoreConverter.Team1HalftimeScoreConverter team1HalftimeScoreConverter;
    private final ScoreConverter.Team2HalftimeScoreConverter team2HalftimeScoreConverter;
    private final ScoreConverter.Team1FinalScoreConverter team1FinalScoreConverter;
    private final ScoreConverter.Team2FinalScoreConverter team2FinalScoreConverter;

    private final WinnerLiveConverter winnerConverter;
    private final LastGoalConverter lastGoalConverter;

    public XmlToSrLiveMatchMapper(IdConverter.MatchIdConverter matchIdConverter,
                                  IdConverter.CompetitorIdConverter competitorIdConverter,
                                  ScoreConverter.Team1CurrentScoreConverter team1CurrentScoreConverter,
                                  ScoreConverter.Team2CurrentScoreConverter team2CurrentScoreConverter,
                                  ScoreConverter.Team1HalftimeScoreConverter team1HalftimeScoreConverter,
                                  ScoreConverter.Team2HalftimeScoreConverter team2HalftimeScoreConverter,
                                  ScoreConverter.Team1FinalScoreConverter team1FinalScoreConverter,
                                  ScoreConverter.Team2FinalScoreConverter team2FinalScoreConverter,
                                  WinnerLiveConverter winnerConverter,
                                  LastGoalConverter lastGoalConverter) {
        super(MatchXml.class, SrLiveMatch.class);
        this.matchIdConverter = matchIdConverter;
        this.competitorIdConverter = competitorIdConverter;
        this.team1CurrentScoreConverter = team1CurrentScoreConverter;
        this.team2CurrentScoreConverter = team2CurrentScoreConverter;
        this.team1HalftimeScoreConverter = team1HalftimeScoreConverter;
        this.team2HalftimeScoreConverter = team2HalftimeScoreConverter;
        this.team1FinalScoreConverter = team1FinalScoreConverter;
        this.team2FinalScoreConverter = team2FinalScoreConverter;
        this.winnerConverter = winnerConverter;
        this.lastGoalConverter = lastGoalConverter;
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected void customizeInit() {
        Converter<ZonedDateTime, LocalDateTime> toLocalDateTimeConverter = ctx -> {
            try {
                return ctx.getSource() == null ? null : Util.toUtc(ctx.getSource());
            } catch (Exception e) {
                log.error("failed to convert zoned datetime " + ctx.getSource() +" to local datetime. Reason: " + e);
                return null;
            }
        };

        TypeMap<MatchXml, SrLiveMatch> typeMap = getOrCreateTypeMap(MatchXml.class, SrLiveMatch.class);
        typeMap.addMappings(mapper -> mapper.using(matchIdConverter).map(MatchXml::getId, SrLiveMatch::setId));
        typeMap.addMappings(mapper -> mapper.using(toLocalDateTimeConverter).map(source -> source.getMatchDate().getValue(), SrLiveMatch::setMatchDate));
        typeMap.addMappings(mapper -> mapper.using(competitorIdConverter).map(source -> source.getTeam1().getUniqueTeamId(), SrLiveMatch::setTeam1Id));
        typeMap.addMappings(mapper -> mapper.using(competitorIdConverter).map(source -> source.getTeam2().getUniqueTeamId(), SrLiveMatch::setTeam2Id));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getStatus().getCode(), SrLiveMatch::setStatus));
        typeMap.addMappings(mapper -> mapper.using(winnerConverter).map(MatchXml::getWinner, SrLiveMatch::setWinner));
        typeMap.addMappings(mapper -> mapper.using(team1CurrentScoreConverter).map(MatchXml::getScores, SrLiveMatch::setTeam1CurrentScore));
        typeMap.addMappings(mapper -> mapper.using(team2CurrentScoreConverter).map(MatchXml::getScores, SrLiveMatch::setTeam2CurrentScore));
        typeMap.addMappings(mapper -> mapper.using(team1HalftimeScoreConverter).map(MatchXml::getScores, SrLiveMatch::setTeam1HalftimeScore));
        typeMap.addMappings(mapper -> mapper.using(team2HalftimeScoreConverter).map(MatchXml::getScores, SrLiveMatch::setTeam2HalftimeScore));
        typeMap.addMappings(mapper -> mapper.using(team1FinalScoreConverter).map(MatchXml::getScores, SrLiveMatch::setTeam1FinalScore));
        typeMap.addMappings(mapper -> mapper.using(team2FinalScoreConverter).map(MatchXml::getScores, SrLiveMatch::setTeam2FinalScore));
        typeMap.addMappings(mapper -> mapper.using(toLocalDateTimeConverter).map(source -> source.getCurrentPeriodStart().getValue(), SrLiveMatch::setCurrentPeriodStart));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getTime().getValue(), SrLiveMatch::setTime));
        typeMap.addMappings(mapper -> mapper.using(toLocalDateTimeConverter).map(source -> source.getLastGoal().getTime().getValue(), SrLiveMatch::setLastGoalTime));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getLastGoal().getMatchTime().getValue(), SrLiveMatch::setLastGoalMatchTime));
        typeMap.addMappings(mapper -> mapper.using(lastGoalConverter).map(MatchXml::getLastGoal, SrLiveMatch::setLastGoalTeam));
    }
}
