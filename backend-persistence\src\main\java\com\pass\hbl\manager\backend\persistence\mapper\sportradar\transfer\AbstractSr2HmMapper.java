package com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.AbstractSportradarEntity;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.IdConverter;
import lombok.Getter;
import org.modelmapper.TypeMap;

public abstract class AbstractSr2HmMapper<SrType extends AbstractSportradarEntity, HmType extends AbstractEntity> extends AbstractMapper<SrType, HmType> {

    @Getter
    private final IdConverter idConverter;

    public AbstractSr2HmMapper(Class<SrType> srTypeClass, Class<HmType> hmTypeClass, IdConverter idConverter) {
        this (srTypeClass, hmTypeClass, idConverter, true);
    }

    public AbstractSr2HmMapper(Class<SrType> srTypeClass, Class<HmType> hmTypeClass, IdConverter idConverter, boolean registerConverters) {
        super(srTypeClass, hmTypeClass, registerConverters);
        this.idConverter = idConverter;
        TypeMap<SrType, HmType> typeMap = getOrCreateTypeMap(srTypeClass, hmTypeClass);
        typeMap.addMappings(mapper -> mapper.using(idConverter).map(AbstractSportradarEntity::getId, AbstractEntity::setId));
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected <Source, Dest> TypeMap<Source, Dest> getOrCreateTypeMap(Class<Source> source, Class<Dest> dest) {
        TypeMap<Source, Dest> map = super.getOrCreateTypeMap(source, dest);
        @SuppressWarnings("unchecked")
        TypeMap<SrType, HmType> typeMap = (TypeMap<SrType, HmType>) map;
        typeMap.addMappings(mapper -> mapper.skip(HmType::setModified));
        typeMap.addMappings(mapper -> mapper.skip(HmType::setModifiedAt));
        typeMap.addMappings(mapper -> mapper.skip(HmType::setCreatedAt));
        typeMap.addMappings(mapper -> mapper.skip(HmType::setDeleted));
        typeMap.addMappings(mapper -> mapper.skip(HmType::setDeletedAt));
        return map;
    }
}
