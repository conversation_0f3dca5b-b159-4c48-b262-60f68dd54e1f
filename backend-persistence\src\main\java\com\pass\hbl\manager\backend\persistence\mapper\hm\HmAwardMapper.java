package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.AwardDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmAward;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.UserAwardLocalizationToDtoConverter;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.UserAwardLocalizationToEntityConverter;
import com.pass.hbl.manager.backend.persistence.repository.shared.SharedLocalizationRepository;
import org.modelmapper.PropertyMap;
import org.springframework.stereotype.Component;

@Component
public class HmAwardMapper extends AbstractMapper<HmAward, AwardDto> {


    public HmAwardMapper(SharedLocalizationRepository localizationRepository) {
        super(HmAward.class, AwardDto.class);

        // cannot do this in customInit() as localizationRepository there is still null
        getModelMapper().addMappings(new PropertyMap<AwardDto, HmAward>() {
            @Override
            protected void configure() {
                using(new UserAwardLocalizationToEntityConverter(localizationRepository)).map(source.getLocalizations(), destination.getDescriptions());
            }
        });

        getModelMapper().addMappings(new PropertyMap<HmAward, AwardDto>() {
            public void configure() {
                using(new UserAwardLocalizationToDtoConverter()).map(source.getDescriptions(), destination.getLocalizations());
            }
        });
    }
}
