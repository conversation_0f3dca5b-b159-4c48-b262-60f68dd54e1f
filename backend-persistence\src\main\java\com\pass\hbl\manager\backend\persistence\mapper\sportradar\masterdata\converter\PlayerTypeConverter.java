package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.sportradar.handball.v2.model.EnumPlayerType;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

@Component
public class PlayerTypeConverter extends AbstractConverter<EnumPlayerType, String> {
    @Override
    protected String convert(EnumPlayerType source) {
        return source == null ? null : source.getValue();
    }
}
