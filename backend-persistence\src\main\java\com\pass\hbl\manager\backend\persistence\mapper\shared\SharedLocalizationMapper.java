package com.pass.hbl.manager.backend.persistence.mapper.shared;

import com.pass.hbl.manager.backend.persistence.dto.shared.LocalizationDto;
import com.pass.hbl.manager.backend.persistence.entity.shared.SharedLocalization;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.springframework.stereotype.Component;

@Component
public class SharedLocalizationMapper extends AbstractMapper<SharedLocalization, LocalizationDto> {

    public SharedLocalizationMapper() {super(SharedLocalization.class, LocalizationDto.class);}

}
