package com.pass.hbl.manager.backend.persistence.domain.datacore;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;


/**
 * Represents a live match statistics Object received by the DataCore Streaming Api (Mqtt)
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class DcLiveMatchStatisticsDo {

    private Map<String, Team> teams;

    @JsonCreator
    public DcLiveMatchStatisticsDo(Map<String, Team> teams) {
        this.teams = teams;
    }

    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Getter
    @Setter
    @NoArgsConstructor
    public static class Team {
        private Map<String, PersonStatistics> persons = new HashMap<>();
    }

    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Getter
    @Setter
    @NoArgsConstructor
    public static class PersonStatistics {
        private int assists;
        private int blocks;
        private int fieldGoalsScored;
        private String minutes;
        private int goalKeeperSecondsPlayed;
        private int timePlayed;
        private int goalsScored;
        private int goalKeeperGoalsAgainst;
        private int redCards;
        private double goalKeeperSaveAccuracy;
        private int goalKeeperShotsSaved;
        private int sevenMetreGoalsScored;
        private int goalKeeperSevenMetreShotsSaved;
        private double shootingAccuracy;
        private int shots;
        private int goalKeeperShotsAgainst;
        private int missedShots;
        private int shotsOnGoal;
        private int steals;
        private int suspensions;
        private int technicalFaults;
        private int yellowCards;
    }
}

