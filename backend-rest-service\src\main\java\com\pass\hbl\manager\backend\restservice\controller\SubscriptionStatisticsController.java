package com.pass.hbl.manager.backend.restservice.controller;

import com.pass.hbl.manager.backend.persistence.dto.hm.SubscriptionStatisticsDto;
import com.pass.hbl.manager.backend.persistence.exception.RateLimitExceededException;
import com.pass.hbl.manager.backend.persistence.service.hm.SubscriptionStatisticsService;
import com.pass.hbl.manager.backend.restservice.util.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.converters.models.PageableAsQueryParam;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

import static com.pass.hbl.manager.backend.persistence.util.Constants.DEFAULT_SUBSCRIPTION_STAT_EXTERNAL_CLIENT;
import static com.pass.hbl.manager.backend.persistence.util.Constants.X_RATE_LIMIT_RESET;

/**
 * REST controller for subscription statistics endpoints
 * Provides comprehensive subscription and payment data for external clients
 */
@RestController
@RequestMapping(ApiConstants.STAT_API + "/subscription")
@Validated
@Tag(name = "Subscription Statistics", description = "Subscription statistics API for external clients")
@Slf4j
public class SubscriptionStatisticsController {

    private final SubscriptionStatisticsService subscriptionStatisticsService;

    public SubscriptionStatisticsController(SubscriptionStatisticsService subscriptionStatisticsService) {
        this.subscriptionStatisticsService = subscriptionStatisticsService;
    }

    @Operation(
            summary = "Get all subscription statistics",
            description = "Retrieve comprehensive subscription and payment data with optional filtering by modification date. " +
                         "Supports pagination and includes rate limiting for external clients."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved subscription statistics",
                    content = @Content(mediaType = "application/json",
                            array = @ArraySchema(schema = @Schema(implementation = SubscriptionStatisticsDto.class)))),
            @ApiResponse(responseCode = "429", description = "Rate limit exceeded",
                    content = @Content(mediaType = "text/plain")),
            @ApiResponse(responseCode = "400", description = "Invalid request parameters")
    })
    @GetMapping(value = "/details", produces = MediaType.APPLICATION_JSON_VALUE)
    @PageableAsQueryParam
    public List<SubscriptionStatisticsDto> getAllSubscriptionStatistics(
            @Parameter(description = "Filter subscriptions modified after this date")
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime changedAfter,
            Pageable pageable,
            HttpServletRequest request,
            HttpServletResponse response
    ) throws IOException {

        String requestUrl = request.getRequestURI();
        // This ensures consistent tracking and rate limiting for all subscription statistics requests
        String externalClient = DEFAULT_SUBSCRIPTION_STAT_EXTERNAL_CLIENT;

        log.info("Received request for all subscription statistics from client: {} with URL: {}", externalClient, requestUrl);

        try {
            return subscriptionStatisticsService.getAllSubscriptionStatistics(changedAfter, pageable, requestUrl, externalClient);
        } catch (RateLimitExceededException rateLimitExceededException) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            response.getWriter().write(rateLimitExceededException.getMessage());
            // Time when rate limit will be reset
            response.setHeader(X_RATE_LIMIT_RESET, Long.toString(rateLimitExceededException.getRateLimitResetEpoch()));
            return null;
        } catch (IllegalArgumentException e) {
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.getWriter().write("Invalid request parameters: " + e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("Unexpected error in getAllSubscriptionStatistics", e);
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.getWriter().write("Internal server error occurred");
            return null;
        }
    }
}
