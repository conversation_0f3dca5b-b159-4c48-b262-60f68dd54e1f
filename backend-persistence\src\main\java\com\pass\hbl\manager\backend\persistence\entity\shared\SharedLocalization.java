package com.pass.hbl.manager.backend.persistence.entity.shared;

import com.pass.hbl.manager.backend.persistence.dto.shared.EntityType;
import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import com.pass.hbl.manager.backend.persistence.entity.hm.converter.LocaleConverter;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Locale;
import java.util.UUID;

import static javax.persistence.EnumType.STRING;

@Table(name = "localization", schema = "shared", catalog = "handball_manager")
@Getter
@Setter
@Entity
@ToString
@NoArgsConstructor
@SQLDelete(sql = "UPDATE shared.localization SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class SharedLocalization extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @Column(name = "entity_type")
    @Enumerated(STRING)
    @NotNull
    private EntityType entityType;

    @Column(name = "entity_id")
    private UUID entityId;

    @Size(max = 256)
    @Column(name = "key")
    @NotNull
    private String key;

    @Size(max = 1024)
    @Column(name = "value")
    @NotNull
    private String value;

    @Convert(converter = LocaleConverter.class)
    @NotNull
    @Column(name = "language_code")
    private Locale locale;


    public SharedLocalization(EntityType entityType, UUID entityId, String key, Locale locale, String value) {
        this.entityType = entityType;
        this.entityId = entityId;
        this.key = key;
        this.value = value;
        this.locale = locale;
    }

    public SharedLocalization(EntityType entityType, UUID entityId, Locale locale, String key) {
        this.entityType = entityType;
        this.entityId = entityId;
        this.key = key;
        this.locale = locale;
    }
}
