package com.pass.hbl.manager.backend.persistence.mapper.datacore.masterdata;

import com.pass.hbl.manager.backend.persistence.entity.datacore.DcSeason;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.masterdata.converter.DcCompetitionConverter;
import com.sportradar.datacore.rest.model.SeasonsModel;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

@Component
public class DcSeasonMapper extends AbstractMapper<DcSeason, SeasonsModel> {

    private final DcCompetitionConverter competitionConverter;

    public DcSeasonMapper(DcCompetitionConverter competitionConverter) {
        super(DcSeason.class, SeasonsModel.class);
        this.competitionConverter = competitionConverter;
    }

    @Override
    protected void customizeInit() {
        TypeMap<SeasonsModel, DcSeason> typeMap = getOrCreateTypeMap(SeasonsModel.class, DcSeason.class);
        typeMap.addMappings(mapper -> mapper.using(competitionConverter).map(SeasonsModel::getCompetitionId, DcSeason::setCompetition));
        typeMap.addMappings(mapper -> mapper.map(SeasonsModel::getSeasonId, DcSeason::setId));
        typeMap.addMappings(mapper -> mapper.map(SeasonsModel::getNameLocal, DcSeason::setName));
    }
}
