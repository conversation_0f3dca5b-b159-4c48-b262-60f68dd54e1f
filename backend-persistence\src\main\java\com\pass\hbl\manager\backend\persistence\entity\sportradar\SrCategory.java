package com.pass.hbl.manager.backend.persistence.entity.sportradar;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Table(name = "category", schema = "sportradar", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class SrCategory extends AbstractSportradarEntity {

    @NotNull
    @Column(name = "name")
    private String name;

    @NotNull
    @Column(name = "country_code")
    private String countryCode;
}
