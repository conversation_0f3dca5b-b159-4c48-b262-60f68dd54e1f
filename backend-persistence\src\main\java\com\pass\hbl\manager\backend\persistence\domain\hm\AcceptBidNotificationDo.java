package com.pass.hbl.manager.backend.persistence.domain.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.UserDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Domain object for transfer market accept bid push notification
 */
@Getter
@Setter
public class AcceptBidNotificationDo {

    private String playerName;

    private String ownerUserName;

    private String leagueName;

    private List<Pair<UserDto, TransferMarketNotificationDo>> notificationByBidder;

    private Map<UUID, List<HmAwardDescriptionDO>> awardsByUserId;

    UUID systemUserId;

    public AcceptBidNotificationDo(String playerName, String ownerUserName, String leagueName, List<Pair<UserDto, TransferMarketNotificationDo>> notificationByBidder, Map<UUID, List<HmAwardDescriptionDO>> awardsByUserId, UUID systemUserId) {
        this.playerName = playerName;
        this.ownerUserName = ownerUserName;
        this.leagueName = leagueName;
        this.notificationByBidder = notificationByBidder;
        this.awardsByUserId = awardsByUserId;
        this.systemUserId = systemUserId;
    }
}
