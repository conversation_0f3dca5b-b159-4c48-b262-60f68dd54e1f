package com.pass.hbl.manager.backend.admin.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI handballManagerOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("HBL handball manager admin API")
                        .version("1.0")
                        .description("Internal admin API")
                )
                .components(new Components()
                                .addSecuritySchemes("basicAuth",
                                new SecurityScheme()
                                        .name("basicAuth")
                                        .type(SecurityScheme.Type.HTTP)
                                        .in(SecurityScheme.In.HEADER)
                                        .scheme("basic")
                        )
                )
                .security(List.of(new SecurityRequirement().addList("basicAuth")));
    }
}
