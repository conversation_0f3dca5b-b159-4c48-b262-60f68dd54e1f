package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrMatchPlayerStatistics;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.sportradar.handball.v2.model.SportEventPlayerStatisticsStatistics;
import org.springframework.stereotype.Component;

@Component
public class SrMatchPlayerStatisticsMapper extends AbstractMapper<SportEventPlayerStatisticsStatistics, SrMatchPlayerStatistics> {

    public SrMatchPlayerStatisticsMapper() {
        super(SportEventPlayerStatisticsStatistics.class, SrMatchPlayerStatistics.class);
    }
}
