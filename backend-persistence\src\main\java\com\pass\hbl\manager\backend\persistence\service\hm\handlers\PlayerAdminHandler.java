package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.domain.admin.AdminPlayerInfoDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerOfMonthDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerMarketValue;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerMarketValueRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
@Transactional
/*
  Handles player operations performed by the administrator (system)
 */
public class PlayerAdminHandler {

    private final PlayerScoreHandler playerScoreHandler;
    private final PlayerOfMonthHandler playerOfMonthHandler;

    private final HmPlayerMarketValueRepository playerMarketValueRepository;

    private final LogMessageService logMessageService;
    private final HmPlayerRepository playerRepository;
    private final TransactionHandler transactionHandler;

    public PlayerAdminHandler(@Lazy PlayerScoreHandler playerScoreHandler, @Lazy PlayerOfMonthHandler playerOfMonthHandler, @Lazy HmPlayerMarketValueRepository playerMarketValueRepository, @Lazy LogMessageService logMessageService, @Lazy HmPlayerRepository playerRepository, TransactionHandler transactionHandler) {
        this.playerScoreHandler = playerScoreHandler;
        this.playerOfMonthHandler = playerOfMonthHandler;
        this.playerMarketValueRepository = playerMarketValueRepository;
        this.logMessageService = logMessageService;
        this.playerRepository = playerRepository;
        this.transactionHandler = transactionHandler;
    }

    public PlayerOfMonthDto setPlayerOfMonth(HmPlayer player, @NotNull Integer month, Integer totalScore) throws EntityNotExistException {
        // set the player of month over all positions
        return playerOfMonthHandler.setPlayerOfMonth(player, month, totalScore);
    }

    public List<AdminPlayerInfoDO> getAllAdminPlayerInfoDOs(List<HmPlayer> players) {
        //return players.stream().map(p -> p.getFirstName() +";" + p.getLastName() + "; id=" + p.getId()).toList();
        return players.stream().map(p -> new AdminPlayerInfoDO(p.getId(), p.getFirstName(), p.getLastName(), p.getPosition(), p.getMarketValue(), p.getActive())).toList();
    }

    public String getAllPlayerScoresByRound(UUID roundId) {
        return playerScoreHandler.getAllPlayerScoresByRound(roundId);
    }

    public Optional<HmPlayerMarketValue> getExistingMarketValue(UUID playerId) {
        return playerMarketValueRepository.findByPlayerIdAndCurrentValue(playerId, true);
    }

    // Method to add or update a player's market value
    public void addOrUpdatePlayerMarketValue(String playerId, int currentMarketValue, String adminUser) {
        try {
            // Look for an existing market value for this player
            Optional<HmPlayerMarketValue> existingMarketValueOpt = getExistingMarketValue(UUID.fromString(playerId));
            if (existingMarketValueOpt.isPresent()) {
                // Update the existing market value
                HmPlayerMarketValue existingMarketValue = existingMarketValueOpt.get();
                existingMarketValue.setMarketValue(currentMarketValue);
                playerMarketValueRepository.save(existingMarketValue);
                String message = "Updated market value to [" + currentMarketValue + "] for player [" + playerId + "] by Admin with E-mail [" + adminUser + "]";
                log.info(message);
                logMessageService.logInfo("addOrUpdatePlayerMarketValue", message);
            } else {
                // Check if the player exists
                Optional<HmPlayer> player = transactionHandler.runInNewTransactionReadOnly(() -> playerRepository.findById(UUID.fromString(playerId)));
                if (player.isPresent()) {
                    // Save a new market value
                    HmPlayerMarketValue newMarketValue = new HmPlayerMarketValue(player.get(), LocalDateTime.of(LocalDate.now(), LocalTime.MIN), currentMarketValue, null, true);
                    playerMarketValueRepository.save(newMarketValue);
                    String message = "Added new market value [" + currentMarketValue + "] to player [" + playerId + "] by Admin with E-mail [" + adminUser + "]";
                    log.info(message);
                    logMessageService.logInfo("addOrUpdatePlayerMarketValue", message);
                } else {
                    String message = "Player with ID [" + playerId + "] not found. Admin E-mail [" + adminUser + "]";
                    log.warn(message);
                    logMessageService.logWarning("addOrUpdatePlayerMarketValue", message);
                }
            }
        } catch (Exception e) {
            logMessageService.logException("Error in addOrUpdatePlayerMarketValue for player " + playerId + " with market value " + currentMarketValue + " by Admin with E-mail " + adminUser + ": ", e);
        }
    }
}
