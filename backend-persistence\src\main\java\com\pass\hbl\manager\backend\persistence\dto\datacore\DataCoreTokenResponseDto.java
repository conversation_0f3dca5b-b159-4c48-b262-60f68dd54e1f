package com.pass.hbl.manager.backend.persistence.dto.datacore;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonRootName("get datacore token response")
@Getter
@Setter
@Schema(description = "Get datacore access token response data")
public class DataCoreTokenResponseDto {

    private DataObject data;

    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonRootName("get datacore token data")
    @Getter
    @Setter
    @NoArgsConstructor
    public static class DataObject {

        @NotNull
        @Schema(description = "token", required = true)
        private String token;

        @NotNull
        @Schema(description = "tokenType", required = true)
        private String tokenType;

        @NotNull
        @Schema(description = "expiresIn", required = true)
        private int expiresIn;
    }

    @JsonCreator
    public DataCoreTokenResponseDto() {
    }
}
