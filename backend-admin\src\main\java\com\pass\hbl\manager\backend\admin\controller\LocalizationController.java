package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.dto.admin.LogMessageDto;
import com.pass.hbl.manager.backend.persistence.dto.shared.EntityType;
import com.pass.hbl.manager.backend.persistence.dto.shared.LocalizationDto;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.service.shared.LocalizationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.core.converters.models.PageableAsQueryParam;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.security.RolesAllowed;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping(ApiConstants.LOCALIZATION_API)
@Validated
@Tag(name = "localization")
@RolesAllowed({ApiConstants.ROLE_ADMIN})
public class LocalizationController extends AbstractController {

    private final LocalizationService service;

    public LocalizationController(ApplicationEventPublisher eventPublisher, LocalizationService service) {
        super(eventPublisher);
        this.service = service;
    }

    @Operation(description = "Get localization by id.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = LocalizationDto.class)))})
    })
    @GetMapping(value = "{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public LocalizationDto get(
            @Parameter(name = "id", description = "Localization id")
            @PathVariable(name = "id")
                    String id) throws EntityNotExistException, FormatException {
        return service.getByIdAsDto(id);
    }


    @Operation(description = "Get filtered localizations.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = LogMessageDto.class)))})
    })
    @GetMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    @PageableAsQueryParam
    public List<LocalizationDto> get(
                    @Parameter(name = "entityType", description = "Entity type")
                    @RequestParam(name = "entityType", required = false)
                    EntityType entityType,
                    @Parameter(name = "entityId", description = "Entity id")
                    @RequestParam(name = "entityId", required = false)
                    String entityId,
                    @Parameter(name = "language", description = "Language tag")
                    @RequestParam(name = "language", required = false)
                    String language,
                    @Parameter(name = "key", description = "Resource bundle key")
                    @RequestParam(name = "key", required = false)
                    String key,
                    @Parameter(hidden=true) Pageable pageable,
                    UriComponentsBuilder uriBuilder,
                    HttpServletRequest request,
                    HttpServletResponse response) throws FormatException {
        return getListFromPageableResult(this, uriBuilder, request, response, service.get(entityType, entityId, language, key, pageable));
    }



    @Operation(description = "Get list of available entity types")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = String.class)))})
    })
    @GetMapping(value = "/entityTypes", produces = MediaType.APPLICATION_JSON_VALUE)
    public String[] getEntityTypes() throws InvalidOperationException {
        return getEnumValues(EntityType.class);
    }
}
