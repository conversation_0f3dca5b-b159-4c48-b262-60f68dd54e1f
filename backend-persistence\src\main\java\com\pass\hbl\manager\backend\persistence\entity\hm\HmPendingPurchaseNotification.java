package com.pass.hbl.manager.backend.persistence.entity.hm;


import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.*;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.UUID;

@Table(name = "pending_purchase_notification", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@ToString
@SQLDelete(sql = "UPDATE hm.pending_purchase_notification SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmPendingPurchaseNotification extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @Column(name = "token")
    private String token;

    @NotNull
    @Column(name = "notification_type")
    private int notificationType;

    @Column(name = "order_id")
    private String orderId;

    @Column(name = "expiry_date")
    private LocalDateTime expiryDate;

    public HmPendingPurchaseNotification(String token, int notificationType, String orderId, LocalDateTime expiryDate) {
        this.token = token;
        this.notificationType = notificationType;
        this.orderId = orderId;
        this.expiryDate = expiryDate;
    }

    public HmPendingPurchaseNotification(String token, int notificationType) {
        this.token = token;
        this.notificationType = notificationType;
    }
}
