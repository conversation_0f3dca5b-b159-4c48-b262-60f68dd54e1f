package com.pass.hbl.manager.backend.persistence.dto.admin;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@JsonRootName("AdminPlayerMarketValueRequestDto")
@Getter
@Setter
@ToString(callSuper = true)
public class AdminPlayerMarketValueRequestDto {

    @Schema(name = "playerIds", description = "List Ids for players", required = true)
    @NotEmpty
    private List<String> playerIds;

    @Schema(name = "currentMarketValue", description = "MarketValue for players", required = true)
    @NotNull
    private Integer currentMarketValue;
}