package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerProfileDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerMarketValue;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.PlayerStatisticsAggregator;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmTransferMarketRepository;
import com.pass.hbl.manager.backend.persistence.service.hm.PlayerService;
import com.pass.hbl.manager.backend.persistence.service.hm.TeamService;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static com.pass.hbl.manager.backend.persistence.util.MappingConstants.LEAGUE_ID;
import static java.util.Objects.nonNull;

@Slf4j
@Component
public class HmPlayerProfileMapper extends AbstractMapper<HmPlayer, PlayerProfileDto> {

    private final PlayerStatisticsAggregator playerStatisticsAggregator;

    private final HmTransferMarketRepository transferMarketRepository;

    private final PlayerService playerService;
    private final TeamService teamService;

    public HmPlayerProfileMapper(PlayerStatisticsAggregator playerStatisticsAggregator, HmTransferMarketRepository transferMarketRepository,
                                 @Lazy PlayerService playerService, @Lazy TeamService teamService) {
        super(HmPlayer.class, PlayerProfileDto.class);
        this.playerStatisticsAggregator = playerStatisticsAggregator;
        this.transferMarketRepository = transferMarketRepository;
        this.playerService = playerService;
        this.teamService = teamService;
    }

    @Override
    protected void customizeInit() {
        TypeMap<HmPlayer, PlayerProfileDto> e2d = getModelMapper().createTypeMap(HmPlayer.class, PlayerProfileDto.class);

        Converter<String, String> toAlpha2LanguageCode = ctx -> {
            try {
                return ctx.getSource() == null ? null : Util.convertToIso2LanguageTag(ctx.getSource());
            } catch (FormatException e) {
                return ctx.getSource();
            }
        };

        e2d.addMappings(mapper -> mapper.using(toAlpha2LanguageCode).map(HmPlayer::getNationality, PlayerProfileDto::setNationality));
    }

    @Override
    protected PlayerProfileDto customizeMapToDto(PlayerProfileDto playerProfileDto, HmPlayer hmPlayer, Map<String, Object> context) {
        // Set player transfer value of the current league
        if (nonNull(context) && context.containsKey(LEAGUE_ID)) {
            UUID leagueId = (UUID) (context.get(LEAGUE_ID));
            UUID playerId = hmPlayer.getId();
            Optional<Integer> transferBid = getLastPlayerTransferInLeague(playerId, leagueId);
            if (transferBid.isPresent()) {
                playerProfileDto.setTransferValue(transferBid.get());
            } else {
                // set the transfer value by joining the league
                setInitialTransferValue(playerProfileDto, leagueId, playerId);
            }
            playerProfileDto.setLeagueId(leagueId.toString());
            playerProfileDto.setPlayedRoundsStatistics(this.playerStatisticsAggregator.getPlayerStatistics(playerId, hmPlayer.getPosition()));
        }
        return super.customizeMapToDto(playerProfileDto, hmPlayer, context);
    }

    private void setInitialTransferValue(PlayerProfileDto playerProfileDto, UUID leagueId, UUID playerId) {
        try {
            teamService.getTeam(leagueId, playerId).ifPresent(hmTeam -> {
                Optional<HmPlayerMarketValue> playerMarketValueOpt = playerService.getMarketValueAtDate(playerId, hmTeam.getJoined().toLocalDate());
                playerMarketValueOpt.ifPresent(playerMarketValue -> playerProfileDto.setTransferValue(playerMarketValue.getMarketValue()));
            });
        } catch (Exception e) {
            log.info("Failed to retrieve the player [" + playerId + "] market value when joining league [" + leagueId + "]. Reason: " + e.getMessage());
        }
    }

    private Optional<Integer> getLastPlayerTransferInLeague(UUID playerId, UUID leagueId) {
        Optional<Object> lastPlayerTransferValueOpt = transferMarketRepository.findLastPlayerTransferValueInLeague(leagueId, playerId);
        return lastPlayerTransferValueOpt.map(o -> (Integer) o);
    }
}
