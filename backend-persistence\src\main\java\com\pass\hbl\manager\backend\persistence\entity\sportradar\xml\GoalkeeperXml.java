package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "Goalkeeper")
@NoArgsConstructor
@Data
public class GoalkeeperXml {

    @JacksonXmlProperty(localName = "id", isAttribute = true)
    private Integer id;

    @JacksonXmlProperty(localName = "team", isAttribute = true)
    private Integer team;
}
