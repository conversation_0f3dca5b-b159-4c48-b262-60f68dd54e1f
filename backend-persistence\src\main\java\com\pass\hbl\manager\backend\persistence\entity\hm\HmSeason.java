package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Table(name = "season", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@SQLDelete( sql = "UPDATE hm.season SET deleted = true, deleted_at = now() WHERE id=? and version=?")
@Where(clause = "deleted=false")
public class HmSeason extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotBlank
    @Size(max = 150)
    @Column( name = "name")
    private String name;

    @NotNull
    @Column(name = "start_date")
    private LocalDate startDate;

    @NotNull
    @Column(name = "end_date")
    private LocalDate endDate;

    @NotNull
    @Column(name = "year")
    private String year;

    @ToString.Exclude
    @OneToMany(mappedBy = "season", fetch = FetchType.EAGER, cascade = {CascadeType.ALL})
    private List<HmRound> rounds;

    //Only the persistence provider can increment or update
    @Version
    @Column(name = "version")
    private Integer version;
}
