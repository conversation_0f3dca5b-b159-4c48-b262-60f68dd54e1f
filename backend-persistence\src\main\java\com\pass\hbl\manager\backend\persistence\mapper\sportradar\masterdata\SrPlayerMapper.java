package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrPlayer;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter.PlayerTypeConverter;
import com.sportradar.handball.v2.model.Player;
import org.springframework.stereotype.Component;

@Component
public class SrPlayerMapper extends AbstractMapper<SrPlayer, Player> {

    private final PlayerTypeConverter playerTypeConverter;
    public SrPlayerMapper(PlayerTypeConverter playerTypeConverter) {
        super(SrPlayer.class, Player.class);
        this.playerTypeConverter = playerTypeConverter;
    }

    @Override
    protected void customizeInit() {
        getModelMapper().addConverter(playerTypeConverter);
    }
}
