package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;

@JsonRootName("Match")
@Getter
@Setter
@Schema(description = "Match Round transfer object")
public class MachtRoundDto extends AbstractDto<MatchDto, String> {

    @Schema (description = "Match id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "Time the match starts", example = "2022-07-21T15:30:00.000")
    private ZonedDateTime startTime;

    @Schema(description = "The home team")
    @NotNull
    private ClubDto home;

    @Schema(description = "The away team")
    @NotNull
    private ClubDto away;
}