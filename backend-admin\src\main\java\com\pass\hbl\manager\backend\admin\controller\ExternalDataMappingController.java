package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalDataMappingDto;
import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.service.admin.ExternalDataMappingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.core.converters.models.PageableAsQueryParam;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.security.RolesAllowed;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping(ApiConstants.EXTERNAL_MAPPING_API)
@Validated
@Tag(name = "externalMapping")
@RolesAllowed({ApiConstants.ROLE_ADMIN})
public class ExternalDataMappingController extends AbstractController {

    private final ExternalDataMappingService service;

    public ExternalDataMappingController(ApplicationEventPublisher eventPublisher, ExternalDataMappingService service) {
        super(eventPublisher);
        this.service = service;
    }

    @Operation(description = "Get external data mapping.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = ExternalDataMappingDto.class)))})
    })
    @GetMapping(value = "{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ExternalDataMappingDto get(
            @Parameter(name = "id", description = "Log message id")
            @PathVariable(name = "id")
                    String id) throws EntityNotExistException, FormatException {
        return service.getByIdAsDto(id);
    }


    @Operation(description = "Get filtered external data mappings.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = ExternalDataMappingDto.class)))})
    })
    @GetMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    @PageableAsQueryParam
    public List<ExternalDataMappingDto> get(
                    @Parameter(name = "datasource", description = "Data source of mapping")
                    @RequestParam(name = "datasource", required = false)
                    Datasource datasource,
                    @Parameter(name = "entity", description = "Entity type to get the mapping for")
                    @RequestParam(name = "entity", required = false)
                    ExternalEntity entity,
                    @Parameter(name = "externalId", description = "the external id")
                    @RequestParam(name = "externalId", required = false)
                    String externalId,
                    @Parameter(name = "internalId", description = "the internal id")
                    @RequestParam(name = "internalId", required = false)
                    String internalId,
                    @Parameter(hidden=true) Pageable pageable,
                    UriComponentsBuilder uriBuilder,
                    HttpServletRequest request,
                    HttpServletResponse response) throws FormatException {
        return getListFromPageableResult(this, uriBuilder, request, response, service.get(datasource, entity, externalId, internalId, pageable));
    }



    @Operation(description = "Get list of available data sources")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = String.class)))})
    })
    @GetMapping(value = "/datasource", produces = MediaType.APPLICATION_JSON_VALUE)
    public String[] getDatasourceValues() throws InvalidOperationException {
        return getEnumValues(Datasource.class);
    }



    @Operation(description = "Get list of available entities")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = String.class)))})
    })
    @GetMapping(value = "/entities", produces = MediaType.APPLICATION_JSON_VALUE)
    public String[] getExternalEntityValues() throws InvalidOperationException {
        return getEnumValues(ExternalEntity.class);
    }
}
