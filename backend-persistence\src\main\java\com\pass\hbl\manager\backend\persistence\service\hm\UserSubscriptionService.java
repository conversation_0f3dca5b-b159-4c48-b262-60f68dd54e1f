package com.pass.hbl.manager.backend.persistence.service.hm;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserSubscription;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserSubscriptionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Slf4j
@Service
@Transactional
public class UserSubscriptionService {

    private final HmUserSubscriptionRepository userSubscriptionRepository;
    private final UserProfileService userProfileService;

    public UserSubscriptionService(HmUserSubscriptionRepository userSubscriptionRepository, UserProfileService userProfileService) {
        this.userSubscriptionRepository = userSubscriptionRepository;
        this.userProfileService = userProfileService;
    }

    /**
     * Creates a new subscription record. This method always creates a new record
     * to maintain historical tracking of all user subscriptions.
     */
    public HmUserSubscription createSubscription(
            String userId,
            String subscriptionId,
            HmUserSubscription.SubscriptionType type,
            LocalDateTime startDate,
            LocalDateTime endDate,
            HmUserSubscription.Platform platform) throws EntityNotExistException, FormatException {

        HmUserProfile user = userProfileService.getByIdInNewTransaction(userId);

        HmUserSubscription newSubscription = new HmUserSubscription();
        newSubscription.setUser(user);
        newSubscription.setSubscriptionId(subscriptionId);
        newSubscription.setSubscriptionType(type);
        newSubscription.setStartDate(startDate);
        newSubscription.setEndDate(endDate);
        newSubscription.setPlatform(platform);
        newSubscription.setPurchaseDate(LocalDateTime.now());

        return userSubscriptionRepository.save(newSubscription);
    }

    public void updatePaymentInfo(UUID subscriptionId, BigDecimal amount, LocalDateTime paymentDate, String paymentId) {
        userSubscriptionRepository.updatePaymentInfo(subscriptionId, amount, paymentDate, paymentId);
    }

    public void updateEndDate(UUID subscriptionId, LocalDateTime endDate) {
        userSubscriptionRepository.updateEndDate(subscriptionId, endDate);
    }


}