package com.pass.hbl.manager.backend.persistence.entity.datacore;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.AbstractSportradarEntity;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrAbstractPlayerStatistics;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Objects;

@Table(name = "match_player_statistics", schema = "datacore", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class DcMatchPlayerStatistics extends SrAbstractPlayerStatistics {

    @Column(name = "player_id")
    private String playerId;

    @Column(name = "match_id")
    private String matchId;

    @Column(name = "assists")
    private Integer assists;

    @Column(name = "blocks")
    private Integer blocks;

    @Column(name = "field_goals_scored")
    private Integer fieldGoalsScored;

    @Column(name = "goalkeeper_seconds_played")
    private Integer goalKeeperSecondsPlayed;

    @Column(name = "time_played")
    private Integer timePlayed;

    @Column(name = "goals_scored")
    private Integer goalsScored;

    @Column(name = "goalkeeper_goals_against")
    private Integer goalKeeperGoalsAgainst;

    @Column(name = "red_cards")
    private Integer redCards;

    @Column(name = "goalkeeper_save_accuracy")
    private Float goalKeeperSaveAccuracy;

    @Column(name = "goalkeeper_shots_saved")
    private Integer goalKeeperShotsSaved;

    @Column(name = "seven_m_goals_scored")
    private Integer sevenMetreGoalsScored;

    @Column(name = "goalkeeper_seven_m_shots_saved")
    private Integer goalKeeperSevenMetreShotsSaved;

    @Column(name = "shooting_accuracy")
    private Float shootingAccuracy;

    @Column(name = "shots")
    private Integer shots;

    @Column(name = "goalkeeper_shots_against")
    private Integer goalKeeperShotsAgainst;

    @Column(name = "missed_shots")
    private Integer missedShots;

    @Column(name = "shots_on_goal")
    private Integer shotsOnGoal;

    @Column(name = "steals")
    private Integer steals;

    @Column(name = "suspensions")
    private Integer suspensions;

    @Column(name = "technical_faults")
    private Integer technicalFaults;

    @Column(name = "yellow_cards")
    private Integer yellowCards;


    public DcMatchPlayerStatistics(String playerId) {
        this.playerId = playerId;
    }

    @Override
    public String getMatchSrId() {
        return matchId;
    }

    @Override
    public String getPlayerSrId() {
        return playerId;
    }

    @Override
    public Integer getAssistsNumber() {
        return assists;
    }

    @Override
    public Integer getSecondsPlayed() {
        return Objects.nonNull(timePlayed) ? timePlayed * 60 : null;
    }

    @Override
    public Integer getGoalsConceded() {
        return this.goalKeeperGoalsAgainst;
    }

    @Override
    public Integer getThrowsNumber() {
        return shots;
    }

    @Override
    public Integer getGoalsThrown() {
        return goalsScored;
    }

    @Override
    public Integer getGoalsSaved() {
        return this.goalKeeperShotsSaved;
    }

    @Override
    public Float getThrowRate() {
        return shootingAccuracy;
    }

    private String stripId(AbstractSportradarEntity e) {
        return e == null || e.getId() == null ? null : Util.stripSportradarId(e.getId());
    }
}
