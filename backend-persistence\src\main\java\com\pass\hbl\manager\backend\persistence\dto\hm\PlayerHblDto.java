package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@EqualsAndHashCode
@JsonRootName("PlayerHbl")
@Data
@Schema(description = "Player market value score by round info object")
@NoArgsConstructor
public class PlayerHblDto {

    @Schema(description = "Player id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @NotBlank
    @Schema(description = "First name", example = "John", required = true)
    private String firstName;

    @NotBlank
    @Schema(description = "Last name", example = "Doe", required = true)
    private String lastName;

    @Schema(description = "An optional status if player could not play")
    private PlayerStatus status;

    @Schema(description = "Is the player active", example = "true")
    private Boolean active;

    @NotNull
    @Schema(description = "Position of the player", required = true)
    private String position;

    @Schema(description = "Optional jersey number")
    private Integer jerseyNumber;

    @Schema(description = "Current market value", required = true)
    private Integer marketValue;

    @Schema(description = "Total score of already played games in current season")
    private Integer totalScore;

    @Schema(description = "Total number of games played in current season")
    private Integer playedGames;

    @Schema(description = "Total score by round")
    private Integer roundScore;

    @Schema(description = "List of scores for each round in the current season")
    private List<ScoreDto> scores;

    @Schema(description = "HBL image ID", example = "2410211")
    private String hblImageId;

    @Schema(description = "Current club of the player", example = "Paris SG")
    private String currentClub;

    @Schema(description = "HBL Club image ID", example = "241025465411")
    public String hblClubImageId;

}
