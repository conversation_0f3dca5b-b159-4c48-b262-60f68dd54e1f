package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrSeason;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter.CompetitionConverter;
import com.sportradar.handball.v2.model.Season;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

@Component
public class SrSeasonMapper extends AbstractMapper<SrSeason, Season> {

    private final CompetitionConverter competitionConverter;

    public SrSeasonMapper(CompetitionConverter competitionConverter) {
        super(SrSeason.class, Season.class);
        this.competitionConverter = competitionConverter;
    }

    @Override
    protected void customizeInit() {
        TypeMap<Season, SrSeason> typeMap = getOrCreateTypeMap(Season.class, SrSeason.class);
        typeMap.addMappings(mapper -> mapper.using(competitionConverter).map(Season::getCompetitionId, SrSeason::setCompetition));
    }
}
