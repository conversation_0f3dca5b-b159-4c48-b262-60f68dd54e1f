package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonRootName("update user notifications request")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Update user notifications request")
public class UpdateNotificationsRequestDto {

    @NotNull
    @Schema(description = "Ids of the notifications to be updated", required = true)
    private List<String> ids;
}
