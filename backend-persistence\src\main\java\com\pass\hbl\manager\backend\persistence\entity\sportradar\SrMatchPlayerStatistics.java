package com.pass.hbl.manager.backend.persistence.entity.sportradar;

import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Table(name = "match_player_statistics", schema = "sportradar", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class SrMatchPlayerStatistics extends SrAbstractPlayerStatistics {

    @NotNull
    @ManyToOne
    @JoinColumn(name = "player_id", referencedColumnName = "id", nullable = false)
    private SrPlayer player;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "match_id", referencedColumnName = "id", nullable = false)
    private SrMatch match;

    @Column(name = "assists")
    private Integer assists;

    @Column(name = "blocks")
    private Integer blocks;

    @Column(name = "field_goals")
    private Integer fieldGoals;

    @Column(name = "goalkeeper_minutes_played")
    private String goalkeeperMinutesPlayed;

    @Column(name = "goals")
    private Integer goals;

    @Column(name = "goals_conceded")
    private Integer goalsConceded;

    @Column(name = "goals_scored")
    private Integer goalsScored;

    @Column(name = "red_cards")
    private Integer redCards;

    @Column(name = "save_accuracy")
    private Float saveAccuracy;

    @Column(name = "saves")
    private Integer saves;

    @Column(name = "seven_m_goals")
    private Integer sevenMGoals;

    @Column(name = "seven_m_saves")
    private Integer sevenMSaves;

    @Column(name = "shooting_accuracy")
    private Float shootingAccuracy;

    @Column(name = "shot_accuracy")
    private Float shotAccuracy;

    @Column(name = "shots")
    private Integer shots;

    @Column(name = "shots_against")
    private Integer shotsAgainst;

    @Column(name = "shots_off_goal")
    private Integer shotsOffGoal;

    @Column(name = "shots_on_goal")
    private Integer shotsOnGoal;

    @Column(name = "steals")
    private Integer steals;

    @Column(name = "suspensions")
    private Integer suspensions;

    @Column(name = "technical_fouls")
    private Integer technicalFouls;

    @Column(name = "yellow_cards")
    private Integer yellowCards;

    public SrMatchPlayerStatistics(SrPlayer player) {
        this.player = player;
    }

    @Override
    public String getMatchSrId() {
        return match == null ? null : match.getId();
    }

    @Override
    public String getPlayerSrId() {
        return player == null ? null : player.getId();
    }

    @Override
    public Integer getAssistsNumber() {
        return null;
    }

    @Override
    public Integer getSecondsPlayed() {
        return null;
    }

    @Override
    public Integer getThrowsNumber() {
        return null;
    }

    @Override
    public Integer getGoalsThrown() {
        return goalsScored;
    }

    @Override
    public Integer getGoalsSaved() {
        return saves;
    }

    @Override
    public Float getThrowRate() {
        return shotAccuracy;
    }

    @Override
    protected void preCreate() {
        setId("sr:ps:" + stripId(getPlayer()) + ":" + stripId(getMatch()));
    }

    private String stripId(AbstractSportradarEntity e) {
        return e == null || e.getId() == null ? null : Util.stripSportradarId(e.getId());
    }
}
