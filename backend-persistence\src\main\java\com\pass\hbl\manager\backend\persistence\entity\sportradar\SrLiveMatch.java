package com.pass.hbl.manager.backend.persistence.entity.sportradar;

import com.pass.hbl.manager.backend.persistence.dto.hm.LastGoal;
import com.pass.hbl.manager.backend.persistence.dto.hm.MatchStatus;
import com.pass.hbl.manager.backend.persistence.dto.hm.Winner;
import com.pass.hbl.manager.backend.persistence.exception.MappingException;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Table(name = "live_match", schema = "sportradar", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class SrLiveMatch extends SrAbstractMatch {

    @Column(name = "match_date")
    private LocalDateTime matchDate;

    @Column(name = "team1_id")
    private String team1Id;

    @Column(name = "team2_id")
    private String team2Id;

    @Column(name = "status")
    private Integer status;

    @Enumerated(EnumType.STRING)
    @NotNull
    @Column(name = "winner")
    private Winner winner;

    @Column(name = "team1_current_score")
    private Integer team1CurrentScore;

    @Column(name = "team2_current_score")
    private Integer team2CurrentScore;

    @Column(name = "team1_halftime_score")
    private Integer team1HalftimeScore;

    @Column(name = "team2_halftime_score")
    private Integer team2HalftimeScore;

    @Column(name = "team1_final_score")
    private Integer team1FinalScore;

    @Column(name = "team2_final_score")
    private Integer team2FinalScore;

    @Column(name = "current_period_start")
    private LocalDateTime currentPeriodStart;

    @Column(name = "time")
    private String time;

    @Column(name = "last_goal_time")
    private LocalDateTime lastGoalTime;

    @Column(name = "last_goal_match_time")
    private String lastGoalMatchTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "last_goal_team")
    private LastGoal lastGoalTeam;

    @Column(name = "venue")
    private String venue;

    private transient Integer round;

    private transient String seasonId;


    @Override
    public LocalDateTime getStartTime() {
        return matchDate;
    }

    @Override
    public String getHomeClubId() {
        return team1Id;
    }

    @Override
    public String getAwayClubId() {
        return team2Id;
    }

    @Override
    public Integer getHomeScore() {
        return team1CurrentScore;
    }

    @Override
    public Integer getAwayScore() {
        return team2CurrentScore;
    }

    @Override
    public Integer getHalfTimeHomeScore() {
        return team1HalftimeScore;
    }

    @Override
    public Integer getHalfTimeAwayScore() {
        return team2HalftimeScore;
    }

    @Override
    public MatchStatus getMatchStatus() throws MappingException {
        return MatchStatus.getByLiveCode(status);
    }

    @Override
    public String getMatchTime() {
        return time;
    }

    @Override
    public Integer getHblRound() {
        return round;
    }
}
