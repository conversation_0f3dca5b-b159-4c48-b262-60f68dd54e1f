package com.pass.hbl.manager.backend.persistence.mapper;

import com.pass.hbl.manager.backend.persistence.domain.hm.TransferMarketBidUserDo;
import com.pass.hbl.manager.backend.persistence.dto.hm.TransferMarketBidUserDto;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.util.Util;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeMap;
import org.modelmapper.config.Configuration;
import org.modelmapper.convention.MatchingStrategies;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

public abstract class AbstractMapper<Entity, DTO> {

    private final ModelMapper modelMapper;

    private final Class<Entity> entityClass;

    private final Class<DTO> dtoClass;

    public AbstractMapper(Class<Entity> entityClass, Class<DTO> dtoClass) {
        this (entityClass, dtoClass, true);
    }

    public AbstractMapper(Class<Entity> entityClass, Class<DTO> dtoClass, boolean registerConverters) {
        this.modelMapper = new ModelMapper() {

            @Override
            public <D> D map(Object source, Class<D> destinationType) {
                Object tmpSource = source;
                if(source == null){
                    tmpSource = new Object();
                }
                return super.map(tmpSource, destinationType);
            }
        };
        this.modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STANDARD)
                .setAmbiguityIgnored(true)
                .setFieldAccessLevel(Configuration.AccessLevel.PRIVATE)
                .setFieldMatchingEnabled(true);

        getModelMapper().typeMap(List.class, LinkedHashMap.class).include(HashMap.class).include(Map.class);
        getModelMapper().typeMap(Map.class, ArrayList.class).include(List.class);

        if (registerConverters) {
            registerConverters();
        }

        this.entityClass = entityClass;
        this.dtoClass = dtoClass;
    }

    protected void registerConverters() {
        registerUUIDConverters();
        registerDateConverters();
        registerPrimitiveConverters();
    }

    protected void registerPrimitiveConverters() {
        modelMapper.addConverter(new AbstractConverter<Integer, Boolean>() {
            @Override
            protected Boolean convert(Integer source) {
                return source == null ? null : source == 1;
            }
        });
        modelMapper.addConverter(new AbstractConverter<Integer, String>() {
            @Override
            protected String convert(Integer source) {
                return source == null ? null : source.toString();
            }
        });
    }

    protected void registerDateConverters() {
        modelMapper.addConverter(new AbstractConverter<LocalDate, org.threeten.bp.LocalDate>() {
            @Override
            protected org.threeten.bp.LocalDate convert(LocalDate localDate) {
                return localDate == null ? null : org.threeten.bp.LocalDate.of(localDate.getYear(), localDate.getMonthValue(),  localDate.getDayOfMonth());
            }
        });
        modelMapper.addConverter(new AbstractConverter<org.threeten.bp.LocalDate, LocalDate>() {
            @Override
            protected LocalDate convert(org.threeten.bp.LocalDate localDate) {
                return localDate == null ? null : LocalDate.of(localDate.getYear(), localDate.getMonthValue(), localDate.getDayOfMonth());
            }
        });
        modelMapper.addConverter(new AbstractConverter<ZonedDateTime, LocalDateTime>() {
            @Override
            protected LocalDateTime convert(ZonedDateTime source) {
                return Util.toLocalDateTime(source);
            }
        });
        modelMapper.addConverter(new AbstractConverter<LocalDateTime, ZonedDateTime>() {
            @Override
            protected ZonedDateTime convert(LocalDateTime source) {
                return Util.toZonedDateTime(source);
            }
        });
    }

    protected void registerUUIDConverters() {
        modelMapper.addConverter(new AbstractConverter<String, UUID>() {
            @Override
            protected UUID convert(String s) {
                try {
                    return StringUtils.isEmpty(s) ? null : Util.convertId(s);
                } catch (FormatException e) {
                    Util.sneakyThrow(e);
                    return null;
                }
            }
        });
        modelMapper.addConverter(new AbstractConverter<UUID, String>() {
            @Override
            public String convert(UUID uuid) {
                return uuid == null ? null : uuid.toString();
            }
        });
    }

    protected <Source, Dest> TypeMap<Source, Dest> getOrCreateTypeMap(@NotNull Class<Source> source, @NotNull Class<Dest> dest) {
        TypeMap<Source, Dest> typeMap = getModelMapper().getTypeMap(source, dest);
        if (typeMap != null) {
            return typeMap;
        }
        return getModelMapper().createTypeMap(source, dest);
    }

    public Entity mapToEntity(@NotNull DTO dto) {
        return mapToEntity(dto, Collections.emptyMap());
    }

    public Entity mapToEntity(@NotNull DTO dto, @NotNull Map<String, Object> context) {
        Entity entity = modelMapper.map(dto, entityClass);
        return customizeMapToEntity(dto, entity, context);
    }

    public Entity mapToEntity(@NotNull DTO dto, @NotNull Entity entity) {
        return mapToEntity(dto, entity, Collections.emptyMap());
    }

    public Entity mapToEntity(@NotNull DTO dto, @NotNull Entity entity, @NotNull Map<String, Object> context) {
        modelMapper.map(dto, entity);
        return customizeMapToEntity(dto, entity, context);
    }

    @PostConstruct
    protected void customizeInit() {
        // wait for override
    }

    protected Entity customizeMapToEntity(@SuppressWarnings("unused") @NotNull DTO dto, @NotNull Entity entity, @SuppressWarnings("unused") @NotNull Map<String, Object> context) {
        return entity;
    }

    public DTO mapToDto(@NotNull Entity entity) {
        DTO dto = mapToDto(entity, Collections.emptyMap());
        return customizeMapToDto(dto, entity);
    }


    public DTO mapToDto(@NotNull Entity entity, @NotNull Map<String, Object> context) {
        DTO dto = modelMapper.map(entity, dtoClass);
        return customizeMapToDto(dto, entity, context);
    }

    public DTO mapToDto(@NotNull Entity entity, @NotNull DTO dto) {
        modelMapper.map(entity, dto);
        return customizeMapToDto(dto, entity);
    }

    public DTO mapToDto(@NotNull Entity entity, @NotNull DTO dto, @NotNull Map<String, Object> context) {
        modelMapper.map(entity, dto);
        return customizeMapToDto(dto, entity, context);
    }

    public List<DTO> mapToDto(@NotNull Iterable<Entity> entityList) {
        return Util.toStream(entityList).map(this::mapToDto).collect(Collectors.toList());
    }

    protected DTO customizeMapToDto(@NotNull DTO dto, @SuppressWarnings("unused") @NotNull Entity entity, @SuppressWarnings("unused") @NotNull Map<String, Object> context) {
        return dto;
    }

    protected ModelMapper getModelMapper() {
        return modelMapper;
    }

    protected DTO customizeMapToDto(@NotNull DTO dto, @NotNull Entity entity) {
        return dto;
    }

    public List<DTO> mapToDto(@NotNull Iterable<Entity> entityList, Map<String, Object> context) {
        return Util.toStream(entityList)
               .map(entity -> mapToDto(entity, context))
                .collect(Collectors.toList());
    }
}
