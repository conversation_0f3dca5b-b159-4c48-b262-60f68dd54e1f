package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserLogin;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserLoginRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserProfileRepository;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

/**
 * Service for tracking user login activity
 */
@Service
@Slf4j
public class UserLoginHandler {

    private final HmUserLoginRepository userLoginRepository;
    private final TransactionHandler transactionHandler;

    public UserLoginHandler(HmUserLoginRepository userLoginRepository, TransactionHandler transactionHandler) {
        this.userLoginRepository = userLoginRepository;
        this.transactionHandler = transactionHandler;
    }

    @Transactional(readOnly = true, propagation = REQUIRES_NEW)
    public void processUserLogin(HmUserProfile user) {
        LocalDate today = LocalDate.now();
        Optional<HmUserLogin> existingLoginOpt = userLoginRepository.findByUserId(user.getId());

        if (existingLoginOpt.isPresent()) {
            HmUserLogin existingLogin = existingLoginOpt.get();
            // Only update if this is the first login of the day
            if (!today.equals(existingLogin.getLastLogin())) {
                log.info("Updating last login date for user ID: {} from {} to {}",
                        user, existingLogin.getLastLogin(), today);
                // Update existing record with today's date
                transactionHandler.runInNewTransaction(() -> {
                    userLoginRepository.updateLastLogin(today, user.getId());
                    return null;
                });
            } else {
                log.info("User ID: {} already logged in today, not updating last login date", user);
            }
        } else {
            transactionHandler.runInNewTransaction(() -> {
                // Create new record with today's date
                log.info("Creating new login record for user ID: {}", user.getId());
                HmUserLogin userLogin = new HmUserLogin(user, today);
                return userLoginRepository.save(userLogin);
            });
        }
    }

    /**
     * @param userId User ID
     * @return Last login date or null if no record exists
     */
    @Transactional(readOnly = true, propagation = REQUIRES_NEW)
    public LocalDate getLastLogin(UUID userId) {
        Optional<HmUserLogin> userLoginOpt = userLoginRepository.findByUserId(userId);
        return userLoginOpt.map(HmUserLogin::getLastLogin).orElse(null);
    }
}
