package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrSeason;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.repository.sportradar.SrSeasonRepository;
import com.sportradar.handball.v2.model.Season;
import lombok.SneakyThrows;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

@Component
public class SeasonConverter extends AbstractConverter<Season, SrSeason> {

    private final SrSeasonRepository repository;

    public SeasonConverter(SrSeasonRepository repository) {
        this.repository = repository;
    }

    @SneakyThrows
    @Override
    protected SrSeason convert(Season source) {
        return source == null ? null : repository.findById(source.getId())
                .orElseThrow(() -> new EntityNotExistException(SrSeason.class, source.getId()));
    }
}
