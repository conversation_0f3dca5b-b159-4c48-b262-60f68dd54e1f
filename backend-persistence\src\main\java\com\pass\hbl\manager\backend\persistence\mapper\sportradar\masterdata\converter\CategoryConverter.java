package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrCategory;
import com.pass.hbl.manager.backend.persistence.repository.sportradar.SrCategoryRepository;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

@Component
public class CategoryConverter extends AbstractConverter<String, SrCategory> {

    private final SrCategoryRepository repository;

    public CategoryConverter(SrCategoryRepository repository) {
        this.repository = repository;
    }

    @Override
    protected SrCategory convert(String source) {
        return StringUtils.isEmpty(source) ? null : repository.findById(source).orElse(null);
    }
}
