package com.pass.hbl.manager.backend.persistence.entity.datacore;

import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.AbstractSportradarEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Table(name = "player", schema = "datacore", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class DcPlayer extends AbstractSportradarEntity {

    @NotNull
    @Column(name = "first_name")
    private String firstName;

    @NotNull
    @Column(name = "last_name")
    private String lastName;

    @Column(name = "hbl_image_id")
    private String hblImageId;

    @Column(name = "position")
    private String position;

    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;

    @Column(name = "nationality")
    private String nationality;

    @Column(name = "height")
    private Integer height;

    @Column(name = "weight")
    private Integer weight;

    @Column(name = "jersey_number")
    private Integer jerseyNumber;

    @Column(name = "gender")
    private String gender;

    @ManyToOne
    @JoinColumn(name = "competitor_id")
    private DcCompetitor competitor;

    @ManyToOne
    @JoinColumn(name = "season_id")
    private DcSeason season;

    @Override
    public ExternalEntity getExternalEntity() {
        return ExternalEntity.PLAYER;
    }
}
