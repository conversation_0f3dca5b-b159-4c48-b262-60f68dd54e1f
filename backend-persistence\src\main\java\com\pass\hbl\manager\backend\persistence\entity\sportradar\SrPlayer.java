package com.pass.hbl.manager.backend.persistence.entity.sportradar;

import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Table(name = "player", schema = "sportradar", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class SrPlayer extends AbstractSportradarEntity {

    @NotNull
    @Column(name = "first_name")
    private String firstName;

    @NotNull
    @Column(name = "last_name")
    private String lastName;

    @Column(name = "type")
    private String type;

    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;

    @Column(name = "nationality")
    private String nationality;

    @Column(name = "country_code")
    private String countryCode;

    @Column(name = "height")
    private Integer height;

    @Column(name = "weight")
    private Integer weight;

    @Column(name = "jersey_number")
    private Integer jerseyNumber;

    @Column(name = "gender")
    private String gender;

    @ManyToOne
    @JoinColumn(name = "competitor_id")
    private SrCompetitor competitor;

    @ManyToOne
    @JoinColumn(name = "season_id")
    private SrSeason season;


    public String getHblImageId() {
        return getId() == null ? null : getId().toLowerCase().replaceAll("[a-z:]*:", "");
    }

    @Override
    public ExternalEntity getExternalEntity() {
        return ExternalEntity.PLAYER;
    }
}
