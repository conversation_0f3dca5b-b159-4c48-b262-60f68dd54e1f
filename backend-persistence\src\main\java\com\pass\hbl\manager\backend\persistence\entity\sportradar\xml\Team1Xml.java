package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "Team1")
@NoArgsConstructor
@Data
public class Team1Xml {

    @JacksonXmlProperty(localName = "BetradarTeamId", isAttribute = true)
    private Integer betradarTeamId;

    @JacksonXmlProperty(localName = "UniqueTeamId", isAttribute = true)
    private Integer uniqueTeamId;

    @JacksonXmlProperty(localName = "Name")
    private List<NameXml> names;
}
