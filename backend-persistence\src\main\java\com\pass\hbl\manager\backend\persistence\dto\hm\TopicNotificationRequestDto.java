package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.List;

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonRootName("Test Notification Topics Request")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Test Notification Topics Request")
public class TopicNotificationRequestDto {
    @Schema(description = "The title of the notification", example = "Notification Title")
    private String title;

    @Schema(description = "The message to be sent in the notification", example = "This is a data message")
    private String dataMessage;

    @Schema(description = "List of topics to which the notifications will be sent")
    private List<NotificationTopic> topics;

    @Schema(description = "The language of the notification", example = "en")
    private String language;

    @Schema(description = "Flag to indicate if saving is enabled", example = "true")
    private boolean saveEnable;
}