package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.util.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@JsonRootName("UserAdminUi")
@Getter
@Setter
@Schema(description = "Reduced user info object for listing users with basic attributes")
public class UserAdminUiDto {

    @Schema(description = "User id is registered with", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "Username user is registered with", example = "john-doe", required = true)
    @NotBlank
    @Size(max = 256)
    private String username;

    @Schema(description = "Mail address user is registered with", example = "<EMAIL>", required = true)
    @Email(message = "Email address")
    @NotBlank
    @Size(max = 256)
    private String emailAddress;

    @JsonCreator
    public UserAdminUiDto() {
    }

    @JsonCreator
    public UserAdminUiDto(String username, String emailAddress) {
        this.username = username;
        this.emailAddress = emailAddress;
    }


    public UserAdminUiDto(String id, String username, String emailAddress) {
        this.id = id;
        this.username = username;
        this.emailAddress = emailAddress;
    }

    public static UserAdminUiDto getAnonymous() {
        return new UserAdminUiDto(Constants.ANONYMOUS_ID, Constants.ANONYMOUS_USERNAME,
                Constants.ANONYMOUS_EMAIL);
    }
}