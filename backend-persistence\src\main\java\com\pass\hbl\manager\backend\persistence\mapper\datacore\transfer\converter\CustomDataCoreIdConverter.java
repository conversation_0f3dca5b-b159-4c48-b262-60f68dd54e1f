package com.pass.hbl.manager.backend.persistence.mapper.datacore.transfer.converter;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminExternalDataMapping;
import com.pass.hbl.manager.backend.persistence.exception.TechnicalException;
import com.pass.hbl.manager.backend.persistence.repository.admin.AdminExternalDataMappingRepository;
import com.pass.hbl.manager.backend.persistence.service.datacore.streaming.DataCoreMappingService;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;


import java.io.IOException;
import java.util.Objects;
import java.util.UUID;

import static java.util.Objects.nonNull;

@NoArgsConstructor(force = true)
@Component
public class CustomDataCoreIdConverter extends AbstractConverter<String, UUID> {

    private final AdminExternalDataMappingRepository repository;
    private final DataCoreMappingService dataCoreMappingService;
    private final ExternalEntity externalEntity;

    public CustomDataCoreIdConverter(AdminExternalDataMappingRepository repository, DataCoreMappingService dataCoreMappingService, ExternalEntity externalEntity) {
        this.repository = repository;
        this.dataCoreMappingService = dataCoreMappingService;
        this.externalEntity = externalEntity;
    }

    @Override
    protected UUID convert(String source) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        UUID sourceId = repository.findFirstByDatasourceAndSourceId(Datasource.DATACORE, source).map(AdminExternalDataMapping::getHmId).orElse(null);
        if (Objects.isNull(sourceId)) {
            try {
                String srId = null;
                if (externalEntity.equals(ExternalEntity.COMPETITOR)) {
                    srId = dataCoreMappingService.getSrIdByDcId(ExternalEntity.COMPETITOR, source);

                } else if (externalEntity.equals(ExternalEntity.PLAYER)) {

                    srId = dataCoreMappingService.getSrIdByDcId(ExternalEntity.PLAYER, source);
                }
                if (nonNull(srId)) {
                    sourceId = repository.findFirstByDatasourceAndSourceId(Datasource.SPORTRADAR, srId).map(AdminExternalDataMapping::getHmId).orElse(null);
                }
            } catch (TechnicalException | IOException e) {
                throw new RuntimeException(e);
            }
        }
        return sourceId;
    }

}
