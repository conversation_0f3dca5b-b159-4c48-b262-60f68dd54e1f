package com.pass.hbl.manager.backend.persistence.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.TOO_MANY_REQUESTS)
@Getter
public class RateLimitExceededException extends RuntimeException {

    private final long rateLimitResetEpoch;

    public RateLimitExceededException(String message) {
        super(message);
        this.rateLimitResetEpoch = 0;
    }

    public RateLimitExceededException(String message, long rateLimitResetEpoch) {
        super(message);
        this.rateLimitResetEpoch = rateLimitResetEpoch;
    }
}