package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerHistoryDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.PlayerStatisticsAggregator;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class HmPlayerHistoryMapper extends AbstractMapper<HmPlayer, PlayerHistoryDto> {

    private final PlayerStatisticsAggregator playerStatisticsAggregator;

    public HmPlayerHistoryMapper(PlayerStatisticsAggregator playerStatisticsAggregator) {
        super(HmPlayer.class, PlayerHistoryDto.class);
        this.playerStatisticsAggregator = playerStatisticsAggregator;
    }

    @Override
    protected PlayerHistoryDto customizeMapToDto(PlayerHistoryDto playerDto, HmPlayer hmPlayer, Map<String, Object> context) {
        playerDto.setTotalScore(playerStatisticsAggregator.getTotalScorePlayedRounds(hmPlayer.getId()));
        return super.customizeMapToDto(playerDto, hmPlayer, context);
    }
}
