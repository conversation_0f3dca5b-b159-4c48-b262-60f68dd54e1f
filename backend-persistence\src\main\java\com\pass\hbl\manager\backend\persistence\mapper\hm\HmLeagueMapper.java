package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.LeagueDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.LeagueInfoDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.HmLeagueMembershipToUserDtoConverter;
import com.pass.hbl.manager.backend.persistence.service.hm.AwardService;
import com.pass.hbl.manager.backend.persistence.service.hm.helpers.LeagueHelper;
import com.pass.hbl.manager.backend.persistence.util.Constants;
import org.modelmapper.TypeMap;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
public class HmLeagueMapper extends AbstractMapper<HmLeague, LeagueDto> {

    private final HmUserMapper hmUserMapper;

    private final LeagueHelper leagueHelper;

    private final AwardService awardService;

    public HmLeagueMapper(HmUserMapper hmUserMapper, LeagueHelper leagueHelper, @Lazy AwardService awardService) {
        super(HmLeague.class, LeagueDto.class);
        this.hmUserMapper = hmUserMapper;
        this.leagueHelper = leagueHelper;
        this.awardService = awardService;
    }

    @Override
    protected void customizeInit() {
        getModelMapper().addConverter(new HmLeagueMembershipToUserDtoConverter(hmUserMapper));
        TypeMap<HmLeague, LeagueInfoDto> e2d = getOrCreateTypeMap(HmLeague.class, LeagueInfoDto.class);
        e2d.addMappings(mapper -> mapper.skip(LeagueInfoDto::setOwnerId));
    }

    @Override
    protected LeagueDto customizeMapToDto(LeagueDto leagueDto, HmLeague hmLeague) {

        try {
            leagueDto.setOwnerId(hmLeague.getOwner().getId().toString());
        } catch (Exception e) {
            // happens if Owner is deleted
            leagueDto.setOwnerId(Constants.ANONYMOUS_USERNAME);
        }
        leagueDto.setMemberCount(leagueHelper.getLeagueSizeExcludingOnHoldMembers(hmLeague));
        leagueDto.setLeagueAwards(awardService.getLeagueAwards(hmLeague.getId()));
        return leagueDto;
    }
}
