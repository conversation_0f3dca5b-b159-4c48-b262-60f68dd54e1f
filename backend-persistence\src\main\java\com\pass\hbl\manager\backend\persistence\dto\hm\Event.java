package com.pass.hbl.manager.backend.persistence.dto.hm;

import lombok.Getter;

public enum Event {
    TIME_PENALTY(false),
    RED_CARD(false),
    PENALTY_CAUSED(false),
    BLOCK(false),
    STEAL(false),
    TECHNICAL_FAULT(false),
    SIX_M_MIDDLE(true),
    SIX_M_LEFT_RIGHT(true),
    SEVEN_M(true),
    NINE_M_MIDDLE(true),
    NINE_M_LEFT_RIGHT(true),
    BACKCOURT(true),
    DIRECT_FREE_THROW(true),
    FAST_BREAK(true),
    WING(true),
    <PERSON>K<PERSON>OWN(true);

    @Getter
    private final boolean characteristic;

    Event(boolean characteristic) {
        this.characteristic = characteristic;
    }
}
