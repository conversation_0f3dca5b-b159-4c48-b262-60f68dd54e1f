package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;

@JsonRootName("Season")
@Getter
@Setter
@Schema(description = "Reduced season info object for listing season with name attribute")
public class SeasonDto extends AbstractDto<SeasonDto, String> {

    @Schema(description = "Season id is registered with", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "name season is registered with", example = "john-doe", required = true)
    @NotBlank
    @Size(max = 16)
    private String name;

    @Schema(description = "start date of the season", example = "2021-09-08", required = true)
    @NotNull
    private LocalDate startDate;

    @Schema(description = "end date of the season", example = "2022-06-12", required = true)
    @NotNull
    private LocalDate endDate;

    @Schema(description = "year of the season", example = "21/22", required = true)
    @NotNull
    private String year;

    @Schema(description = "started rounds of the season including finished rounds and current round")
    List<RoundDto> startedRounds;


    @JsonCreator
    public SeasonDto() {
    }

    @JsonCreator
    public SeasonDto(String name) {
        this.name = name;
    }
}
