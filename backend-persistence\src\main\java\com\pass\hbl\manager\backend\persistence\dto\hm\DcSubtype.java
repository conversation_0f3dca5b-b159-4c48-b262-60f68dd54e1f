package com.pass.hbl.manager.backend.persistence.dto.hm;

public enum DcSubtype {
    BACKCOURT("backCourt"),
    BALL_FAULT("ballFault"),
    CAUSED("caused"),
    DEFENCE("defence"),
    NINE_METRE("nineMetre"),
    RULE_FAULT("ruleFault"),
    SHOT("shot"),
    SIX_METRE("sixMetre"),
    WING("wing");

    private final String value;

    DcSubtype(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
