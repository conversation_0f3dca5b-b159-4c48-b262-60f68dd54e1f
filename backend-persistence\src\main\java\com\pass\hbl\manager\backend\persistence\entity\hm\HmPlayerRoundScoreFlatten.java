package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.UUID;

@Table(name = "v_player_round_score", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Immutable
@Where(clause = "deleted=false")
public class HmPlayerRoundScoreFlatten extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @Column(name = "player_id")
    private UUID playerId;

    @Column(name = "round_id")
    private UUID roundId;

    @Column(name = "match_id")
    private UUID matchId;

    @Column(name = "score")
    private Integer score;

}
