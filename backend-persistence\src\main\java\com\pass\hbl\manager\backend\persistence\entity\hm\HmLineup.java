package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Table(name = "lineup", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@Entity
@ToString
@SQLDelete(sql = "UPDATE hm.lineup SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmLineup extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @ToString.Exclude
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "team_id", referencedColumnName = "id", updatable = false)
    private HmTeam team;

    @NotNull
    @ToString.Exclude
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "round_id", referencedColumnName = "id", updatable = false)
    private HmRound round;

    public HmLineup(HmTeam team, HmRound round) {
        this.team = team;
        this.round = round;
    }
}
