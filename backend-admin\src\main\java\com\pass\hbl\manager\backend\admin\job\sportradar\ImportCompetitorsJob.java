package com.pass.hbl.manager.backend.admin.job.sportradar;

import com.pass.hbl.manager.backend.persistence.job.ScheduledJob;
import com.pass.hbl.manager.backend.persistence.job.admin.AdminAbstractJob;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.datacore.masterdata.ImportCompetitorsDataCoreService;
import com.pass.hbl.manager.backend.persistence.service.sportradar.masterdata.ImportCompetitorsService;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

import static com.pass.hbl.manager.backend.admin.job.sportradar.ImportSeasonJob.PARAM_DATACORE;

@Component
@Slf4j
@ScheduledJob(description = "Import clubs from Sportradar", parameters = ImportCompetitorsJob.PARAM_CURRENT_ONLY + "=true")
public class ImportCompetitorsJob extends AdminAbstractJob {

    public static final String PARAM_CURRENT_ONLY = "currentOnly";
    public static final String PARAM_TRANSFER_ONLY = "transferOnly";
    private final Environment environment;
    private final ImportCompetitorsService importCompetitorsService;
    private final ImportCompetitorsDataCoreService importCompetitorsDataCoreService;

    private boolean isDataCoreApi;

    public ImportCompetitorsJob(ParameterService parameterService, Environment environment, ImportCompetitorsService importCompetitorsService, ImportCompetitorsDataCoreService importCompetitorsDataCoreService) {
        super(parameterService);
        this.environment = environment;
        this.importCompetitorsService = importCompetitorsService;
        this.importCompetitorsDataCoreService = importCompetitorsDataCoreService;

    }

    @Override
    protected void work() throws Exception {
        if (isDataCoreApi) {
            log.info("Sportradar data imported from the DataCore Rest-Api");
            importCompetitorsDataCoreService.start(getParameterAsBoolean(PARAM_CURRENT_ONLY), getParameterAsBoolean(PARAM_TRANSFER_ONLY));
        } else {
            importCompetitorsService.start(getParameterAsBoolean(PARAM_CURRENT_ONLY), getParameterAsBoolean(PARAM_TRANSFER_ONLY));
        }
    }

    @Override
    protected void init() throws Exception {
        this.isDataCoreApi = getParameterAsBoolean(PARAM_DATACORE);
        List<String> activeProfiles = Arrays.asList(environment.getActiveProfiles());
        if (isDataCoreApi) {
            importCompetitorsDataCoreService.initDataCoreDefaults(activeProfiles);
        } else {
            importCompetitorsService.initSportradarDefaults();
        }
    }

    @Override
    protected void tearDown() {

    }

    @Override
    protected void terminate() throws Exception {
        importCompetitorsService.cancel();
    }
}
