package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.dto.admin.ParameterDto;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping(ApiConstants.PARAMETER_API)
@Validated
@Tag(name = "parameter")
@RolesAllowed({ApiConstants.ROLE_ADMIN})
public class ParameterController {

    private final ParameterService service;

    public ParameterController(ParameterService service) {
        this.service = service;
    }

    @Operation(description = "List all parameters")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = ParameterDto.class)))})
    })
    @GetMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<ParameterDto> getAllParameters() {
        return service.getAllAsDto();
    }



    @Operation(description = "Get parameter by name. If parameter does not exist it will be created. The value then will be either the default value or the computed value. In case the value would be empty, an exception is triggered.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = ParameterDto.class)))})
    })
    @GetMapping(value = "/{name}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ParameterDto getParameter(
                    Authentication authentication,
                    @Parameter(name = "name", description = "Parameter name", required = true)
                    @PathVariable(name = "name")
                    @NotNull String name,
                    @Parameter(name = "defaultValue", description = "the default value if parameter does not exist")
                    @RequestParam(name = "defaultValue", required = false, defaultValue = "null")
                    String defaultValue) throws InvalidOperationException {
        return service.getParam(name, defaultValue, authentication.getName());
    }



    @Operation(description = "Get parameter value by name. If parameter does not exist it will be created. The value then will be either the default value or the computed value. In case the value would be empty, an exception is triggered.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.TEXT_PLAIN_VALUE, array = @ArraySchema(schema = @Schema(implementation = String.class)))})
    })
    @GetMapping(value = "/{name}/value", produces = MediaType.APPLICATION_JSON_VALUE)
    public String getParameterValue(
            Authentication authentication,
            @Parameter(name = "name", description = "Parameter name", required = true)
            @PathVariable(name = "name")
            @NotNull String name,
            @Parameter(name = "defaultValue", description = "the default value if parameter does not exist")
            @RequestParam(name = "defaultValue", required = false, defaultValue = "null")
                    String defaultValue) throws InvalidOperationException {
        return service.getValue(name, defaultValue, authentication.getName());
    }



    @Operation(description = "Get parameter value as Integer by name. If parameter does not exist it will be created. The value then will be either the default value or the computed value. In case the value would be empty, an exception is triggered.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.TEXT_PLAIN_VALUE, array = @ArraySchema(schema = @Schema(implementation = String.class)))})
    })
    @GetMapping(value = "/{name}/valueAsInt", produces = MediaType.APPLICATION_JSON_VALUE)
    public int getParameterValueAsInteger(
            Authentication authentication,
            @Parameter(name = "name", description = "Parameter name", required = true)
            @PathVariable(name = "name")
            @NotNull String name,
            @Parameter(name = "defaultValue", description = "the default value if parameter does not exist")
            @RequestParam(name = "defaultValue", required = false, defaultValue = "-1")
                    int defaultValue) throws InvalidOperationException, FormatException {
        return service.getAsInteger(name, defaultValue, authentication.getName());
    }



    @Operation(description = "Set parameter. If the parameter does not exist, it will be created")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = ParameterDto.class)))})
    })
    @PutMapping(value = "/{name}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ParameterDto set(
            @Parameter(name = "name", description = "Parameter name", required = true)
            @PathVariable(name = "name")
            @NotNull String name,
            @Parameter(name = "value", description = "Parameter value", required = true)
            @RequestParam(name = "value")
            @NotNull String value) {
        return service.set(name, value);
    }



    @Operation(description = "Delete parameter.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok")
    })
    @DeleteMapping(value = "/{name}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void delete(
            @Parameter(name = "name", description = "Parameter name", required = true)
            @PathVariable(name = "name")
            @NotNull String name) {
        service.delete(name);
    }
}
