package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.AwardCode;
import com.pass.hbl.manager.backend.persistence.dto.hm.LeagueInfo;
import com.pass.hbl.manager.backend.persistence.entity.AbstractPictureEntity;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Table(name = "league", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@Entity
@ToString
@SQLDelete(sql = "UPDATE hm.league SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmLeague extends AbstractPictureEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotBlank
    @Size(max = 256)
    @Column(name = "name")
    private String name;

    @Min(0)
    @ToString.Exclude
    @Column(name = "max_size")
    private int maxSize;

    @ToString.Exclude
    @Column(name = "description")
    private String description;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "season_id", referencedColumnName = "id", updatable = false)
    private HmSeason season;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "owner_id", referencedColumnName = "id")
    private HmUserProfile owner;

    @NotNull
    @ToString.Exclude
    @Column(name = "public_access")
    private Boolean publicAccess;

    @ToString.Exclude
    @OneToMany(mappedBy = "league", fetch = FetchType.LAZY)
    List<HmLeagueInvitation> leagueInvitations;

    @ToString.Exclude
    @OneToMany(mappedBy = "league", fetch = FetchType.LAZY)
    List<HmLeagueMembership> leagueMemberships;

    @ToString.Exclude
    @OneToMany(mappedBy = "league", fetch = FetchType.LAZY)
    List<HmTeam> teams;

    @ToString.Exclude
    @OneToMany(mappedBy = "league", fetch = FetchType.LAZY)
    List<HmTransferMarket> transferMarketsAppearances;

    @ToString.Exclude
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "previous_league_id", referencedColumnName = "id")
    private HmLeague previousLeague;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "league_id", referencedColumnName = "id")
    private List<HmUserAward> assignedAwards;

    @ToString.Exclude
    @Column(name = "active")
    private boolean isActive;

    @ToString.Exclude
    @Column(name = "inactive_since")
    private LocalDateTime inactiveSince;

    public HmLeague(String name, HmSeason season, HmUserProfile owner, Boolean publicAccess, HmLeague previousLeague) {
        this.name = name;
        this.season = season;
        this.owner = owner;
        this.publicAccess = publicAccess;
        this.previousLeague = previousLeague;
        this.isActive = true;
        this.inactiveSince = null;
    }

    public HmLeague(String name, HmSeason season, HmUserProfile owner, Boolean publicAccess) {
        this(name, season, owner, publicAccess, null);
    }

    public HmLeague(String name, String description, HmSeason season, HmUserProfile owner, Boolean publicAccess, HmLeague previousLeague) {
        this.name = name;
        this.description = description;
        this.season = season;
        this.owner = owner;
        this.publicAccess = publicAccess;
        this.previousLeague = previousLeague;
        this.isActive = true;
        this.inactiveSince = null;
    }

    public List<HmTeam> getTeams() {
        return isNull(teams)? null : teams.stream().filter(t -> isNull(t.getLeft())).toList();
    }

    public List<HmLeagueMembership> getMembers() {
        return Util.toStream(this.getLeagueMemberships()).filter(lm -> nonNull(lm.getJoined())).collect(Collectors.toList());
    }

    public List<HmLeagueMembership> getApplicants() {
        return Util.toStream(this.getLeagueMemberships()).filter(lm -> Objects.isNull(lm.getJoined())).collect(Collectors.toList());
    }

    public List<HmLeagueMembership> getJoinedMembers() {
        return Util.toStream(getMembers()).filter(lm -> nonNull(lm.getJoined())).collect(Collectors.toList());
    }

    public Map<LeagueInfo, String> getLeagueInfo() {
        Map<LeagueInfo, String> leagueInfoMap = new HashMap<>();
        if (nonNull(previousLeague)) {
            leagueInfoMap.put(LeagueInfo.PREVIOUS_LEAGUE_ID, previousLeague.getId().toString());
            List<HmUserAward> assignedAwardsPreviousLeague = previousLeague.getAssignedAwards();
            if (nonNull(assignedAwardsPreviousLeague)) {
                assignedAwardsPreviousLeague.stream().filter(userAward ->
                        userAward.getAward().getCode().name().equals(AwardCode.LEAGUE_WINNER.name())).findFirst()
                        .ifPresent(hmUserAward -> leagueInfoMap.put(LeagueInfo.PREVIOUS_LEAGUE_WINNER_ID,
                        hmUserAward.getUserProfile().getId().toString()));
            }
        }
        return leagueInfoMap;
    }

    public int getSize() {
        return getJoinedMembers().size();
    }
}
