package com.pass.hbl.manager.backend.persistence.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

@Configuration
@ComponentScan(basePackages="com.pass.hbl.manager.backend")
public class ThreadPoolTaskSchedulerConfig {

    @Bean(name = "HBLThreadPoolScheduler")
    public ThreadPoolTaskScheduler threadPoolTaskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setThreadNamePrefix("HBL-Scheduler");
        scheduler.setPoolSize(Math.max(Runtime.getRuntime().availableProcessors() - 4, 1));
        return scheduler;
    }
}
