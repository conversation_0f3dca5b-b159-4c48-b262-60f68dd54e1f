package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;

import java.util.Arrays;
import java.util.Objects;

public enum SessionAttribute {
    CURRENT_LEAGUE;

    @JsonCreator
    public static SessionAttribute getFromString(String s) {
        return Arrays.stream(values()).filter(sa -> Objects.equals(sa.toString(), s)).findFirst().orElse(null);
    }
}
