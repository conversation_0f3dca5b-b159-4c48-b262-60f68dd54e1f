package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.time.ZonedDateTime;

@JsonRootName("Player market value statistic")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Player market value statistic by unit used for player market value history")
public class PlayerMarketValueStatisticDto {

    @Schema(description = "unit of the statistic", example = "week", required = true)
    private StatisticsUnit unit;

    @Schema(description = "average market value by the given statistics unit", required = true)
    private Double marketValue;

    @Schema(description = "start date of the round", example = "2021-09-08T00:00:00", required = true)
    @NotNull
    private ZonedDateTime from;

    @Schema(description = "end date of the round", example = "2022-06-12T23:59:59", required = true)
    @NotNull
    private ZonedDateTime to;

    @Schema(description = "statistic display label", example = "KW1", required = true)
    @NotNull
    private String unitLabel;
}
