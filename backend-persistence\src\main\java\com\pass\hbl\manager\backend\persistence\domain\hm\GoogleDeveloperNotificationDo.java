package com.pass.hbl.manager.backend.persistence.domain.hm;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoogleDeveloperNotificationDo {

    @Schema(description = "The version of this notification. Initially, this is 1.0", example = "1.0")
    private String version;

    @Schema(description = "The package name of the application that this notification relates to", example = "com.pass.handball")
    private String packageName;

    @Schema(description = "The timestamp when the event occurred, in milliseconds since the Epoch")
    private Long eventTimeMillis;

    //TODO hbs remove
    @Schema(description = "If this field is present, then this notification is related to a subscription, and this field contains additional information related to the subscription")
    private String oneTimeProductNotification;

    @Schema(description = "If this field is present, then this notification is related to a one-time purchase, and this field contains additional information related to the purchase")
    private GoogleSubscriptionNotificationDo subscriptionNotification;

    @Schema(description = "If this field is present, then this notification is related to a test publish. These are sent only through the Google Play Developer Console")
    private String testNotification;

}
