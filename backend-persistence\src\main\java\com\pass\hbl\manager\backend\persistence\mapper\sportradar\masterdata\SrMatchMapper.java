package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrMatch;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter.*;
import com.sportradar.handball.v2.model.Summary;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Map;

@Component
public class SrMatchMapper extends AbstractMapper<SrMatch, Summary> {
    private final SportEventSeasonConverter seasonConverter;
    private final HomeCompetitorConverter homeCompetitorConverter;
    private final AwayCompetitorConverter awayCompetitorConverter;
    private final HomeHalfTimeScoreConverter homeHalfTimeScoreConverter;
    private final AwayHalfTimeScoreConverter awayHalfTimeScoreConverter;
    private final SportEventRoundConverter roundConverter;
    private final SportEventLiveConverter liveConverter;
    private final PlayerStatisticsConverter statisticsConverter;
    private final OdtToLdtConverter odtToLdtConverter;
    private final SportEventStatusConverter statusConverter;
    private final SportEventMatchStatusConverter matchStatusConverter;
    private final WinnerConverter winnerConverter;

    public SrMatchMapper(SportEventSeasonConverter seasonConverter,
                         HomeCompetitorConverter homeCompetitorConverter,
                         AwayCompetitorConverter awayCompetitorConverter,
                         HomeHalfTimeScoreConverter homeHalfTimeScoreConverter,
                         AwayHalfTimeScoreConverter awayHalfTimeScoreConverter,
                         SportEventRoundConverter roundConverter,
                         SportEventLiveConverter liveConverter,
                         PlayerStatisticsConverter statisticsConverter,
                         OdtToLdtConverter odtToLdtConverter,
                         SportEventStatusConverter statusConverter,
                         SportEventMatchStatusConverter matchStatusConverter,
                         WinnerConverter winnerConverter) {
        super(SrMatch.class, Summary.class);
        this.seasonConverter = seasonConverter;
        this.homeCompetitorConverter = homeCompetitorConverter;
        this.awayCompetitorConverter = awayCompetitorConverter;
        this.homeHalfTimeScoreConverter = homeHalfTimeScoreConverter;
        this.awayHalfTimeScoreConverter = awayHalfTimeScoreConverter;
        this.roundConverter = roundConverter;
        this.liveConverter = liveConverter;
        this.statisticsConverter = statisticsConverter;
        this.odtToLdtConverter = odtToLdtConverter;
        this.statusConverter = statusConverter;
        this.matchStatusConverter = matchStatusConverter;
        this.winnerConverter = winnerConverter;
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected void customizeInit() {
        TypeMap<Summary, SrMatch> typeMap = getOrCreateTypeMap(Summary.class, SrMatch.class);
        typeMap.addMappings(mapper -> mapper.map(source -> source.getSportEvent().getId(), SrMatch::setId));
        typeMap.addMappings(mapper -> mapper.using(odtToLdtConverter).map(source -> source.getSportEvent().getStartTime(), SrMatch::setStartTime));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getSportEvent().getStartTimeConfirmed(), SrMatch::setStartTimeConfirmed));
        typeMap.addMappings(mapper -> mapper.using(seasonConverter).map(Summary::getSportEvent, SrMatch::setSeason));
        typeMap.addMappings(mapper -> mapper.using(roundConverter).map(Summary::getSportEvent, SrMatch::setRound));
        typeMap.addMappings(mapper -> mapper.using(liveConverter).map(Summary::getSportEvent, SrMatch::setLive));
        typeMap.addMappings(mapper -> mapper.using(homeCompetitorConverter).map(Summary::getSportEvent, SrMatch::setHomeCompetitor));
        typeMap.addMappings(mapper -> mapper.using(awayCompetitorConverter).map(Summary::getSportEvent, SrMatch::setAwayCompetitor));
        typeMap.addMappings(mapper -> mapper.using(statusConverter).map(source -> source.getSportEventStatus().getStatus(), SrMatch::setStatus));
        typeMap.addMappings(mapper -> mapper.using(matchStatusConverter).map(source -> source.getSportEventStatus().getMatchStatus(), SrMatch::setMatchStatus));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getSportEventStatus().getHomeScore(), SrMatch::setHomeScore));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getSportEventStatus().getAwayScore(), SrMatch::setAwayScore));
        typeMap.addMappings(mapper -> mapper.using(homeHalfTimeScoreConverter).map(Summary::getSportEventStatus, SrMatch::setHomeHalfTimeScore));
        typeMap.addMappings(mapper -> mapper.using(awayHalfTimeScoreConverter).map(Summary::getSportEventStatus, SrMatch::setAwayHalfTimeScore));
        typeMap.addMappings(mapper -> mapper.using(winnerConverter).map(source -> source, SrMatch::setWinner));
        typeMap.addMappings(mapper -> mapper.using(statisticsConverter).map(Summary::getStatistics, SrMatch::setPlayerStatistics));
    }

    @Override
    protected SrMatch customizeMapToEntity(Summary summary, SrMatch srMatch, Map<String, Object> context) {
        if (srMatch != null && !CollectionUtils.isEmpty(srMatch.getPlayerStatistics())) {
            srMatch.getPlayerStatistics().forEach(s -> s.setMatch(srMatch));
        }
        return srMatch;
    }
}
