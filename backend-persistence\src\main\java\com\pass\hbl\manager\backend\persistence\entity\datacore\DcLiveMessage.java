package com.pass.hbl.manager.backend.persistence.entity.datacore;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.LiveMessageStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.UUID;

@Table(name = "live_message", schema = "datacore", catalog = "handball_manager")
@Getter
@Setter
@ToString
@Entity
public class DcLiveMessage {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id", nullable = false)
    private UUID id;

    @NotNull
    @Column(name = "received_at", nullable = false)
    private LocalDateTime receivedAt;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private LiveMessageStatus status;

    @Column(name = "message", nullable = false)
    private String message;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "generated_at")
    private LocalDateTime generatedAt;

    @Column(name = "modified_at")
    private LocalDateTime modifiedAt;

    @Column(name = "type")
    private String type;

    public DcLiveMessage(String message) {
        this.receivedAt = LocalDateTime.now();
        this.message = message;
    }

    public DcLiveMessage() {
        this.receivedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onPersist() {
            this.modifiedAt = LocalDateTime.now();
    }

    @PrePersist
    protected void onCreate() {
        this.modifiedAt = LocalDateTime.now();
    }

    @Override
    @SuppressWarnings({"EqualsWhichDoesntCheckParameterClass", "EqualsDoesntCheckParameterClass", "com.haulmont.jpb.EqualsDoesntCheckParameterClass"})
    public boolean equals(Object o) {
        return EqualsBuilder.reflectionEquals(this, o);
    }

    @Override
    public int hashCode() {
        return HashCodeBuilder.reflectionHashCode(this);
    }
}
