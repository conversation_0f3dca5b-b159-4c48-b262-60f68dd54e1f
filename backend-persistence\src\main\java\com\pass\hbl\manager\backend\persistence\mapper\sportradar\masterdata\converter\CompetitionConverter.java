package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrCompetition;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.repository.sportradar.SrCompetitionRepository;
import lombok.SneakyThrows;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

@Component
public class CompetitionConverter extends AbstractConverter<String, SrCompetition> {

    private final SrCompetitionRepository repository;

    public CompetitionConverter(SrCompetitionRepository repository) {
        this.repository = repository;
    }

    @SneakyThrows
    @Override
    protected SrCompetition convert(String source) {
        return source == null ? null : repository.findById(source).orElseThrow(() -> new EntityNotExistException(SrCompetition.class, source));
    }
}
