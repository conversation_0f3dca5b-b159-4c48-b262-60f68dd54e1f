package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.time.ZonedDateTime;
import java.util.List;

@JsonRootName("lineup round score details")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Lineup round score details response used for live feature")
public class LineupRoundScoreDetailsDto {

    @Schema(description = "true, if currently a match day is running", example = "sundays in a regular round", required = true)
    @JsonProperty(value = "isMatchDayRunning")
    boolean isMatchDayRunning;

    @Schema(description = "nearest game start related to a previous or next game")
    private ZonedDateTime nearestGameStart;

    @Schema(description = "order of the special or regular round in season, corresponds to round number")
    private Integer roundOrder;

    @NotNull
    @Schema(description = "label of the season")
    private String seasonLabel;

    @Schema(description = "player round score details", required = true)
    private List<PlayerRoundScoreDetailsDto> playerScoreDetails;

    @Schema(description = "true, if user had a positive balance at round closing")
    @JsonProperty(value = "isScoringEnabled")
    Boolean isScoringEnabled;

    @Schema(description = "specifies whether the round is a special or regular round in season")
    private Boolean specialRound;
}
