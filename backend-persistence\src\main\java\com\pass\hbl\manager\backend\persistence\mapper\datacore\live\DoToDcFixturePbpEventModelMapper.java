package com.pass.hbl.manager.backend.persistence.mapper.datacore.live;

import com.pass.hbl.manager.backend.persistence.domain.datacore.FixturePbpEventModelDo;
import com.pass.hbl.manager.backend.persistence.entity.datacore.DcLiveMatchPlayByPlay;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.pass.hbl.manager.backend.persistence.mapper.datacore.live.DoToDcLiveMatchSummaryMapper.DC_HOME_CLUB_ID_PAIR;
import static java.util.Objects.isNull;

@Slf4j
@Component
public class DoToDcFixturePbpEventModelMapper extends AbstractMapper<FixturePbpEventModelDo, DcLiveMatchPlayByPlay> {

    //private final DcFixturePbpScoreConverter.HomeTeamScoreConverter homeTeamScoreConverter;
    //private final DcFixturePbpScoreConverter.AwayTeamScoreConverter awayTeamScoreConverter;

    public DoToDcFixturePbpEventModelMapper() {
        super(FixturePbpEventModelDo.class, DcLiveMatchPlayByPlay.class);
    }

    @Override
    protected void customizeInit() {
        Converter<FixturePbpEventModelDo.Options, String> toAttackTypeConverter = ctx -> {
            try {
                return ctx.getSource() == null ? null : ctx.getSource().getAttackType();
            } catch (Exception e) {
                log.error("failed to convert " + ctx.getSource() + " to attackType. Reason: " + e);
                return null;
            }
        };

        Converter<FixturePbpEventModelDo.Options, String> toFailureReasonConverter = ctx -> {
            try {
                return ctx.getSource() == null ? null : ctx.getSource().getFailureReason();
            } catch (Exception e) {
                log.error("failed to convert " + ctx.getSource() + " to failureReason. Reason: " + e);
                return null;
            }
        };

        Converter<FixturePbpEventModelDo.Options, String> toLocationConverter = ctx -> {
            try {
                return ctx.getSource() == null ? null : ctx.getSource().getLocation();
            } catch (Exception e) {
                log.error("failed to convert " + ctx.getSource() + " to location. Reason: " + e);
                return null;
            }
        };

        Converter<FixturePbpEventModelDo.Options, String> toGoalKeeperIdConverter = ctx -> {
            try {
                return ctx.getSource() == null ? null : ctx.getSource().getGoalKeeperId();
            } catch (Exception e) {
                log.error("failed to convert " + ctx.getSource() + " to location. Reason: " + e);
                return null;
            }
        };

        Converter<FixturePbpEventModelDo.Options, Boolean> toEmptyNetConverter = ctx -> {
            try {
                return ctx.getSource() == null ? null : ctx.getSource().getEmptyNet();
            } catch (Exception e) {
                log.error("failed to convert " + ctx.getSource() + " to location. Reason: " + e);
                return null;
            }
        };

        TypeMap<FixturePbpEventModelDo, DcLiveMatchPlayByPlay> typeMap = getOrCreateTypeMap(FixturePbpEventModelDo.class, DcLiveMatchPlayByPlay.class);
        //typeMap.addMappings(mapper -> mapper.using(homeTeamScoreConverter).map(source -> source, DcLiveMatchPlayByPlay::setScoreHome));
        //typeMap.addMappings(mapper -> mapper.using(awayTeamScoreConverter).map(source -> source, DcLiveMatchPlayByPlay::setScoreAway));
        typeMap.addMappings(mapper -> mapper.map(FixturePbpEventModelDo::getPersonId, DcLiveMatchPlayByPlay::setPlayerId));
        typeMap.addMappings(mapper -> mapper.using(toAttackTypeConverter).map(FixturePbpEventModelDo::getOptions, DcLiveMatchPlayByPlay::setAttackType));
        typeMap.addMappings(mapper -> mapper.using(toFailureReasonConverter).map(FixturePbpEventModelDo::getOptions, DcLiveMatchPlayByPlay::setFailureReason));
        typeMap.addMappings(mapper -> mapper.using(toLocationConverter).map(FixturePbpEventModelDo::getOptions, DcLiveMatchPlayByPlay::setLocation));
        typeMap.addMappings(mapper -> mapper.using(toGoalKeeperIdConverter).map(FixturePbpEventModelDo::getOptions, DcLiveMatchPlayByPlay::setGoalKeeperId));
        typeMap.addMappings(mapper -> mapper.using(toEmptyNetConverter).map(FixturePbpEventModelDo::getOptions, DcLiveMatchPlayByPlay::setEmptyNet));
    }

    @Override
    public DcLiveMatchPlayByPlay customizeMapToDto(DcLiveMatchPlayByPlay dcPlayByPlay, FixturePbpEventModelDo fixturePbpEventModelDo, Map<String, Object> ctx) {
        if (isNull(fixturePbpEventModelDo) || isNull(dcPlayByPlay)) {
            return null;
        }
        // Map of key = entityId (matchId), value = score
        Map<String, Integer> scores = fixturePbpEventModelDo.getScores();
        if (isNull(scores) || scores.isEmpty()) {
            return dcPlayByPlay;
        }
        // if the context is empty the first entityId in the Map count as home club, second as away club
        if (isNull(ctx) || ctx.isEmpty()) {
            Set<String> keys = scores.keySet();
            List<String> keyList = new ArrayList<>(keys);
            if (keyList.size() == 2) {
                // Get the score of the match id
                String homeTeamId = keyList.get(0);
                String awayTeamId = keyList.get(0);
                dcPlayByPlay.setScoreHome(scores.get(homeTeamId));
                dcPlayByPlay.setScoreHome(scores.get(awayTeamId));
            }
        } else {
            Pair<String, String> homeClubIdPair = (Pair<String, String>) ctx.get(DC_HOME_CLUB_ID_PAIR);
            if (scores.size() == 2) {
                // Get the match id
                dcPlayByPlay.setScoreHome(scores.get(homeClubIdPair.getKey()));
                dcPlayByPlay.setScoreAway(scores.get(homeClubIdPair.getValue()));
            }
        }
        return dcPlayByPlay;
    }
}
