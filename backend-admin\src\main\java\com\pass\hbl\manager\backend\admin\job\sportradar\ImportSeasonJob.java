package com.pass.hbl.manager.backend.admin.job.sportradar;

import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.exception.SchedulingException;
import com.pass.hbl.manager.backend.persistence.job.ScheduledJob;
import com.pass.hbl.manager.backend.persistence.job.admin.AdminAbstractJob;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.datacore.masterdata.ImportSeasonDataCoreService;
import com.pass.hbl.manager.backend.persistence.service.sportradar.masterdata.ImportSeasonService;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
@ScheduledJob(description = "Import seasons from Sportradar")
@Slf4j
public class ImportSeasonJob extends AdminAbstractJob {

    public static final String PARAM_DATACORE = "datacore";
    public static final String PARAM_TRANSFER_ONLY = "transferOnly";
    private final Environment environment;
    private final ImportSeasonService importSeasonService;
    private final ImportSeasonDataCoreService importSeasonDataCoreService;

    public ImportSeasonJob(ImportSeasonService importSeasonService,
                           ParameterService parameterService, org.springframework.core.env.Environment environment, ImportSeasonDataCoreService importSeasonDataCoreService) {
        super(parameterService);
        this.importSeasonService = importSeasonService;
        this.environment = environment;
        this.importSeasonDataCoreService = importSeasonDataCoreService;
    }

    @Override
    protected void work() throws Exception {

        boolean isDataCoreApi = getParameterAsBoolean(PARAM_DATACORE);
        if (isDataCoreApi) {
            log.info("Sportradar data imported from the DataCore Rest-Api");
            importSeasonDataCoreService.start(getParameterAsBoolean(PARAM_TRANSFER_ONLY));
        } else {
            importSeasonService.start(getParameterAsBoolean(PARAM_TRANSFER_ONLY));
        }
    }

    @Override
    protected void init() throws InvalidOperationException, SchedulingException, FormatException {
        boolean isDataCoreApi = getParameterAsBoolean(PARAM_DATACORE);
        List<String> activeProfiles = Arrays.asList(environment.getActiveProfiles());

        if (isDataCoreApi) {
            importSeasonDataCoreService.initDataCoreDefaults(activeProfiles);
        } else {
            importSeasonService.initSportradarDefaults();
        }
    }

    @Override
    protected void tearDown() {

    }

    @Override
    protected void terminate() {
        importSeasonService.cancel();
    }
}
