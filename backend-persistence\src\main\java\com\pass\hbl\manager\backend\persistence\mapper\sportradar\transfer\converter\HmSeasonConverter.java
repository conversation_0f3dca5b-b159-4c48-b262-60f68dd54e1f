package com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminExternalDataMapping;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmSeason;
import com.pass.hbl.manager.backend.persistence.repository.admin.AdminExternalDataMappingRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmSeasonRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ExternalDataMappingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
public class HmSeasonConverter extends AbstractConverter<String, HmSeason> {

    private final ExternalDataMappingService externalDataMappingService;

    private final HmSeasonRepository seasonRepository;

    public HmSeasonConverter(ExternalDataMappingService externalDataMappingService, HmSeasonRepository seasonRepository) {
        this.externalDataMappingService = externalDataMappingService;
        this.seasonRepository = seasonRepository;
    }

    @Override
    protected HmSeason convert(String source) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        Optional<HmSeason> season = externalDataMappingService.get(Datasource.DATACORE, ExternalEntity.SEASON, source)
                .map(AdminExternalDataMapping::getHmId)
                .map(seasonRepository::findById)
                .filter(Optional::isPresent)
                .map(Optional::get);
        if (season.isEmpty()) {
            log.warn("Unknown season with SrId=" + source);
            return null;
        }
        return season.get();
    }
}
