package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.UUID;

@JsonRootName("Transfer Market Bid User")
@Getter
@Setter
@NoArgsConstructor
@Schema(description = "Transfer market bid user info")
public class TransferMarketBidUserDto   {

    @NotNull
    @Schema(description = "Status of the transfer", example = "SOLD", required = true)
    private TransferStatus transferStatus;

    @NotNull
    @Schema(description = "player dto", required = true)
    private PlayerHistoryDto player;

    @Min(0)
    @Schema(description = "Value of the bid", required = true)
    private int bid;

    @NotNull
    @Schema(description = "Status of the bid", example = "ACCEPTED", required = true)
    private BidStatus status;

    @NotNull
    @Schema(description = "deleted at", required = true)
    private LocalDateTime deletedAt;

    public TransferMarketBidUserDto(TransferStatus transferStatus) {
        this.transferStatus = transferStatus;
    }

}
