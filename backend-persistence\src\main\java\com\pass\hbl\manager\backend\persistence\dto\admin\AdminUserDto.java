package com.pass.hbl.manager.backend.persistence.dto.admin;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.UUID;

@JsonRootName("AdminUser")
@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AdminUserDto extends AbstractDto<AdminUserDto, String> {
    @Schema(name = "id", description = "UUID of the AdminUser", required = true)
    @Size(min = 36, max = 36)
    @NotBlank
    private UUID id;

    @Schema(name = "username", description = "AdminUser username", required = true)
    @Size(max = 64)
    @NotBlank
    String username;

    @Schema(name = "emailAddress", description = "AdminUser emailAddress", required = true)
    @NotBlank
    String emailAddress;

    @Schema(name = "roles", description = "AdminUser roles", required = true)
    String roles;

    @Override
    public String getId() {
        return null;
    }
}
