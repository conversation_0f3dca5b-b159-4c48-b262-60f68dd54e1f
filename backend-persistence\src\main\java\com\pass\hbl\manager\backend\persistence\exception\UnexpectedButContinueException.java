package com.pass.hbl.manager.backend.persistence.exception;

import lombok.Getter;

@Getter
public class UnexpectedButContinueException extends CodedException {

    public UnexpectedButContinueException(String message) {
        super(ExceptionCode.UNEXPECTED, message);
    }

    public UnexpectedButContinueException(String message, Throwable cause) {
        super(ExceptionCode.UNEXPECTED, message, cause);
    }
}
