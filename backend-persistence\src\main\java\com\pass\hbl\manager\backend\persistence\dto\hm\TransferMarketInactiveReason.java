package com.pass.hbl.manager.backend.persistence.dto.hm;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * Reason for which the transfer market is inactive for all leagues
 */
public enum TransferMarketInactiveReason {
    TECHNICAL_ISSUE, SEASON_END, DEFAULT;

    public static TransferMarketInactiveReason getByStringCaseIgnore(String s) {
        if (Objects.isNull(s) || StringUtils.isEmpty(s)) {
            return null;
        }
        return TransferMarketInactiveReason.valueOf(s.toUpperCase());
    }
}
