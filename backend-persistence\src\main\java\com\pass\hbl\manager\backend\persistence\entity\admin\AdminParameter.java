package com.pass.hbl.manager.backend.persistence.entity.admin;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

import static com.pass.hbl.manager.backend.persistence.util.Constants.NOT_ASSIGNED;

@Table(name = "parameter", schema = "admin", catalog = "handball_manager")
@Getter
@Setter
@ToString
@Entity
@SQLDelete(sql = "UPDATE admin.parameter SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class AdminParameter extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @Column(name = "name")
    private String name;

    @Column(name = "value")
    private String value;

    @Column(name = "date_value")
    private LocalDateTime dateValue;

    public AdminParameter() {
    }

    public AdminParameter(String name) {
        this.name = name;
    }

    public AdminParameter(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public AdminParameter(String name, LocalDateTime dateValue) {
        this.name = name;
        this.dateValue = dateValue;
        this.value = NOT_ASSIGNED;
    }
}
