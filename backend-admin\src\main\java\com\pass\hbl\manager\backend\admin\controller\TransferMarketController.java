package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.dto.admin.TransferMarketAdminDto;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.service.hm.TransferMarketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;

import static com.pass.hbl.manager.backend.admin.util.ApiConstants.ROLE_ADMIN;

@RestController
@RequestMapping(ApiConstants.TRANSFER_MARKET_API)
@Validated
@Tag(name = "transfer market", description = "Admin API for transfer market management")
@RolesAllowed({ROLE_ADMIN, ApiConstants.ROLE_ADMIN_WRITE})
public class TransferMarketController {

    private final TransferMarketService service;

    public TransferMarketController(TransferMarketService service) {
        this.service = service;
    }

    @Operation(summary = "Get last auctions by the given league, user and player")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Found transfer market items",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = TransferMarketAdminDto.class))}),
            @ApiResponse(responseCode = "404", description = "No transfer market item found", content = {@Content})
    })
    @GetMapping(value = "/history/player/{playerId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<TransferMarketAdminDto> getHistoryByLeagueAndUserAndPlayer(@Parameter(name = "playerId")
                                                                      @PathVariable(name = "playerId") @NotNull UUID playerId,
                                                                           @Parameter(name = "leagueName")
                                                                      @RequestParam(name = "leagueName") @NotNull String leagueName,
                                                                           @Parameter(name = "user") @RequestParam(name = "user") @NotNull String usernameOrEmailAddress,
                                                                           @Parameter(name = "count", description = "count of last transfers", schema = @Schema(implementation = Integer.class))
                                                                      @RequestParam(name = "count") Integer count) throws EntityNotExistException {

        return service.getHistoryByLeagueAndUserAndPlayer(playerId, leagueName, usernameOrEmailAddress, count);
    }
}
