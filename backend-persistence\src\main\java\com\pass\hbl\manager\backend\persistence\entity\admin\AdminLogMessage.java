package com.pass.hbl.manager.backend.persistence.entity.admin;

import com.pass.hbl.manager.backend.persistence.dto.admin.LogMessageLevel;
import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.UUID;

@Table(name = "log_message", schema = "admin", catalog = "handball_manager")
@Getter
@Setter
@ToString
@Entity
@SQLDelete(sql = "UPDATE admin.log_message SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class AdminLogMessage extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;
    @Column(name = "name")
    private String name;

    @Column(name = "message")
    private String message;

    @ToString.Exclude
    @Column(name = "exception")
    private String exception;

    @Enumerated(EnumType.STRING)
    @Column(name = "level")
    private LogMessageLevel level;
    public AdminLogMessage() {
    }

    public AdminLogMessage(String name, String message, LogMessageLevel level) {
        super();
        this.name = name;
        this.message = message;
        this.level = level;
    }
}
