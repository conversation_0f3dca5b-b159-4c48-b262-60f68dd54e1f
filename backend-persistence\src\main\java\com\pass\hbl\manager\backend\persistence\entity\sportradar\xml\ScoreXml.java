package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "Score")
@NoArgsConstructor
@Data
public class ScoreXml {

    public static final String TYPE_CURRENT = "Current";
    public static final String TYPE_PERIOD1 = "Period1";
    public static final String TYPE_NORMALTIME = "Normaltime";

    @JacksonXmlProperty(localName = "sort", isAttribute = true)
    private Integer sort;

    @JacksonXmlProperty(localName = "type", isAttribute = true)
    private String type;

    @JacksonXmlProperty(localName = "Team1")
    private SimpleTeam1Xml team1;

    @JacksonXmlProperty(localName = "Team2")
    private SimpleTeam2Xml team2;
}
