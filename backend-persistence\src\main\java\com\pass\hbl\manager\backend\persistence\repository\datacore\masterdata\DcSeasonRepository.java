package com.pass.hbl.manager.backend.persistence.repository.datacore.masterdata;

import com.pass.hbl.manager.backend.persistence.entity.datacore.DcSeason;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Optional;

public interface DcSeasonRepository extends PagingAndSortingRepository<DcSeason, String> {

    @Query(value = "SELECT * FROM datacore.season WHERE start_date <= now() and end_date >= now() limit 1", nativeQuery = true)
    Optional<DcSeason> findCurrentSeason();
}
