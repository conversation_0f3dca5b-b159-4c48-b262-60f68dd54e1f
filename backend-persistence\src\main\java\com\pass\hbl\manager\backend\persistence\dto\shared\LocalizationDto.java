package com.pass.hbl.manager.backend.persistence.dto.shared;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

@JsonRootName("Description")
@Getter
@Setter
@Schema(description = "Entities localization")
public class LocalizationDto extends AbstractDto<LocalizationDto, String> {

    @Schema(description = "Description id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "Type of the entity", example = "AWARD", required = true)
    @Size(max = 16)
    @NotEmpty
    private String hmEntityType;

    @Schema(description = "Id of the entity", example = "005aa59d-81d9-41eb-9df7-94867a1f7542")
    private String entityId;

    @Schema(description = "key of the localization", example = "name", required = true)
    @Size(max = 256)
    @NotEmpty
    private String key;

    @Schema(description = "value of the localization", example = "Season Winner", required = true)
    @Size(max = 1024)
    @NotEmpty
    private String value;

    @Schema(description = "code of the language", example = "de")
    @Size(min = 2, max = 2)
    @NotEmpty
    private String languageCode;
}
