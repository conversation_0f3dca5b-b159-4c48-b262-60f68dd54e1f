package com.pass.hbl.manager.backend.persistence.entity.datacore;

import com.pass.hbl.manager.backend.persistence.domain.datacore.FixtureStatus;
import com.pass.hbl.manager.backend.persistence.dto.hm.MatchStatus;
import com.pass.hbl.manager.backend.persistence.dto.hm.Winner;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrAbstractMatch;
import com.pass.hbl.manager.backend.persistence.exception.MappingException;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Table(name = "match", schema = "datacore", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class DcMatch extends SrAbstractMatch {

    @NotNull
    @Column(name = "start_time")
    private LocalDateTime startTime;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "season_id", referencedColumnName = "id", nullable = false)
    private DcSeason season;

    @Column(name = "round")
    private Integer round;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "home_id", referencedColumnName = "id", nullable = false)
    private DcCompetitor homeCompetitor;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "away_id", referencedColumnName = "id", nullable = false)
    private DcCompetitor awayCompetitor;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private FixtureStatus status;

    @Column(name = "home_score")
    private Integer homeScore;

    @Column(name = "away_score")
    private Integer awayScore;

    @Enumerated(EnumType.STRING)
    @NotNull
    @Column(name = "winner")
    private Winner winner;


    @Override
    public String getHomeClubId() {
        return getHomeCompetitor().getId();
    }

    @Override
    public String getAwayClubId() {
        return getAwayCompetitor().getId();
    }


    @Override
    public String getMatchTime() {
        return null;
    }


    @Override
    public MatchStatus getMatchStatus() {
        try {
            return MatchStatus.getByDataCoreFixtureStatus(status);
        } catch (MappingException e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public String getSeasonId() {
        return season.getId();
    }

    @Override
    public void setSeasonId(String seasonId) {
        // ignore for static matches
    }

    @Override
    public Integer getHblRound() {
        return round;
    }

    @Override
    public Integer getHomeScore() {
        return homeScore;
    }

    @Override
    public Integer getAwayScore() {
        return awayScore;
    }

    @Override
    public Winner getWinner() {
        return winner;
    }

    @Override
    public Integer getHalfTimeHomeScore() {
        return null;
    }

    @Override
    public Integer getHalfTimeAwayScore() {
        return null;
    }

    @Override
    public LocalDateTime getStartTime() {
        return startTime;
    }

}
