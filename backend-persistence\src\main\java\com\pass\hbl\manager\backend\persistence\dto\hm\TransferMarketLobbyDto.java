package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;
import java.util.List;

@JsonRootName("TransferMarket")
@Getter
@Setter
@Schema(description = "Transfer market")
public class TransferMarketLobbyDto extends AbstractDto<TransferMarketLobbyDto, String> {

    @Schema(description = "Bid id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true, accessMode = Schema.AccessMode.READ_ONLY)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @NotNull
    @Schema(description = "Player offered for a transfer", required = true)
    private PlayerLobbyDto player;

    @NotNull
    @Schema(description = "Transfer price", required = true)
    private int price;

    @Schema(description = "Creation datetime of the transfer item")
    private ZonedDateTime createdAt;

    public TransferMarketLobbyDto() {
    }

    public TransferMarketLobbyDto(String id, PlayerLobbyDto player, int price, ZonedDateTime createdAt) {
        this.id = id;
        this.player = player;
        this.price = price;
        this.createdAt = createdAt;
    }
}
