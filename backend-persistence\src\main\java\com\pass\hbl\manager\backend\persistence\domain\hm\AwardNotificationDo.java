package com.pass.hbl.manager.backend.persistence.domain.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.NotificationEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Domain object for user award push notification
 */
@Getter
@Setter
public class AwardNotificationDo extends AbstractNotificationDo {

    private String picture;

    public AwardNotificationDo(NotificationEvent notificationEvent, String picture) {
        this.notificationEvent = notificationEvent;
        this.picture = picture;
    }
}