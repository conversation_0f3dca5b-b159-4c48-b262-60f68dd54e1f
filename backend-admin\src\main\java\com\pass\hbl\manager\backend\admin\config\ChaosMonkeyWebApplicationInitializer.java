package com.pass.hbl.manager.backend.admin.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.WebApplicationInitializer;

import javax.servlet.ServletContext;

@Configuration
public class ChaosMonkeyWebApplicationInitializer implements WebApplicationInitializer {

    @Override
    public void onStartup(ServletContext servletContext) {
        String profiles = servletContext.getInitParameter("spring.profiles.active");
        if (StringUtils.isNotEmpty(profiles)) {
            profiles = profiles + ",chaos-monkey";
        } else {
            profiles = "chaos-monkey";
        }
        servletContext.setInitParameter("spring.profiles.active", profiles);
    }
}
