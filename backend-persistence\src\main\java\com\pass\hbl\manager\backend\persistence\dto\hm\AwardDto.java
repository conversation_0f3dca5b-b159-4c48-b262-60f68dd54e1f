package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import jdk.jfr.BooleanFlag;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.*;
import java.util.Map;

@JsonRootName("Award")
@Getter
@Setter
@Schema(description = "Award info")
public class AwardDto extends AbstractPictureDto<AwardDto, String> {

    @Schema(description = "Award id, required for updates, always set on get", example = "005aa59d-81d9-41eb-9df7-94867a1f7542")
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "Award stack-ability")
    @BooleanFlag
    private boolean stackable;

    @Schema(description = "code of the award", example = "LEAGUE_WINNER")
    @NotNull
    private AwardCode code;

    @Min(1)
    @Max(Integer.MAX_VALUE)
    @Schema(description = "order of the award")
    private int displayOrder;

    @Schema(description = "exprience points obtained by the award")
    private Integer experiencePoints;

    @Schema(description = "money obtained the award")
    private Integer money;

    @Schema(description = "award descriptions by localization", required = true)
    @JsonDeserialize(contentAs = AwardLocalizationDto.class)
    @NotEmpty
    private Map<String, AwardLocalizationDto> localizations;
}
