package com.pass.hbl.manager.backend.admin.config;

//@Configuration
//@EnableWebSecurity
//@EnableGlobalMethodSecurity(prePostEnabled = true, jsr250Enabled = true)
public class LeagacySecurityConfig {

	/*private final MyBasicAuthenticationEntryPoint authenticationEntryPoint;

	private final HandballManagerAdminConfigurationProperties properties;

	public SecurityConfig(MyBasicAuthenticationEntryPoint authenticationEntryPoint, HandballManagerAdminConfigurationProperties properties) {
		this.authenticationEntryPoint = authenticationEntryPoint;
		this.properties = properties;
	}
	@Bean
	public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
		return authenticationConfiguration.getAuthenticationManager();
	}

	@Bean
	public UserDetailsService userDetailsService() {
		UserDetails admin = User.builder()
				.username(properties.getSecurity().getAdminUser())
				.password(passwordEncoder().encode(properties.getSecurity().getAdminPassword()))
				.roles(ADMIN)
				.build();
		UserDetails writeAdmin = User.builder()
				.username(properties.getSecurity().getAdminWriteUser())
				.password(passwordEncoder().encode(properties.getSecurity().getAdminWritePassword()))
				.roles(ADMIN_WRITE)
				.build();
		return new InMemoryUserDetailsManager(admin, writeAdmin);
	}

	@Bean
	public DaoAuthenticationProvider authenticationProvider() {
		DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
		authProvider.setUserDetailsService(userDetailsService());
		authProvider.setPasswordEncoder(passwordEncoder());
		return authProvider;
	}

	@Bean
	public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
		http.cors().and().csrf().disable();

		http.authorizeRequests()
				.anyRequest().authenticated()
				.and()
				.httpBasic()
				.authenticationEntryPoint(authenticationEntryPoint);

		http.addFilterAfter(new CustomFilter(),
				BasicAuthenticationFilter.class);
		return http.build();
	}

	@Bean
	public PasswordEncoder passwordEncoder() {
		return new BCryptPasswordEncoder();
	}
*/}
