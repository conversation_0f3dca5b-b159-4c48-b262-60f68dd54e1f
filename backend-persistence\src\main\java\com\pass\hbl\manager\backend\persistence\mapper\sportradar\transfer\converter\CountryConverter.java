package com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter;

import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

@Component
public class CountryConverter extends AbstractConverter<String, String> {

    private final Map<String, String> codes;

    public CountryConverter() {
        codes = new HashMap<>();
        Arrays.stream(Locale.getAvailableLocales()).forEach(l -> {
            try {
                codes.put(l.getISO3Country(), l.getCountry());
            } catch (Throwable t) {
                // just ignore and continue
            }
        });

    }

    @Override
    protected String convert(String source) {
        if (!StringUtils.hasText(source)) {
            return null;
        }
        return codes.getOrDefault(source, null);
    }
}
