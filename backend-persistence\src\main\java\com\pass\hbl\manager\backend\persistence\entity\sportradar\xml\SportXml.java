package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "Sport")
@NoArgsConstructor
@Data
public class SportXml {

    @JacksonXmlProperty(localName = "BetradarSportId", isAttribute = true)
    private Integer betradarSportId;

    @JacksonXmlProperty(localName = "Name")
    private List<NameXml> names;

    @JacksonXmlProperty(localName = "Category")
    private List<CategoryXml> categories;
}
