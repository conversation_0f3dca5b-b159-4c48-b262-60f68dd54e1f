package com.pass.hbl.manager.backend.admin.job.sportradar;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.job.ScheduledJob;
import com.pass.hbl.manager.backend.persistence.job.admin.AdminAbstractJob;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.sportradar.transfer.Sr2HmService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Set;

@Slf4j
@Component
@ScheduledJob(description = "Transfers data from sportradar data ", parameters = TransferSportradarDataJob.PARAM_OBJECTS + "=" + TransferSportradarDataJob.PARAM_VALUE_ALL)
public class TransferSportradarDataJob extends AdminAbstractJob {

    public static final String PARAM_OBJECTS = "objects";
    public static final String PARAM_VALUE_ALL = "all";
    public static final String PARAM_VALUE_SEASON = "seasons";
    public static final String PARAM_VALUE_COMPETITOR = "competitors";
    public static final String PARAM_VALUE_PLAYER = "players";
    public static final String PARAM_VALUE_MATCH = "matches";
    public static final String PARAM_VALUE_EVENTS = "events";
    public static final String PARAM_VALUE_STATISTICS = "statistics";

    private final Sr2HmService service;

    public TransferSportradarDataJob(Sr2HmService service, ParameterService parameterService) {
        super(parameterService);
        this.service = service;
    }

    @Override
    protected void work() throws Exception {

        getParameterList(PARAM_OBJECTS).forEach(s -> {
            log.warn("CAUTION: this service cannot be interrupted as this would lead to data inconsistencies. Do not terminate it!!!");
            switch (s) {
                case PARAM_VALUE_COMPETITOR -> service.transferClubs();
                case PARAM_VALUE_EVENTS -> {
                    try {
                        service.transferEvents(false);
                    } catch (Exception e) {
                        log.error("Error by events transfer: " + e.getMessage());
                    }
                }
                case PARAM_VALUE_MATCH -> service.transferMatches();
                case PARAM_VALUE_PLAYER -> service.transferPlayers();
                case PARAM_VALUE_SEASON -> service.transferSeasons();
                case PARAM_VALUE_STATISTICS -> service.transferStatistics(null);
                case PARAM_VALUE_ALL -> {
                    service.transferSeasons();
                    service.transferClubs();
                    service.transferPlayers();
                    Set<String> matchIds = service.transferMatches();
                    try {
                        service.transferEvents(false);
                    } catch (Exception e) {
                    log.error("Error by events transfer: " + e.getMessage());
                }
                    service.transferStatistics(matchIds);
                }
            }
        });

    }

    @Override
    protected void init() {
    }

    @Override
    protected void tearDown() {

    }

    @Override
    protected void terminate() throws Exception {
    }
}
