package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.exception.MappingException;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

public enum Winner {
    HOME(1), AWAY(2), DRAW(3), N_A(0);

    private final int srLiveMatchIdentifier;

    Winner(int srLiveMatchIdentifier) {
        this.srLiveMatchIdentifier = srLiveMatchIdentifier;
    }

    public static Winner getByStringCaseIgnore(String s) {
        if (StringUtils.isEmpty(s)) {
            return null;
        }
        return Winner.valueOf(s.toUpperCase());
    }

    public static Winner getBySrLiveMatchValue(int value) throws MappingException {
        return Arrays.stream(Winner.values()).filter(w -> w.srLiveMatchIdentifier == value).findFirst()
                .orElseThrow(() -> new MappingException("Unknown winner live identifier", Datasource.SPORTRADAR, String.valueOf(value), Winner.class));
    }
}
