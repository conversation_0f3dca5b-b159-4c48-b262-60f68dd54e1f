package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@JsonRootName("Player match score")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Player match score")
public class PlayerMatchScoreDto {

    @NotNull
    @ToString.Exclude
    @Schema(description = "id of the match", required = true)
    private String matchId;

    @Schema(description = "total score gained overall up to the given round")
    private Integer totalScore;

    @Schema(description = "challenger club")
    private ClubDto challenger;
}
