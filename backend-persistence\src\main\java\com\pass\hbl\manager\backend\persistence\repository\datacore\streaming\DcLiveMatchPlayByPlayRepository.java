package com.pass.hbl.manager.backend.persistence.repository.datacore.streaming;

import com.pass.hbl.manager.backend.persistence.entity.datacore.DcLiveMatchPlayByPlay;
import com.pass.hbl.manager.backend.persistence.entity.datacore.DcLiveMatchPlayerStatistics;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.time.LocalDateTime;

public interface DcLiveMatchPlayByPlayRepository extends PagingAndSortingRepository<DcLiveMatchPlayByPlay, String> {

    @Query(value = "select max(h.modifiedAt) from DcLiveMatchPlayByPlay h")
    LocalDateTime getLatestUpdate();

    int deleteByMatchId(String matchId);

    int deleteByPlayId(String playId);
}
