package com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminExternalDataMapping;
import com.pass.hbl.manager.backend.persistence.repository.admin.AdminExternalDataMappingRepository;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class IdConverter extends AbstractConverter<String, UUID> {

    private final AdminExternalDataMappingRepository repository;

    public IdConverter(AdminExternalDataMappingRepository repository) {
        this.repository = repository;
    }

    @Override
    protected UUID convert(String source) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }

        // ids in sportradar are unique across entities. That's why this works
        return repository.findFirstByDatasourceAndSourceId(Datasource.SPORTRADAR, source).map(AdminExternalDataMapping::getHmId).orElse(null);
    }
}
