package com.pass.hbl.manager.backend.admin.service.internal;

import com.pass.hbl.manager.backend.persistence.entity.admin.AdminUser;
import com.pass.hbl.manager.backend.persistence.repository.admin.AdminUserRepository;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class UserDetailsInfoService implements UserDetailsService {

    private final AdminUserRepository repository;

    public UserDetailsInfoService(AdminUserRepository repository) {
        this.repository = repository;
    }

    @Override
    public UserDetails loadUserByUsername(String emailAddress) throws UsernameNotFoundException {
        Optional<AdminUser> user = repository.findByEmail(emailAddress);
        return user.map(adminUser -> new UserDetailsInfo(adminUser.getEmailAddress(), adminUser.getPassword(),
                adminUser.getRoles())).orElseThrow(() -> new UsernameNotFoundException("User Not Found: " + emailAddress));
    }
}
