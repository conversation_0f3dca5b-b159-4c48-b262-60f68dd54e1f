package com.pass.hbl.manager.backend.persistence.mapper.datacore.live;

import com.pass.hbl.manager.backend.persistence.domain.datacore.FixturePbpEventModelDo;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.sportradar.datacore.rest.model.FixturePbpEventModel;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;
import org.threeten.bp.LocalDateTime;

import java.util.UUID;

@Slf4j
@Component
public class FixturePbpEventModelMapper extends AbstractMapper<FixturePbpEventModel, FixturePbpEventModelDo> {

    public FixturePbpEventModelMapper() {
        super(FixturePbpEventModel.class, FixturePbpEventModelDo.class);
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected void customizeInit() {

        Converter<LocalDateTime, java.time.LocalDateTime> toEventTimeConverter = ctx -> {
            try {
                if (ctx.getSource() == null) {
                    return null;
                } else {
                    LocalDateTime threeTenDateTime = ctx.getSource();
                    return java.time.LocalDateTime.of(
                            threeTenDateTime.getYear(),
                            threeTenDateTime.getMonthValue(),
                            threeTenDateTime.getDayOfMonth(),
                            threeTenDateTime.getHour(),
                            threeTenDateTime.getMinute(),
                            threeTenDateTime.getSecond(),
                            threeTenDateTime.getNano()
                    );
                }
            } catch (Exception e) {
                log.error("failed to convert " + ctx.getSource() + " to eventTime. Reason: " + e);
                return null;
            }
        };

        Converter<UUID, String> uuidStringConverter = ctx -> {
            try {
                return ctx.getSource() == null? null: ctx.getSource().toString();
            } catch (Exception e) {
                log.error("failed to convert " + ctx.getSource() + " to String. Reason: " + e);
                return null;
            }
        };

        Converter<FixturePbpEventModel.PeriodIdEnum, Integer> periodIdConverter = ctx -> {
            try {
                return ctx.getSource() == null? null: ctx.getSource().getValue();
            } catch (Exception e) {
                log.error("failed to convert " + ctx.getSource() + " to Integer. Reason: " + e);
                return null;
            }
        };


        TypeMap<FixturePbpEventModel, FixturePbpEventModelDo> typeMap = getOrCreateTypeMap(FixturePbpEventModel.class, FixturePbpEventModelDo.class);
        typeMap.addMappings(mapper -> mapper.using(toEventTimeConverter) .map(FixturePbpEventModel::getEventTime, FixturePbpEventModelDo::setEventTime));
        typeMap.addMappings(mapper -> mapper.using(uuidStringConverter).map( FixturePbpEventModel::getPersonId, FixturePbpEventModelDo::setPersonId));
        typeMap.addMappings(mapper -> mapper.using(periodIdConverter).map(FixturePbpEventModel::getPeriodId, FixturePbpEventModelDo::setPeriodId));
    }
}
