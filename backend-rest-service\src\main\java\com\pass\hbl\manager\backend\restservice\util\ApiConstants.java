package com.pass.hbl.manager.backend.restservice.util;

public interface ApiConstants {

    // IMPORTANT! UPDATE the current version number to require an app update for all versions until 1.0.5
    // Initial value = 1; 22.05.2024 -> upgrade 1 -> 2
    int CURRENT_MAJOR_APP_VERSION = 3;

    // IMPORTANT! UPDATE the next version number to require an app update for all versions starting from 1.0.6
    int NEXT_MAJOR_APP_VERSION = 3;

    // example "v1"
    String VERSION_V1 = "v1";
    String VERSION_V2 = "v2";

    String USER_API = "/" + VERSION_V1 + "/user";
    String IMAGE_API = "/" + VERSION_V1 + "/image";
    String LEAGUE_API = "/" + VERSION_V1 + "/league";
    String SEASON_API = "/" + VERSION_V1 + "/season";
    String TEAM_API = "/" + VERSION_V1 + "/team";
    String PLAYER_API = "/" + VERSION_V1 + "/player";
    String AWARD_API = "/" + VERSION_V1 + "/award";
    String LIVE_API = "/internal/" + VERSION_V1 + "/live";
    String TRANSFER_MARKET_API = "/" + VERSION_V1 + "/transfermarket";
    String SCHEDULER_API = "/" + VERSION_V1 + "/scheduler";
    String FCM_API = "/" + VERSION_V1 + "/fcm";
    String PING_API = "/" + VERSION_V1 + "/ping";
    String STREAM_API = "/" + VERSION_V1 + "/stream";
    String SNS_API = "/" + VERSION_V1 + "/sns";
    String MQTT_API = "/" + VERSION_V1 + "/mqtt";

    String ADMIN_API = "/admin";
    String HBL_API = "/hbl";
    String STAT_API = "/" + VERSION_V1 + "/statistics";
    String APPLE_NO_VERSION_API = "/apple/notification";
    String GOOGLE_NO_VERSION_API = "/google/notification";

    String LEAGUE_API_VERSION_V2 = "/" + VERSION_V2 + "/league";


    String MAGIC_TOKEN = "magic-token";

    String API_KEY = "apiKey";

    String CONFIRMATION_LINK = "confirmation";

    String FCM_SERVICE_ACCOUNT_KEY_PROD = "fcm-account-key-prod.json";

    String FCM_SERVICE_ACCOUNT_KEY_PROD2 = "fcm-account-key-prod2.json";
    String FCM_SERVICE_ACCOUNT_KEY_PROD3 = "fcm-account-key-prod3.json";
    String FCM_SERVICE_ACCOUNT_KEY_PROD4 = "fcm-account-key-prod4.json";
    String FCM_SERVICE_ACCOUNT_KEY_PROD5 = "fcm-account-key-prod5.json";
    String FCM_SERVICE_ACCOUNT_KEY_DEV = "fcm-account-key-dev.json";
    String FCM_SERVICE_ACCOUNT_KEY_STAGE = "fcm-account-key-stage.json";
    String FCM_SERVICE_ACCOUNT_KEY_STAGE2 = "fcm-account-key-stage2.json";
    String FCM_SERVICE_ACCOUNT_KEY_STAGE3 = "fcm-account-key-stage3.json";
    String FCM_SERVICE_ACCOUNT_KEY_STAGE4 = "fcm-account-key-stage4.json";

    String UTC_TIMEZONE = "Etc/UTC";

    String PING = "ping";
}
