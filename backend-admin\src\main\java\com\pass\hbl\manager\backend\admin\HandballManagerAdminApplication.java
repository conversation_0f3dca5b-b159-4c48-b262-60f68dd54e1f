package com.pass.hbl.manager.backend.admin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import java.util.TimeZone;

import static com.pass.hbl.manager.backend.admin.util.ApiConstants.UTC_TIMEZONE;

@SpringBootApplication(scanBasePackages = {"com.pass.hbl.manager.backend"})
@EntityScan({"com.pass.hbl.manager.backend"})
@EnableJpaRepositories({"com.pass.hbl.manager.backend"})
@PropertySource("classpath:application.yml")
@EnableCaching
public class HandballManagerAdminApplication {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone(UTC_TIMEZONE));
        SpringApplication.run(HandballManagerAdminApplication.class, args);
    }
}
