package com.pass.hbl.manager.backend.persistence.entity.hm;


import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.UUID;

@Table(name = "user_round_score", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@Entity
@NoArgsConstructor
@ToString
@SQLDelete(sql = "UPDATE hm.user_round_score SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmUserRoundScore extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    //Important FetchType.LAZY in order to avoid object loading in a write-transaction
    @ToString.Exclude
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id", updatable = false)
    private HmUserProfile user;

    @ToString.Exclude
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "league_id", referencedColumnName = "id", updatable = false)
    private HmLeague league;

    @ToString.Exclude
    @OneToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "round_id", referencedColumnName = "id", updatable = false)
    private HmRound round;

    @Column(name = "score")
    private Integer score;

    @Column(name = "start_balance")
    private Integer startBalance;

    public HmUserRoundScore(HmUserProfile user, HmLeague league, HmRound round, Integer startBalance) {
        this(user, league, round, null, startBalance);
    }

    public HmUserRoundScore(HmUserProfile user, HmLeague league, HmRound round, Integer score, Integer startBalance) {
        this.user = user;
        this.league = league;
        this.round = round;
        this.score = score;
        this.startBalance = startBalance;
    }
}
