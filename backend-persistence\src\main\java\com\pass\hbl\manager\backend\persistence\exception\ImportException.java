package com.pass.hbl.manager.backend.persistence.exception;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import lombok.Getter;

public class ImportException extends CodedException {

    @Getter
    private final Datasource datasource;

    public ImportException(Datasource datasource, String message, String... args) {
        this(datasource, message, null, args);
    }

    public ImportException(Datasource datasource, String message, Throwable cause, String... args) {
        super(ExceptionCode.IMPORT, message, cause, args);
        this.datasource = datasource;
    }

}
