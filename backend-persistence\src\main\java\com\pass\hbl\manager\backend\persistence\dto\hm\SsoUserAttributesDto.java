package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;

@JsonRootName("SsoUserAttributes")
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(description = "User attributes returned from Single Sign-On interface")
public class SsoUserAttributesDto {

    @Schema(description = "Short id the user is registered with", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String sub;

    @Size(max = 256)
    @Schema(description = "Optional first name of user")
    private String name;

    @Size(max = 256)
    @JsonProperty(value = "family_name")
    @Schema(description = "Optional last name of user")
    private String familyName;

    @Schema(description = "Username user is registered with", example = "john-doe", required = true)
    @NotBlank
    @Size(max = 256)
    private String username;

    @Schema(description = "Mail address user is registered with", example = "<EMAIL>", required = true)
    @JsonProperty("email")
    @NotBlank
    @Size(max = 256)
    private String emailAddress;

    @NotNull
    @JsonProperty("email_verified")
    @Schema(description = "Whether mail address is verified", example = "false", required = true)
    private boolean email_verified;

    @Schema(description = "user status", example = "CONFIRMED", required = true)
    @NotBlank
    private String userstatus;

    @JsonCreator
    public SsoUserAttributesDto() {
    }

    @Override
    public String toString() {
        return "SsoUserAttributesDto{" +
                "sub='" + sub + '\'' +
                ", name='" + name + '\'' +
                ", familyName='" + familyName + '\'' +
                ", username='" + username + '\'' +
                ", emailAddress='" + emailAddress + '\'' +
                ", email_verified=" + email_verified +
                ", userstatus='" + userstatus + '\'' +
                '}';
    }
}
