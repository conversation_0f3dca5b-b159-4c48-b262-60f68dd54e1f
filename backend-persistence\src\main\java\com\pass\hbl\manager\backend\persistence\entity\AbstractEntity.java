package com.pass.hbl.manager.backend.persistence.entity;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

@MappedSuperclass
@Setter
@Getter
public abstract class AbstractEntity implements Deletable, Timestamped, Serializable {

    public static final LocalDateTime ZERO_DATE = LocalDateTime.of(1970,1,1,0,0,0);

    @Column(name = "created_at", columnDefinition = "timestamp not null default now()", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "modified_at", columnDefinition = "timestamp")
    private LocalDateTime modifiedAt;

    @Column(name = "deleted_at", columnDefinition = "timestamp")
    private LocalDateTime deletedAt;

    @Column(name = "deleted", columnDefinition = "boolean not null default false", nullable = false)
    private boolean deleted;

    private transient boolean modified;

    public AbstractEntity() {
        this.modified = false;
        this.createdAt = LocalDateTime.now();
    }

    public AbstractEntity self() {
        return this;
    }

    public abstract UUID getId();

    public abstract void setId (UUID id);

    @PrePersist
    protected void onCreate() {
        if (this.createdAt == null) {
            this.createdAt = LocalDateTime.now();
        }
        checkDates();
        checkCustomDates();
    }

    @PreUpdate
    protected void onPersist() {
        this.modifiedAt = LocalDateTime.now();
        checkDates();
        checkCustomDates();
    }

    @PostLoad
    protected void afterLoad() {
        if (Objects.equals(this.modifiedAt, ZERO_DATE)) {
            this.modifiedAt = null;
        }
        if (Objects.equals(this.deletedAt, ZERO_DATE)) {
            this.deletedAt = null;
        }
        loadCustomDates();
    }

    protected String[] getIgnoreFields() {
        return new String[]{"modifiedAt", "createdAt", "deletedAt", "deleted", "modified"};
    }

    private void checkDates() {
        if (this.createdAt == null) {
            this.createdAt = ZERO_DATE;
        }
        if (this.modifiedAt == null) {
            this.modifiedAt = ZERO_DATE;
        }
        if (this.deletedAt == null) {
            this.deletedAt = ZERO_DATE;
        }
    }

    protected void checkCustomDates() {
        // wait for override
    }

    protected void loadCustomDates() {
        // wait for override
    }

    @Override
    @SuppressWarnings({"EqualsWhichDoesntCheckParameterClass", "EqualsDoesntCheckParameterClass", "com.haulmont.jpb.EqualsDoesntCheckParameterClass"})
    public boolean equals(Object o) {
        return EqualsBuilder.reflectionEquals(this, o, getIgnoreFields());
    }

    @Override
    public int hashCode() {
        return HashCodeBuilder.reflectionHashCode(this, getIgnoreFields());
    }
}
