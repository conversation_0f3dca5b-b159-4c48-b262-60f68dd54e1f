package com.pass.hbl.manager.backend.admin.service.internal;

import com.pass.hbl.manager.backend.persistence.dto.admin.UserRole;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminUser;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public class UserDetailsInfo implements UserDetails {

    private final String username;
    private final String password;
    private List<GrantedAuthority> authorities;

    public UserDetailsInfo(AdminUser user){
        username = user.getEmailAddress();
        password = user.getPassword();
    }

    public UserDetailsInfo(String username, String password, List<UserRole> roles){
        this.username = username;
        this.password = password;
        this.authorities = roles.stream()
                .map(role -> new SimpleGrantedAuthority(role.name()))
                .collect(Collectors.toList());
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }
}
