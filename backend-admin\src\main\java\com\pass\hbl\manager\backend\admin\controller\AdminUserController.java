package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.dto.admin.AddAdminUserRequestDto;
import com.pass.hbl.manager.backend.persistence.dto.admin.AdminUserDto;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminUser;
import com.pass.hbl.manager.backend.persistence.repository.admin.AdminUserRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.AdminUserService;
import com.pass.hbl.manager.backend.persistence.service.admin.UserVerificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.pass.hbl.manager.backend.admin.util.ApiConstants.ROLE_SYSTEM;

@RestController
@RequestMapping(ApiConstants.ADMIN_USER_API)
@Validated
@Tag(name = "admin user", description = "API for admin user management")
@Slf4j
@CrossOrigin
public class AdminUserController {

    private final AdminUserService userService;
    private final AdminUserRepository userRepository;
    private final UserVerificationService userVerificationService;

    public AdminUserController(AdminUserService userService, AdminUserRepository userRepository, UserVerificationService userVerificationService, AuthenticationManager authenticationManager) {
        this.userService = userService;
        this.userRepository = userRepository;
        this.userVerificationService = userVerificationService;
    }

    @GetMapping("/systemTest")
    @RolesAllowed({ROLE_SYSTEM})
    public void getSystemUser(HttpServletResponse httpServletResponse)
            throws IOException {
        httpServletResponse.setContentType("text/plain");
        if (Objects.nonNull(userService.getSystemUser())) {
            httpServletResponse.getWriter().write("Success");

        } else {
            getBadRequestHttpResponse(httpServletResponse, "Error. System user not found");
        }
    }

    @Operation(summary = "Search AdminUser")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Found AdminUser", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = AdminUserDto.class))}),
            @ApiResponse(responseCode = "404", description = "No AdminUser found", content = {@Content})
    })
    @GetMapping(value = "/search", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<AdminUserDto> searchAdminUser(@Parameter(hidden = true) Pageable pageable,
                                              @RequestParam(value = "adminUser", required = false) String adminUser) {
        return userService.getAllAdminUser(pageable, adminUser);
    }

    @Operation(summary = "Change Password for AdminUser")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "AdminUser update info", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "No AdminUser details found")
    })
    @PostMapping("/change-password")
    public void changePassword(HttpServletResponse httpServletResponse,
                               @Parameter(name = "emailAddress")
                               @RequestParam String emailAddress,
                               @Parameter(name = "newPassword")
                               @RequestParam String newPassword) throws IOException {
        try {
            if (userService.validatePassword(newPassword)) {
                userService.changePassword(emailAddress, newPassword);
            } else {
                getBadRequestHttpResponse(httpServletResponse, """
                        Error. Invalid Password.
                        Password must contain 1 number (0-9)
                        Password must contain 1 uppercase letters
                        Password must contain 1 lowercase letters
                        Password must contain 1 non-alpha numeric number
                        Password is 8-16 characters with no space""");
            }
        } catch (Exception e) {
            getBadRequestHttpResponse(httpServletResponse, e.getMessage());
        }
    }

    @Operation(summary = "Adds a new user as administrator")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support")
    })
    @PostMapping("/add")
    @RolesAllowed({ROLE_SYSTEM})
    public void addAdminUser(HttpServletResponse httpServletResponse,
                             @Parameter(name = "playerTransferRequest", description = "request info for adding an admin user", required = true,
                                     schema = @Schema(implementation = AddAdminUserRequestDto.class))
                             @RequestBody AddAdminUserRequestDto addAdminUserRequestDto)
            throws IOException {
        httpServletResponse.setContentType("text/plain");
        try {
            if (userService.validatePassword(addAdminUserRequestDto.getPassword())) {
                if (userService.addAdminUser(new AdminUser(addAdminUserRequestDto.getUsername(), addAdminUserRequestDto.getPassword(),
                        addAdminUserRequestDto.getEmailAddress(), addAdminUserRequestDto.getRoles()))) {
                    httpServletResponse.getWriter().write("Success");

                } else {
                    getBadRequestHttpResponse(httpServletResponse, "Error. User Already exist");
                }
            } else {
                getBadRequestHttpResponse(httpServletResponse, """
                        Error. Invalid Password.
                        Password must contain 1 number (0-9)
                        Password must contain 1 uppercase letters
                        Password must contain 1 lowercase letters
                        Password must contain 1 non-alpha numeric number
                        Password is 8-16 characters with no space""");
            }
        } catch (Exception e) {
            getBadRequestHttpResponse(httpServletResponse, e.getMessage());
        }
    }


    @Operation(summary = "Deletes an admin user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support")
    })
    @DeleteMapping("/delete")
    @RolesAllowed({ROLE_SYSTEM})
    public void deleteAdminUser(HttpServletResponse httpServletResponse,
                                @Parameter(name = "emailAddress")
                                @RequestParam String emailAddress) throws IOException {
        httpServletResponse.setContentType("text/plain");
        try {
            if (userService.deleteAdminUser(emailAddress)) {
                httpServletResponse.getWriter().write("Success");
            } else {
                getBadRequestHttpResponse(httpServletResponse, "Error. User does not exist");
            }
        } catch (Exception e) {
            getBadRequestHttpResponse(httpServletResponse, e.getMessage());
        }
    }


    @Operation(summary = "Checks the one time password of a registered user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support")
    })
    @PostMapping("/checkOtp")
    public void checkUserOtp(HttpServletResponse httpServletResponse, @Parameter(name = "emailAddress") @RequestParam String emailAddress,
                             @Parameter(name = "otp", description = "one time password")
                             @RequestParam int otp) throws IOException {
        int validationOtp = userService.getUserOtp(emailAddress);
        httpServletResponse.setContentType("text/plain");
        if (otp == validationOtp) {
            httpServletResponse.getWriter().write("Success");
        } else {
            httpServletResponse.getWriter().write("Error. Invalid one time password (otp)");
            httpServletResponse.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        }
    }


    @Operation(summary = "Login and generate token for an admin user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support")
    })
    @PostMapping("/login")
    public void AuthenticateAndGenerateToken(HttpServletResponse
                                                     response, @Parameter(name = "emailAddress") @RequestParam String emailAddress) throws IOException {
        response.setContentType("text/plain");
        try {
            log.info("AuthenticateAndGenerateToken: User [" + emailAddress + "] successfully authenticated!");
            Optional<AdminUser> userOptional = userRepository.findByEmail(emailAddress);
            if (userOptional.isPresent()) {
                boolean result = userVerificationService.generateOtp(userOptional.get());
                if (result) {
                    response.getWriter().write("Success");
                } else {
                    getBadRequestHttpResponse(response, "Error. Generation of one time password failed");
                }
            } else {
                getBadRequestHttpResponse(response, "Error. User does not exist");
            }
        } catch (Exception e) {
            getBadRequestHttpResponse(response, e.getMessage());
        }
    }

    private void getBadRequestHttpResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.getWriter().write(message);
    }
}
