databaseChangeLog:
  - changeSet:
      author: system
      id: 158
      labels: initial
      comment: add table user_login
      changes:
        # hm.user_login
        - createTable:
            catalogName: handball_manager
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: uuid
              - column:
                  constraints:
                    nullable: false
                  name: user_id
                  type: uuid
                  remarks: Reference to user_profile.id
              - column:
                  constraints:
                    nullable: false
                  name: last_login
                  type: date
              - column:
                  name: created_at
                  type: timestamp
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  constraints:
                    nullable: false
                  name: modified_at
                  type: timestamp
                  defaultValueComputed: CURRENT_TIMESTAMP
                  defaultOnNull: true
              - column:
                  name: deleted_at
                  type: timestamp
                  defaultValueDate: "1970-01-01T00:00:00"
                  defaultOnNull: true
              - column:
                  constraints:
                    nullable: false
                  name: deleted
                  type: boolean
                  defaultValueBoolean: false
                  defaultOnNull: true
            remarks: Storing user login information for tracking last login date
            schemaName: hm
            tableName: user_login
        # Create index on user_sso_id for faster lookups
        - createIndex:
            catalogName: handball_manager
            columns:
              - column:
                  name: user_id
            indexName: idx_user_login_user_id
            schemaName: hm
            tableName: user_login
