package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.repository.sportradar.SrCompetitorRepository;
import com.pass.hbl.manager.backend.persistence.util.SportradarConstants;
import org.springframework.stereotype.Component;

@Component
public class HomeCompetitorConverter extends AbstractCompetitorConverter {
    public HomeCompetitorConverter(SrCompetitorRepository repository) {
        super(repository, SportradarConstants.HOME);
    }
}
