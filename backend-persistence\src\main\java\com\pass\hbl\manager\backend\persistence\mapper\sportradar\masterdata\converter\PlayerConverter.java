package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrPlayer;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.repository.sportradar.SrPlayerRepository;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

@Component
public class PlayerConverter extends AbstractConverter<String, SrPlayer> {

    private final SrPlayerRepository playerRepository;

    public PlayerConverter(SrPlayerRepository playerRepository) {
        this.playerRepository = playerRepository;
    }

    @SneakyThrows
    @Override
    protected SrPlayer convert(String source) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        return playerRepository.findById(source).orElseThrow(() -> new EntityNotExistException(SrPlayer.class, source));
    }
}
