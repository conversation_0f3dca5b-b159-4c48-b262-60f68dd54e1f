package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.sportradar.handball.v2.model.SportEvent;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

@Component
public class SportEventLiveConverter extends AbstractConverter<SportEvent, Boolean> {
    @Override
    protected Boolean convert(SportEvent source) {
        return source.getCoverage() == null ? null : source.getCoverage().getLive();
    }
}
