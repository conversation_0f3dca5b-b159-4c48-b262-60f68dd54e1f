package com.pass.hbl.manager.backend.persistence.config;

import liquibase.Scope;
import liquibase.integration.spring.SpringLiquibase;
import liquibase.ui.LoggerUIService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Slf4j
@Configuration
public class LiquibaseConsoleService implements BeanPostProcessor {
    @Override
    public Object postProcessBeforeInitialization(@NotNull Object bean, @NotNull String beanName)
            throws BeansException {
        if (bean instanceof SpringLiquibase) {
            try {
                Scope.enter(Map.of(Scope.Attr.ui.name(), new LoggerUIService()));
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return bean;
    }
}
