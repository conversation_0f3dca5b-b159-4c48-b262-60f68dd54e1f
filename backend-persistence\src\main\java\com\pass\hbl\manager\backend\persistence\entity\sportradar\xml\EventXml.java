package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "Event")
@NoArgsConstructor
@Data
public class EventXml {

    @JacksonXmlProperty(localName = "id", isAttribute = true)
    private Long id;

    @JacksonXmlProperty(localName = "type", isAttribute = true)
    private Integer type;

    @JacksonXmlProperty(localName = "extra", isAttribute = true)
    private Integer extra;

    @JacksonXmlProperty(localName = "outcome", isAttribute = true)
    private Integer outcome;

    @JacksonXmlProperty(localName = "disabled", isAttribute = true)
    private Boolean disabled;

    @JacksonXmlProperty(localName = "Time")
    private TimeXml time;

    @JacksonXmlProperty(localName = "Team")
    private TeamXml team;

    @JacksonXmlProperty(localName = "Player1")
    private Player1Xml player1;

    @JacksonXmlProperty(localName = "Player2")
    private Player2Xml player2;

    @JacksonXmlProperty(localName = "Goalkeeper")
    private GoalkeeperXml goalkeeper;

    @JacksonXmlProperty(localName = "Position")
    private PositionXml position;

    @JacksonXmlProperty(localName = "Score")
    private ScoreXml score;
}
