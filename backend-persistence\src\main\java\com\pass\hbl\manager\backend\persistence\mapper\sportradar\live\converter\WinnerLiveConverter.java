package com.pass.hbl.manager.backend.persistence.mapper.sportradar.live.converter;

import com.pass.hbl.manager.backend.persistence.dto.hm.Winner;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.xml.WinnerXml;
import com.pass.hbl.manager.backend.persistence.exception.MappingException;
import com.pass.hbl.manager.backend.persistence.util.Util;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

@Component
public class WinnerLiveConverter extends AbstractConverter<WinnerXml, Winner> {
    @Override
    protected Winner convert(WinnerXml source) {
        if (source == null || source.getValue() == null) {
            return null;
        }
        try {
            return Winner.getBySrLiveMatchValue(source.getValue());
        } catch (MappingException e) {
            Util.sneakyThrow(e);
            return null;
        }
    }
}
