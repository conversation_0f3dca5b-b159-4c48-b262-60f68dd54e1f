package com.pass.hbl.manager.backend.persistence.mapper.datacore.live;

import com.pass.hbl.manager.backend.persistence.domain.datacore.DcSportEvent;
import com.pass.hbl.manager.backend.persistence.entity.datacore.DcLiveMatchPlayByPlay;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.live.converter.DcLivePlayByPlayScoreConverter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DoToDcLivePlayByPlayMapper extends AbstractMapper<DcSportEvent, DcLiveMatchPlayByPlay> {

    private final DcLivePlayByPlayScoreConverter.HomeTeamScoreConverter homeTeamScoreConverter;
    private final DcLivePlayByPlayScoreConverter.AwayTeamScoreConverter awayTeamScoreConverter;

    public DoToDcLivePlayByPlayMapper(DcLivePlayByPlayScoreConverter.HomeTeamScoreConverter homeTeamScoreConverter, DcLivePlayByPlayScoreConverter.AwayTeamScoreConverter awayTeamScoreConverter) {
        super(DcSportEvent.class, DcLiveMatchPlayByPlay.class);
        this.homeTeamScoreConverter = homeTeamScoreConverter;
        this.awayTeamScoreConverter = awayTeamScoreConverter;
    }

    @Override
    protected void customizeInit() {
        Converter<DcSportEvent.Options, String> toAttackTypeConverter = ctx -> {
            try {
                return ctx.getSource() == null ? null : ctx.getSource().getAttackType();
            } catch (Exception e) {
                log.error("failed to convert " + ctx.getSource() + " to attackType. Reason: " + e);
                return null;
            }
        };

        Converter<DcSportEvent.Options, String> toFailureReasonConverter = ctx -> {
            try {
                return ctx.getSource() == null ? null : ctx.getSource().getFailureReason();
            } catch (Exception e) {
                log.error("failed to convert " + ctx.getSource() + " to failureReason. Reason: " + e);
                return null;
            }
        };

        Converter<DcSportEvent.Options, String> toLocationConverter = ctx -> {
            try {
                return ctx.getSource() == null ? null : ctx.getSource().getLocation();
            } catch (Exception e) {
                log.error("failed to convert " + ctx.getSource() + " to location. Reason: " + e);
                return null;
            }
        };



        TypeMap<DcSportEvent, DcLiveMatchPlayByPlay> typeMap = getOrCreateTypeMap(DcSportEvent.class, DcLiveMatchPlayByPlay.class);
        typeMap.addMappings(mapper -> mapper.using(homeTeamScoreConverter).map(source -> source, DcLiveMatchPlayByPlay::setScoreHome));
        typeMap.addMappings(mapper -> mapper.using(awayTeamScoreConverter).map(source -> source, DcLiveMatchPlayByPlay::setScoreAway));
        typeMap.addMappings(mapper -> mapper.map(DcSportEvent::getPersonId, DcLiveMatchPlayByPlay::setPlayerId));
        typeMap.addMappings(mapper -> mapper.using(toAttackTypeConverter).map(DcSportEvent::getOptions, DcLiveMatchPlayByPlay::setAttackType));
        typeMap.addMappings(mapper -> mapper.using(toFailureReasonConverter).map(DcSportEvent::getOptions, DcLiveMatchPlayByPlay::setFailureReason));
        typeMap.addMappings(mapper -> mapper.using(toLocationConverter).map(DcSportEvent::getOptions, DcLiveMatchPlayByPlay::setLocation));
    }
}
