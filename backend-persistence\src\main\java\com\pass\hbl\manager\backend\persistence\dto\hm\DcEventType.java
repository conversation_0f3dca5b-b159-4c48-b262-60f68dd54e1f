package com.pass.hbl.manager.backend.persistence.dto.hm;

public enum DcEventType {
    ASSIST("assist"),
    TECHNICAL_FAULT("technicalFault"),
    SEVEN_METRE_PENALTY ("sevenMetrePenalty"),
    GOAL("goal"),
    STEAL("steal"),
    REDCARD("redCard"),
    B<PERSON>UE<PERSON>RD("blueCard"),
    SUSPENSION("suspension"),
    BLOCK("block");

    private final String value;

    DcEventType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}