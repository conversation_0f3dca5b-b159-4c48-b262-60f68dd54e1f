package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.UUID;

@JsonRootName("UserAward")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Schema(description = "Award assignment to a user")
public class UserAwardDto extends AbstractDto<UserAwardDto, String> {

    @Schema(description = "Award id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "number of achievements", required = true)
    @Min(1)
    private int numberOfAchievements;

    @Schema(description = "length of the current award series", required = true)
    @Min(0)
    private int currentSeries;

    @Schema(description = "length of the current award series", required = true)
    @Min(0)
    private int longestSeries;

    @Schema(description = "Award of the user", required = true)
    @NotNull
    private AwardDto award;

    /*@Schema(description = "user owning the award", required = true)
    @NotNull
    private UserDto user;*/

    @Schema(description = "user owning the award", required = true)
    @NotNull
    private String userId;

    @Schema(description = "league in which the award is assigned")
    private String leagueId;


    /*@Schema(description = "league in which the award is assigned", required = true)
    @NotNull
    private LeagueInfoDto league;*/
}
