package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;

@JsonRootName("LeagueInvitation")
@Getter
@Setter
@Schema(description = "Invitations for private leagues")
public class LeagueInvitationDto extends AbstractDto<LeagueInvitationDto, String> {

    @Schema(description = "Invitation id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "League Id", required = true)
    @NotBlank
    @Size(max = 32)
    private String leagueId;

    @Schema(description = "Timestamp for expiration", example = "2018-07-14T17:45:55.948353600", required = true)
    @NotNull
    private ZonedDateTime validUntil;

    @JsonCreator
    public LeagueInvitationDto() {
    }

    @JsonCreator
    public LeagueInvitationDto(String leagueId, ZonedDateTime validUntil) {
        this.leagueId = leagueId;
        this.validUntil = validUntil;
    }
}
