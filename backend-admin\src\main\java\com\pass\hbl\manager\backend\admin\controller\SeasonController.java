package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.dto.hm.MachtRoundDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.RoundDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmRound;
import com.pass.hbl.manager.backend.persistence.service.hm.MatchService;
import com.pass.hbl.manager.backend.persistence.service.hm.SeasonService;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@RestController
@RequestMapping(ApiConstants.SEASON_API)
@Validated
@Tag(name = "season", description = "Admin APi for season and round management", externalDocs = @ExternalDocumentation(description = "Further details", url = "https://www.liquimoly-hbl.de/de/liqui-moly-hbl/spielplan/"))
@RolesAllowed({ApiConstants.ROLE_ADMIN})
public class SeasonController {

    private final SeasonService service;
    private final MatchService matchService;

    public SeasonController(SeasonService service, MatchService matchService) {
        this.service = service;
        this.matchService = matchService;
    }

    @Operation(summary = "Get all available round numbers in current season set up by START7")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "All available round numbers in current season", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Available round numbers in current season could not be found")
    })
    @GetMapping(value = "/roundNumber/all", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Integer> getAllUsernames() throws Exception {
        return service.getSortedRoundsCurrentSeason().stream().map(HmRound::getRoundNumber).filter(Objects::nonNull).sorted().toList();
    }

    @Operation(summary = "Get all round details of current season")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "All round details of current season",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "All round details of current season could not be found")
    })
    @GetMapping(value = "/round/all", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<RoundDto> getAllRound() throws Exception {
        return service.getSortedRoundsCurrentSeasonAsDtos();
    }

    @Operation(summary = "Get all Matches")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "All Match By round",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "All Match By round not be found")
    })
    @GetMapping(value = "/round/{roundId}/match/all", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<MachtRoundDto> getAllMatch(@Parameter(name = "roundId", description = "id of the round to leave", required = true)
                                          @PathVariable("roundId")
                                          @NotNull String roundId) {
        return matchService.getAllMatchesByRoundAsDto(UUID.fromString(roundId));
    }
}
