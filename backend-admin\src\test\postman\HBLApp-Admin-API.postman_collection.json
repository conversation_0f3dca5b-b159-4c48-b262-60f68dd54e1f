{"info": {"_postman_id": "7f60c927-4eca-45ad-a800-bfca0a19072c", "name": "HBLApp-Admin-API", "description": "Internal Importer Backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "01 - Test<PERSON><PERSON>e SchedulerController", "item": [{"name": "get All Scheduler Jobs", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/scheduler/jobs", "host": ["{{base_url}}"], "path": ["scheduler", "jobs"]}, "description": "list all scheduler jobs"}, "response": []}, {"name": "refresh Schedule", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/scheduler/jobs/refreshSchedule?interruptIfRunning=false", "host": ["{{base_url}}"], "path": ["scheduler", "jobs", "refreshSchedule"], "query": [{"key": "interruptIfRunning", "value": "false", "description": "(Required) "}]}, "description": "Refresh the schedule of the cron jobs from the database"}, "response": []}, {"name": "find Job By Name", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);\r", "pm.collectionVariables.set(\"job_id\", jsonData[\"id\"]);\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/scheduler/job?name={{job_name}}", "host": ["{{base_url}}"], "path": ["scheduler", "job"], "query": [{"key": "name", "value": "{{job_name}}", "description": "(Required) name of the job"}]}, "description": "Find a job by name"}, "response": []}, {"name": "run Job", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/scheduler/job/run/:id?interruptIfRunning=false", "host": ["{{base_url}}"], "path": ["scheduler", "job", "run", ":id"], "query": [{"key": "interruptIfRunning", "value": "false"}], "variable": [{"key": "id", "value": "{{job_id}}"}]}, "description": "Run a job"}, "response": []}, {"name": "get Running Jobs", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/scheduler/jobs/running", "host": ["{{base_url}}"], "path": ["scheduler", "jobs", "running"]}, "description": "list all running scheduler jobs"}, "response": []}, {"name": "run Job to be stopped", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["setTimeout(function(){}, [12000]);"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/scheduler/job/run/:id?interruptIfRunning=false", "host": ["{{base_url}}"], "path": ["scheduler", "job", "run", ":id"], "query": [{"key": "interruptIfRunning", "value": "false"}], "variable": [{"key": "id", "value": "{{job_id}}"}]}, "description": "Run a job"}, "response": []}, {"name": "stop Job", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/scheduler/job/stop/:id?mode=MANUAL&interruptIfRunning=true", "host": ["{{base_url}}"], "path": ["scheduler", "job", "stop", ":id"], "query": [{"key": "mode", "value": "MANUAL", "description": "(Required) the running mode"}, {"key": "interruptIfRunning", "value": "true", "description": "(Required) if job should be terminated the hard way"}], "variable": [{"key": "id", "value": "{{job_id}}", "description": "(Required) id of the job"}]}, "description": "Stop a running job"}, "response": []}, {"name": "get Job", "event": [{"listen": "test", "script": {"exec": ["pm.collectionVariables.set(\"job\", responseBody);\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/scheduler/job/:id", "host": ["{{base_url}}"], "path": ["scheduler", "job", ":id"], "variable": [{"key": "id", "value": "{{job_id}}", "description": "(Required) id of the job in UUID format"}]}, "description": "Find a job by id"}, "response": []}, {"name": "save", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var body_str = pm.collectionVariables.get(\"job\");\r", "body_str = body_str.replace(\"\\\"cronExpression\\\":null\", \"\\\"cronExpression\\\":\\\"0 0 12 1/1 * ?\\\"\");\r", "pm.collectionVariables.set('request_body', body_str);\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{{request_body}}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/scheduler/job/save?interruptIfRunning=true", "host": ["{{base_url}}"], "path": ["scheduler", "job", "save"], "query": [{"key": "interruptIfRunning", "value": "true", "description": "(Required) if job should be terminated the hard way"}]}, "description": "Save a job"}, "response": []}, {"name": "terminate All Schedules", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/scheduler/jobs/terminateAllSchedules?interruptIfRunning=false", "host": ["{{base_url}}"], "path": ["scheduler", "jobs", "terminateAllSchedules"], "query": [{"key": "interruptIfRunning", "value": "false", "description": "(Required) "}]}, "description": "Refresh the schedule of the cron jobs from the database"}, "response": []}, {"name": "restore job", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var body_str = pm.collectionVariables.get(\"job\");\r", "pm.collectionVariables.set('request_body', body_str);\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{{request_body}}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/scheduler/job/save?interruptIfRunning=true", "host": ["{{base_url}}"], "path": ["scheduler", "job", "save"], "query": [{"key": "interruptIfRunning", "value": "true", "description": "(Required) if job should be terminated the hard way"}]}, "description": "Save a job"}, "response": []}]}, {"name": "02 - <PERSON><PERSON><PERSON><PERSON> ParameterController", "item": [{"name": "get all parameter", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/parameter", "host": ["{{base_url}}"], "path": ["parameter"]}}, "response": []}, {"name": "get known parameter by name", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/parameter/:name", "host": ["{{base_url}}"], "path": ["parameter", ":name"], "variable": [{"key": "name", "value": "SportradarLocale"}]}}, "response": []}, {"name": "get unknown parameter by name", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/parameter/:name?defaultValue=dummy", "host": ["{{base_url}}"], "path": ["parameter", ":name"], "query": [{"key": "defaultValue", "value": "dummy"}], "variable": [{"key": "name", "value": "dummy"}]}}, "response": []}, {"name": "get unknown parameter string value", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/parameter/:name/value", "host": ["{{base_url}}"], "path": ["parameter", ":name", "value"], "variable": [{"key": "name", "value": "dummy"}]}}, "response": []}, {"name": "delete unknown parameter", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/parameter/:name", "host": ["{{base_url}}"], "path": ["parameter", ":name"], "variable": [{"key": "name", "value": "dummy"}]}}, "response": []}, {"name": "set parameter", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/parameter/:name?value=1", "host": ["{{base_url}}"], "path": ["parameter", ":name"], "query": [{"key": "value", "value": "1"}], "variable": [{"key": "name", "value": "dummy"}]}}, "response": []}, {"name": "get unknown parameter int value", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/parameter/:name/valueAsInt", "host": ["{{base_url}}"], "path": ["parameter", ":name", "valueAsInt"], "variable": [{"key": "name", "value": "dummy"}]}}, "response": []}, {"name": "delete parameter", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/parameter/:name", "host": ["{{base_url}}"], "path": ["parameter", ":name"], "variable": [{"key": "name", "value": "dummy"}]}}, "response": []}]}, {"name": "03 - <PERSON><PERSON><PERSON><PERSON> LogMessageController", "item": [{"name": "get all logs (one page)", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);\r", "pm.collectionVariables.set(\"log_id\", jsonData[0].id)\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/log?page=0&size=10", "host": ["{{base_url}}"], "path": ["log"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "get by id", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/log/:id", "host": ["{{base_url}}"], "path": ["log", ":id"], "variable": [{"key": "id", "value": "{{log_id}}"}]}}, "response": []}, {"name": "get HelloWorldJob logs (one page)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/log?page=0&size=10&processName=HelloWorldJob", "host": ["{{base_url}}"], "path": ["log"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}, {"key": "processName", "value": "HelloWorldJob"}]}}, "response": []}, {"name": "get admin/logs logs (one page)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/log?page=0&size=10&processName=%5B1000%5D%25admin%2Flog", "host": ["{{base_url}}"], "path": ["log"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}, {"key": "processName", "value": "%5B1000%5D%25admin%2Flog"}]}}, "response": []}, {"name": "get HelloWorldJob errors (one page)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/log?page=0&size=10&processName=HelloWorldJob&level=ERROR", "host": ["{{base_url}}"], "path": ["log"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}, {"key": "processName", "value": "HelloWorldJob"}, {"key": "level", "value": "ERROR"}]}}, "response": []}, {"name": "get Hello<PERSON>orld<PERSON>ob errors with start date (one page)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/log?page=0&size=10&processName=HelloWorldJob&level=ERROR&start=2022-02-24T13:47", "host": ["{{base_url}}"], "path": ["log"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}, {"key": "processName", "value": "HelloWorldJob"}, {"key": "level", "value": "ERROR"}, {"key": "start", "value": "2022-02-24T13:47"}]}}, "response": []}, {"name": "get HelloWorld<PERSON>ob errors with end date (one page)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/log?page=0&size=10&processName=HelloWorldJob&level=ERROR&end=2022-02-24T13:47", "host": ["{{base_url}}"], "path": ["log"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}, {"key": "processName", "value": "HelloWorldJob"}, {"key": "level", "value": "ERROR"}, {"key": "end", "value": "2022-02-24T13:47"}]}}, "response": []}, {"name": "get HelloWorld<PERSON>ob errors with between date (one page)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/log?page=0&size=10&processName=HelloWorldJob&level=ERROR&end=2022-02-24T13:47&start=2022-02-24T13:46", "host": ["{{base_url}}"], "path": ["log"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}, {"key": "processName", "value": "HelloWorldJob"}, {"key": "level", "value": "ERROR"}, {"key": "end", "value": "2022-02-24T13:47"}, {"key": "start", "value": "2022-02-24T13:46"}]}}, "response": []}, {"name": "get log levels", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/log/levels", "host": ["{{base_url}}"], "path": ["log", "levels"]}}, "response": []}]}, {"name": "04 - <PERSON><PERSON><PERSON><PERSON> ExternalDataMapping", "item": [{"name": "get all", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/externalMapping?page=0&size=10", "host": ["{{base_url}}"], "path": ["externalMapping"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "get datasources", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/externalMapping/datasource", "host": ["{{base_url}}"], "path": ["externalMapping", "datasource"]}}, "response": []}, {"name": "get entities", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/externalMapping/entities", "host": ["{{base_url}}"], "path": ["externalMapping", "entities"]}}, "response": []}]}], "auth": {"type": "basic", "basic": [{"key": "password", "value": "{{admin_password}}", "type": "string"}, {"key": "username", "value": "{{admin_username}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["pm.request.addHeader({ key: 'Host', value: '{{host}}' })"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "job_name", "value": "HelloWorldJob", "type": "string"}, {"key": "job_id", "value": "", "type": "string"}, {"key": "job", "value": "", "type": "string"}, {"key": "request_body", "value": "", "type": "string"}, {"key": "log_id", "value": "", "type": "string"}]}