package com.pass.hbl.manager.backend.persistence.entity.sportradar;

import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Objects;

@MappedSuperclass
@Setter
@Getter
public class AbstractSportradarEntity {

    static final LocalDateTime ZERO_DATE = LocalDateTime.of(1970,1,1,0,0,0);

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "modified_at")
    private LocalDateTime modifiedAt;


    public AbstractSportradarEntity() {
        this.modifiedAt = LocalDateTime.now();
        checkDates();
    }

    @PrePersist
    protected void onCreate() {
        if (this.modifiedAt == null) {
            this.modifiedAt = LocalDateTime.now();
        }
        checkDates();
        preCreate();
    }

    protected void preCreate() {
        // wait for override
    }

    @PreUpdate
    protected void onPersist() {
        this.modifiedAt = LocalDateTime.now();
        checkDates();
        preUpdate();
    }

    protected void preUpdate() {
        // wait for override
    }

    @PostLoad
    protected void afterLoad() {
        if (Objects.equals(this.modifiedAt, ZERO_DATE)) {
            this.modifiedAt = null;
        }
        postLoad();
    }

    protected void postLoad() {
        // wait for override
    }

    public ExternalEntity getExternalEntity() {
        throw new RuntimeException("Trying to get external entity type on a non mappable object");
    }


    private void checkDates() {
        if (this.modifiedAt == null) {
            this.modifiedAt = ZERO_DATE;
        }
    }

    @Override
    @SuppressWarnings({"EqualsWhichDoesntCheckParameterClass", "EqualsDoesntCheckParameterClass", "com.haulmont.jpb.EqualsDoesntCheckParameterClass"})
    public boolean equals(Object o) {
        return EqualsBuilder.reflectionEquals(this, o, "modifiedAt");
    }

    @Override
    public int hashCode() {
        return HashCodeBuilder.reflectionHashCode(this, "modifiedAt");
    }
}
