package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;

@JsonRootName("Match")
@Getter
@Setter
@Schema(description = "Match transfer object")
public class MatchDto extends AbstractDto<MatchDto, String> {

    @Schema(description = "Match id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "The round number of this match", example = "1", required = true)
    @NotNull
    @Min(1)
    private Integer round;

    @Schema(description = "The status of this match", example = "NOT_STARTED", required = true)
    @NotNull
    private MatchStatus matchStatus;

    @Schema(description = "Time the match starts", example = "2022-07-21T15:30:00.000")
    private ZonedDateTime startTime;

    @Schema(description = "The current time in the match in case it is live in format [mm:ss]", example = "43:23")
    private String matchTime;

    @Schema(description = "The home team")
    @NotNull
    private ClubDto home;

    @Schema(description = "The away team")
    @NotNull
    private ClubDto away;

    @Schema(description = "the current or final score of the home team", example = "10", required = true)
    @Min(0)
    private Integer homeScore;

    @Schema(description = "the current or final score of the away team", example = "10", required = true)
    @Min(0)
    private Integer awayScore;

    @Schema(description = "the winner of the match", example = "AWAY", required = true)
    @NotNull
    private Winner winner;
}
