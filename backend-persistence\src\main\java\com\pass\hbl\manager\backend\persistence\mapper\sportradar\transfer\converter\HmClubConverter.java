package com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminExternalDataMapping;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmClub;
import com.pass.hbl.manager.backend.persistence.repository.admin.AdminExternalDataMappingRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmClubRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ExternalDataMappingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
public class HmClubConverter extends AbstractConverter<String, HmClub> {

    private final ExternalDataMappingService externalDataMappingService;

    private final HmClubRepository clubRepository;

    public HmClubConverter(ExternalDataMappingService externalDataMappingService, HmClubRepository clubRepository) {
        this.externalDataMappingService = externalDataMappingService;
        this.clubRepository = clubRepository;
    }

    @Override
    protected HmClub convert(String source) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        Optional<HmClub> club = externalDataMappingService.get(Datasource.DATACORE, ExternalEntity.COMPETITOR, source)
                .map(AdminExternalDataMapping::getHmId)
                .map(clubRepository::findById)
                .filter(Optional::isPresent)
                .map(Optional::get);
        if (club.isEmpty()) {
            log.warn("Unknown club with SrId=" + source);
            return null;
        }
        return club.get();
    }
}
