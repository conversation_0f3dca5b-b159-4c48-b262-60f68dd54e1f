package com.pass.hbl.manager.backend.persistence.repository.admin;

import com.pass.hbl.manager.backend.persistence.entity.admin.AdminSchedulerJob;
import com.pass.hbl.manager.backend.persistence.repository.AbstractSchedulerJobRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;
import java.util.UUID;

public interface AdminSchedulerJobRepository extends AbstractSchedulerJobRepository<AdminSchedulerJob> {

    @Query(value = "SELECT * FROM admin.scheduler_job s WHERE s.name = :jobName limit 1", nativeQuery = true)
    Optional<AdminSchedulerJob> findFirstByNameIncludingDeleted(String jobName);

    @Query(value = "SELECT * from admin.scheduler_job s WHERE id = :id", nativeQuery = true)
    Optional<AdminSchedulerJob> findByIdAndDeletedIgnored(@Param("id") UUID id);
}
