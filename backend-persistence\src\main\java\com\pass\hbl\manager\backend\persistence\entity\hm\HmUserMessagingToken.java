package com.pass.hbl.manager.backend.persistence.entity.hm;


import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.UUID;

@Table(name = "user_messaging_token", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@Entity
@NoArgsConstructor
@ToString
@SQLDelete(sql = "UPDATE hm.user_messaging_token SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmUserMessagingToken extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id", updatable = false)
    private HmUserProfile user;

    @NotBlank
    @Size(max = 4096)
    @Column(name = "token")
    private String token;


    public HmUserMessagingToken(HmUserProfile user, String token) {
        this.user = user;
        this.token = token;
    }
}
