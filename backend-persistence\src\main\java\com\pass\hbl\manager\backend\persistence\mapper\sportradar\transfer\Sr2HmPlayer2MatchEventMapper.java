package com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer;

import com.pass.hbl.manager.backend.persistence.dto.hm.EventScope;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.HmMatchConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.HmPlayerConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.IdConverter;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmHpiCalculationRepository;
import org.springframework.stereotype.Component;

@Component
public class Sr2HmPlayer2MatchEventMapper extends Sr2HmPlayerMatchEventMapper {
    public Sr2HmPlayer2MatchEventMapper(IdConverter idConverter,
                                        HmMatchConverter matchConverter,
                                        HmPlayerConverter playerConverter,
                                        HmHpiCalculationRepository hpiCalculationRepository) {
        super(idConverter, EventScope.PLAYER_2, matchConverter, playerConverter, hpiCalculationRepository);
    }
}
