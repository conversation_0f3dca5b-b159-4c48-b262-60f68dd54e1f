package com.pass.hbl.manager.backend.persistence.domain.hm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@NoArgsConstructor
@AllArgsConstructor
public class HmLeagueMemberAwardsDO {

    @Getter
    private UUID userId;

    @Getter
    private UUID leagueId;

    @Getter
    private String leagueName;

    @Getter
    private int experiencePointsByAwards;

    @Getter
    private List<HmAwardDescriptionDO> awardDescriptions;
}
