package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.UserDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static com.pass.hbl.manager.backend.persistence.util.Constants.TRIAL_SUBSCRIPTION_60_DAYS;
import static java.util.Objects.isNull;

@Component
public class HmUserMapper extends AbstractMapper<HmUserProfile, UserDto> {

    public HmUserMapper() {
        super(HmUserProfile.class, UserDto.class);
    }

    @Override
    protected UserDto customizeMapToDto(UserDto userDto, HmUserProfile userProfile) {
        if (userProfile == null) {
            return null;
        }
        userDto.setTrialSubscriptionDaysLeft(getTrialSubscriptionDaysLeft(userProfile));
        userDto.setManualSubscriptionDaysLeft(getManualSubscriptionDaysLeft(userProfile));
        userDto.anonymize();
        return userDto;
    }

    private Integer getTrialSubscriptionDaysLeft(HmUserProfile user) {
        LocalDateTime registeredSince = user.getRegisteredSince();
        if (isNull(registeredSince)) return null;
        LocalDate trialSubscriptionUntil;
        if (registeredSince.toLocalDate().isAfter(TRIAL_SUBSCRIPTION_REFERENCE_DATE)) {
            trialSubscriptionUntil = registeredSince.toLocalDate().plusDays(TRIAL_SUBSCRIPTION_30_DAYS);
        } else {
            trialSubscriptionUntil = registeredSince.toLocalDate().plusDays(TRIAL_SUBSCRIPTION_60_DAYS);
        }
        LocalDate today = LocalDateTime.now().toLocalDate();
        if (trialSubscriptionUntil.isBefore(today)) return null;
        long daysBetween = ChronoUnit.DAYS.between(today, trialSubscriptionUntil);
        return Long.valueOf(daysBetween).intValue();
    }

    private Integer getManualSubscriptionDaysLeft(HmUserProfile user) {
        LocalDateTime premiumExpiration = user.getPremiumExpiration();
        if (!user.isManualSubscription() || isNull(premiumExpiration)) return null;
        LocalDate today = LocalDate.now();
        LocalDate premiumExpirationDate = premiumExpiration.toLocalDate();
        if (premiumExpirationDate.isAfter(today)) {
            long daysBetween = ChronoUnit.DAYS.between(today, premiumExpirationDate);
            return Long.valueOf(daysBetween).intValue();
        } else {
            return null;
        }
    }
}
