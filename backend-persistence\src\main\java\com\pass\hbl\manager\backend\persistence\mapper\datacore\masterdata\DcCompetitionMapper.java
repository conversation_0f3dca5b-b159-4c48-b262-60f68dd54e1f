package com.pass.hbl.manager.backend.persistence.mapper.datacore.masterdata;

import com.pass.hbl.manager.backend.persistence.entity.datacore.DcCompetition;
import com.pass.hbl.manager.backend.persistence.entity.datacore.DcSeason;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.sportradar.datacore.rest.model.CompetitionsModel;
import com.sportradar.datacore.rest.model.SeasonsModel;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

@Component
public class DcCompetitionMapper extends AbstractMapper<DcCompetition, CompetitionsModel> {

    public DcCompetitionMapper() {
        super(DcCompetition.class, CompetitionsModel.class);
    }

    @Override
    protected void customizeInit() {
        TypeMap<CompetitionsModel, DcCompetition> typeMap = getOrCreateTypeMap(CompetitionsModel.class, DcCompetition.class);
        typeMap.addMappings(mapper -> mapper.map(CompetitionsModel::getCompetitionId, DcCompetition::setId));
        typeMap.addMappings(mapper -> mapper.map(CompetitionsModel::getNameLocal, DcCompetition::setName));
    }
}
