package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;

@JsonRootName("UserRegistration")
@Getter
@Setter
@Schema(description = "Reduced user info object for listing users with basic attributes")
public class UserRegistrationDto extends AbstractDto<UserRegistrationDto, String> {

    @Schema(description = "User id is registered with", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "Email key", example = "b76dea8b75373ddb8cb6faad78b2f6f0", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String emailKey;

    @Schema(description = "timestamp how log unconfirmed registration is valid", example = "2022-02-24T13:47:00.000000", required = true)
    @NotNull
    private ZonedDateTime validUntil;

    @JsonCreator
    public UserRegistrationDto() {
    }

    @JsonCreator
    public UserRegistrationDto(String id, String emailKey, ZonedDateTime validUntil) {
        this.id = id;
        this.emailKey = emailKey;
        this.validUntil = validUntil;
    }
}
