databaseChangeLog:
  - changeSet:
      author: system
      id: 176
      labels: initial
      comment: add table subscription info
      changes:
        - createTable:
            catalogName: handball_manager
            schemaName: hm
            tableName: user_subscription
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: uuid
              - column:
                  constraints:
                    nullable: false
                    foreignKeyName: fk_user_subscription_user
                    references: hm.user_profile(id)
                  name: user_id
                  type: uuid
              - column:
                  constraints:
                    nullable: false
                  name: subscription_id
                  type: varchar(255)
              - column:
                  constraints:
                    nullable: false
                  name: subscription_type
                  type: varchar(50)
              - column:
                  constraints:
                    nullable: false
                  name: subscription_period
                  type: varchar(50)
              - column:
                  constraints:
                    nullable: false
                  name: start_date
                  type: timestamp
              - column:
                  name: end_date
                  type: timestamp
              - column:
                  name: last_payment_amount
                  type: decimal(10,2)
              - column:
                  name: last_payment_date
                  type: timestamp
              - column:
                  name: last_payment_id
                  type: varchar(255)
              - column:
                  name: platform
                  type: varchar(50)
              - column:
                  constraints:
                    nullable: false
                  name: created_at
                  type: timestamp
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  constraints:
                    nullable: false
                  name: modified_at
                  type: timestamp
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  constraints:
                    nullable: false
                  name: deleted
                  type: boolean
                  defaultValueBoolean: false
              - column:
                  name: deleted_at
                  type: timestamp