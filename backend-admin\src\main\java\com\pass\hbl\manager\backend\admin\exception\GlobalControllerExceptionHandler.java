package com.pass.hbl.manager.backend.admin.exception;

import com.pass.hbl.manager.backend.persistence.dto.exception.ErrorMessage;
import com.pass.hbl.manager.backend.persistence.exception.*;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.ConversionFailedException;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;

import javax.validation.ConstraintViolationException;

@RestControllerAdvice
@Slf4j
public class GlobalControllerExceptionHandler {

    private final LogMessageService logMessageService;

    public GlobalControllerExceptionHandler(LogMessageService logMessageService) {
        this.logMessageService = logMessageService;
    }

    @ExceptionHandler({ConstraintViolationException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ApiResponse(responseCode = "400", description = "Error converting objects", content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = ErrorMessage.class)))
    public ResponseEntity<ErrorMessage> handleValidationException(Exception ex, WebRequest request) {
        CodedException e = new CodedException(ExceptionCode.VALIDATION, ex.getMessage(), ex);
        log(e, request);
        return new ResponseEntity<>(ErrorMessage.produce(HttpStatus.BAD_REQUEST, e), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler({ConversionFailedException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ApiResponse(responseCode = "400", description = "Error converting objects", content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = ErrorMessage.class)))
    public ResponseEntity<ErrorMessage> handleConversion(Exception ex, WebRequest request) {
        CodedException e = new CodedException(ExceptionCode.CONVERSION, ex.getMessage(), ex);
        log(e, request);
        return new ResponseEntity<>(ErrorMessage.produce(HttpStatus.BAD_REQUEST, e), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler({FormatException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ApiResponse(responseCode = "400", description = "Format exception", content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = ErrorMessage.class)))
    public ResponseEntity<ErrorMessage> handleFormatException(Exception ex, WebRequest request) {
        log(ex, request);
        return new ResponseEntity<>(ErrorMessage.produce(HttpStatus.BAD_REQUEST, ex), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler({SchedulingException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ApiResponse(responseCode = "500", description = "Scheduling problems", content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = ErrorMessage.class)))
    public ResponseEntity<ErrorMessage> handleSchedulerException(Exception ex, WebRequest request) {
        log(ex, request);
        return new ResponseEntity<>(ErrorMessage.produce(HttpStatus.INTERNAL_SERVER_ERROR, ex), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler({EntityNotExistException.class})
    @ResponseStatus(HttpStatus.NOT_FOUND)
    @ApiResponse(responseCode = "404", description = "Scheduling problems", content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = ErrorMessage.class)))
    public ResponseEntity<ErrorMessage> handleEntityNotExistException(Exception ex, WebRequest request) {
        log(ex, request);
        return new ResponseEntity<>(ErrorMessage.produce(HttpStatus.NOT_FOUND, ex), HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler({Exception.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ApiResponse(responseCode = "500", description = "Unexpected exception in catch all block", content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = ErrorMessage.class)))
    public ResponseEntity<ErrorMessage> handleCatchAll(Exception ex, WebRequest request) {
        log(ex, request);
        return new ResponseEntity<>(ErrorMessage.produce(HttpStatus.INTERNAL_SERVER_ERROR, ex), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private void log(Exception e, WebRequest request) {
        if (e instanceof CodedException ce) {
            logMessageService.logException("["+ ce.getCode().getCode() + "]: " + ((ServletWebRequest) request).getRequest().getRequestURL().toString(), e);
        } else {
            logMessageService.logException(((ServletWebRequest) request).getRequest().getRequestURL().toString(), e);
        }
        log.error(((ServletWebRequest) request).getRequest().getRequestURL().toString() + " -> " + e.getMessage(), e);
    }
}
