package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.util.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;
import java.util.Map;

@JsonRootName("UserProfile")
@Getter
@Setter
@Schema(description = "Reduced user info object for listing users with basic attributes")
public class UserProfileDto extends AbstractPictureDto<UserProfileDto, String> {

    @Schema(description = "User id is registered with", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "Username user is registered with", example = "john-doe", required = true)
    @NotBlank
    @Size(max = 256)
    private String username;

    @Size(max = 256)
    @Schema(description = "Optional last name of user")
    private String lastName;

    @Size(max = 256)
    @Schema(description = "Optional first name of user")
    private String firstName;

    @Schema(description = "Mail address user is registered with", example = "<EMAIL>", required = true)
    @Email(message = "Email address")
    @NotBlank
    @Size(max = 256)
    private String emailAddress;

    @NotNull
    @Schema(description = "Level of the player")
    private int level;

    @NotNull
    @Schema(description = "experience points of the player")
    private Integer experiencePoints;

    @Schema(description = "Optional map of session attributes")
    private Map<SessionAttribute, String> sessionAttributes;

    @NotNull
    @Schema(description = "Whether mail address is validated", example = "false", required = true)
    private boolean emailValidated;
    @NotNull
    @Schema(description = "Timestamp of user registration", example = "2018-07-14T17:45:55.948353600", required = true)
    private ZonedDateTime registeredSince;

    @NotNull
    @Schema(description = "True if user is premium user", example = "true", required = true, defaultValue = "false")
    private boolean premium;

    @Schema(description = "If user is premium user, timestamp when premium membership expires", example = "2018-07-14T17:45:55.948353600")
    private ZonedDateTime premiumExpiration;

    @JsonCreator
    public UserProfileDto() {
    }

    @JsonCreator
    public UserProfileDto(String username, String emailAddress) {
        this.username = username;
        this.emailAddress = emailAddress;
        this.registeredSince = ZonedDateTime.now();
        this.experiencePoints = 0;
        this.level = 1;
        this.premium = false;
    }


    public UserProfileDto(String id, String username, String firstName, String lastName, String emailAddress, boolean emailValidated) {
        this.id = id;
        this.username = username;
        this.lastName = lastName;
        this.firstName = firstName;
        this.emailAddress = emailAddress;
        this.emailValidated = emailValidated;
        this.registeredSince = ZonedDateTime.now();
        this.experiencePoints = 0;
        this.level = 1;
        this.premium = false;
    }

    public static UserProfileDto getAnonymous() {
        return new UserProfileDto(Constants.ANONYMOUS_ID, Constants.ANONYMOUS_USERNAME,null, null,
                Constants.ANONYMOUS_EMAIL, true);
    }
}
