package com.pass.hbl.manager.backend.persistence.mapper.datacore.transfer.converter;

import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.repository.admin.AdminExternalDataMappingRepository;
import com.pass.hbl.manager.backend.persistence.service.datacore.streaming.DataCoreMappingService;
import org.springframework.stereotype.Component;

@Component
public class DcPlayerIdConverter extends CustomDataCoreIdConverter {

    public DcPlayerIdConverter(AdminExternalDataMappingRepository repository, DataCoreMappingService dataCoreMappingService) {
        super(repository, dataCoreMappingService, ExternalEntity.PLAYER);
    }
}


