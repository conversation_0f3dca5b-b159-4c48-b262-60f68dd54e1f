package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.LeagueInvitationDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeagueInvitation;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.HmEntityToStringConverter;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.StringToHmLeagueConverter;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueRepository;
import org.springframework.stereotype.Component;

@Component
public class HmLeagueInvitationMapper extends AbstractMapper<HmLeagueInvitation, LeagueInvitationDto> {

    private final HmLeagueRepository hmLeagueRepository;

    public HmLeagueInvitationMapper(HmLeagueRepository hmLeagueRepository) {
        super(HmLeagueInvitation.class, LeagueInvitationDto.class);
        this.hmLeagueRepository = hmLeagueRepository;
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected void customizeInit() {
        getModelMapper().addConverter(new StringToHmLeagueConverter(hmLeagueRepository));
        getModelMapper().addConverter(new HmEntityToStringConverter<HmLeague>());
    }
}
