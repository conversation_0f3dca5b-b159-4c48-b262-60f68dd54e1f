package com.pass.hbl.manager.backend.persistence.mapper.datacore.transfer;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.AbstractSportradarEntity;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.transfer.converter.CustomDataCoreIdConverter;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.transfer.converter.DataCoreIdConverter;
import org.modelmapper.TypeMap;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

public abstract class AbstractDc2HmMapper<DcType extends AbstractSportradarEntity, HmType extends AbstractEntity> extends AbstractMapper<DcType, HmType> {

    @Autowired
    private final DataCoreIdConverter idConverter;

    @Autowired
    private final CustomDataCoreIdConverter customDataCoreIdConverter;

    public AbstractDc2HmMapper(Class<DcType> dcTypeClass, Class<HmType> hmTypeClass, DataCoreIdConverter idConverter, CustomDataCoreIdConverter customDataCoreIdConverter) {
        this(dcTypeClass, hmTypeClass, idConverter, true, customDataCoreIdConverter);
    }

    public AbstractDc2HmMapper(Class<DcType> srTypeClass, Class<HmType> hmTypeClass, DataCoreIdConverter idConverter, boolean registerConverters, CustomDataCoreIdConverter customDataCoreIdConverter) {
        super(srTypeClass, hmTypeClass, registerConverters);
        this.idConverter = idConverter;
        this.customDataCoreIdConverter = customDataCoreIdConverter;
        TypeMap<DcType, HmType> typeMap = getOrCreateTypeMap(srTypeClass, hmTypeClass);
        if (Objects.isNull(idConverter)) {
            typeMap.addMappings(mapper -> mapper.using(customDataCoreIdConverter).map(AbstractSportradarEntity::getId, AbstractEntity::setId));
        } else {
            typeMap.addMappings(mapper -> mapper.using(idConverter).map(AbstractSportradarEntity::getId, AbstractEntity::setId));
        }
    }


    @SuppressWarnings("DuplicatedCode")
    @Override
    protected <Source, Dest> TypeMap<Source, Dest> getOrCreateTypeMap(Class<Source> source, Class<Dest> dest) {
        TypeMap<Source, Dest> map = super.getOrCreateTypeMap(source, dest);
        @SuppressWarnings("unchecked")
        TypeMap<DcType, HmType> typeMap = (TypeMap<DcType, HmType>) map;
        typeMap.addMappings(mapper -> mapper.skip(HmType::setModified));
        typeMap.addMappings(mapper -> mapper.skip(HmType::setModifiedAt));
        typeMap.addMappings(mapper -> mapper.skip(HmType::setCreatedAt));
        typeMap.addMappings(mapper -> mapper.skip(HmType::setDeleted));
        typeMap.addMappings(mapper -> mapper.skip(HmType::setDeletedAt));
        return map;
    }
}
