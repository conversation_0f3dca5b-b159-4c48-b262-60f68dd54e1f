package com.pass.hbl.manager.backend.persistence.mapper.datacore.masterdata.converter;

import com.sportradar.datacore.rest.model.MatchCompetitor;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

public abstract class DcScoreCompetitorConverter extends AbstractConverter<List<MatchCompetitor>, Integer> {

    private final boolean isHome;

    public DcScoreCompetitorConverter(boolean isHome) {
        this.isHome = isHome;
    }

    @Override
    protected Integer convert(List<MatchCompetitor> matchCompetitors) {

        if (CollectionUtils.isEmpty(matchCompetitors)) {
            return null;
        }
        return matchCompetitors.stream()
                .filter(competitor -> Objects.equals(competitor.getIsHome(), isHome))
                .findFirst()
                .map(competitor -> competitor.getScore() != null ? Integer.parseInt(competitor.getScore()) : null)
                .orElse(null);
    }

    @Component
    public static class HomeDcScoreCompetitorConverter extends DcScoreCompetitorConverter {
        public HomeDcScoreCompetitorConverter() {
            super(true);
        }
    }

    @Component
    public static class AwayDcScoreCompetitorConverter extends DcScoreCompetitorConverter {
        public AwayDcScoreCompetitorConverter() {
            super(false);
        }
    }
}
