package com.pass.hbl.manager.backend.persistence.domain.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.AwardCode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Locale;
import java.util.UUID;

@NoArgsConstructor
@AllArgsConstructor
public class HmAwardDescriptionDO {

    @Getter
    private AwardCode awardCode;

    @Getter
    private String description;

    @Getter
    private UUID awardPicture;

    @Getter
    private Locale locale;

    @Getter
    private Integer money;
}
