package com.pass.hbl.manager.backend.persistence.service.admin;

import com.pass.hbl.manager.backend.persistence.dto.admin.AdminUserDto;
import com.pass.hbl.manager.backend.persistence.dto.admin.UserRole;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminUser;
import com.pass.hbl.manager.backend.persistence.mapper.admin.AdminUserMapper;
import com.pass.hbl.manager.backend.persistence.repository.admin.AdminUserRepository;
import com.pass.hbl.manager.backend.persistence.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Transactional
@Slf4j
public class AdminUserService {

    private final AdminUserRepository repository;

    private final PasswordEncoder encoder = new BCryptPasswordEncoder();

    private final AdminUserMapper mapper;

    private volatile AdminUser systemUser;

    private final Object lock = new Object();

    public AdminUserService(AdminUserRepository repository, AdminUserMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void createSystemUser() {
        if (!repository.existsByEmailAddressIgnoreCase(Constants.SYSTEM_EMAIL)) {
            log.info("System admin user (bank) does not exist. Will create it.");
            AdminUser system = new AdminUser(Constants.SYSTEM_USERNAME, Constants.SYSTEM_PASSWORD, Constants.SYSTEM_EMAIL, List.of(UserRole.values()));
            system.setPassword(encoder.encode(system.getPassword()));
            repository.save(system);
        }
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<AdminUserDto> getAllAdminUser(Pageable pageable, String adminUser) {
        Pageable sortedPageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by("username").ascending());
        if (adminUser == null || adminUser.isEmpty()) {
            return repository.findAll(pageable).map(mapper::mapToDto).toList();
        } else {
            return repository.findAllByUsernameContainingIgnoreCaseOrEmailAddressContainingIgnoreCase(adminUser, adminUser, sortedPageable).map(mapper::mapToDto).toList();
        }
    }

    public void changePassword(String emailAddress, String newPassword) {
        Optional<AdminUser> adminUserOptional = repository.findByEmail(emailAddress);
        adminUserOptional.ifPresentOrElse(adminUser -> {
            adminUser.setPassword(encoder.encode(newPassword));
            repository.save(adminUser);
        }, () -> log.error("Admin User with emailAddress[" + emailAddress + "] not found"));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public AdminUser getSystemUser() {
        if (systemUser == null) {
            synchronized (lock) {
                if (systemUser == null) {
                    systemUser = repository.findByEmailAddressIgnoreCase(Constants.SYSTEM_EMAIL)
                            .orElseThrow(() -> new RuntimeException("Fatal: no system user exists in admin.user table (email=" + Constants.SYSTEM_EMAIL + ". Create it, otherwise system will not start"));
                }
            }
        }
        return systemUser;
    }

    public boolean validatePassword(String password) {
        // Compile regular expression
        final Pattern pattern = Pattern.compile("^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s).{8,16}$");
        // Match regex against input
        final Matcher matcher = pattern.matcher(password);
        return matcher.matches();
    }

    public boolean addAdminUser(AdminUser user) {
        Optional<AdminUser> tempUser = repository.findByEmail(user.getEmailAddress());
        if (tempUser.isPresent()) {
            return false;
        } else {
            user.setPassword(encoder.encode(user.getPassword()));
            repository.save(user);
            return true;
        }
    }

    public boolean deleteAdminUser(String emailAddress) {
        Optional<AdminUser> userOptional = repository.findByEmail(emailAddress);
        if (userOptional.isPresent()) {
            repository.delete(userOptional.get());
            return true;
        } else {
            return false;
        }
    }

    public int getUserOtp(String email) {
        Optional<AdminUser> user = repository.findByEmail(email);
        if (user.isPresent()) {
            return user.get().getOtp();
        } else {
            return 0;
        }
    }
}
