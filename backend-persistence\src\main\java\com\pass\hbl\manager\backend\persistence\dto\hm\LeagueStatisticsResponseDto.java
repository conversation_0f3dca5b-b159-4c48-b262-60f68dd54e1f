package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * League statistics response data transfer object
 * Used for the optimized league statistics API endpoint
 * Contains totalLeagueCount at the top level and a list of league items
 */
@JsonRootName("LeagueStatistics")
@Getter
@Setter
@Schema(description = "League statistics response data")
public class LeagueStatisticsResponseDto {

    @Schema(description = "Total league count (cached)", example = "9851", required = true)
    @Min(0)
    private int totalLeagueCount;

    @Schema(description = "List of leagues", required = true)
    @NotNull
    @Valid
    private List<LeagueStatisticsItemDto> leagues;

    @JsonCreator
    public LeagueStatisticsResponseDto() {
    }

    public LeagueStatisticsResponseDto(int totalLeagueCount, List<LeagueStatisticsItemDto> leagues) {
        this.totalLeagueCount = totalLeagueCount;
        this.leagues = leagues;
    }
}
