package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrMatchPlayerStatistics;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.SrMatchPlayerStatisticsMapper;
import com.pass.hbl.manager.backend.persistence.repository.sportradar.SrMatchPlayerStatisticsRepository;
import com.pass.hbl.manager.backend.persistence.repository.sportradar.SrPlayerRepository;
import com.sportradar.handball.v2.model.*;
import lombok.SneakyThrows;
import org.modelmapper.Converter;
import org.modelmapper.spi.MappingContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class PlayerStatisticsConverter implements Converter<InlineResponse20016Statistics, List<SrMatchPlayerStatistics>> {

    private final SrPlayerRepository playerRepository;
    private final SrMatchPlayerStatisticsRepository playerStatisticsRepository;
    private final SrMatchPlayerStatisticsMapper statisticsMapper;

    public PlayerStatisticsConverter(SrPlayerRepository playerRepository,
                                     SrMatchPlayerStatisticsRepository playerStatisticsRepository,
                                     SrMatchPlayerStatisticsMapper statisticsMapper) {
        this.playerRepository = playerRepository;
        this.playerStatisticsRepository = playerStatisticsRepository;
        this.statisticsMapper = statisticsMapper;
    }

    @Override
    public List<SrMatchPlayerStatistics> convert(MappingContext<InlineResponse20016Statistics, List<SrMatchPlayerStatistics>> context) {

        InlineResponse20016Statistics source = context.getSource();
        SportEvent sportEvent = ((Summary) context.getParent().getSource()).getSportEvent();
        if (source == null || source.getTotals() == null ||  CollectionUtils.isEmpty(source.getTotals().getCompetitors())) {
            return Collections.emptyList();
        }

        return source.getTotals().getCompetitors().stream()
                .map(SportEventStatisticsTotalsCompetitors::getPlayers)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(s -> convert(sportEvent, s))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @SneakyThrows
    private SrMatchPlayerStatistics convert(@NotNull SportEvent sportEvent, @NotNull SportEventPlayerStatistics source) {
        Objects.requireNonNull(source.getId(), "Missing player id in sportradar statistics object");
        Objects.requireNonNull(sportEvent.getId(), "Missing sportEvent id in sportradar statistics object");
        SrMatchPlayerStatistics existing = playerStatisticsRepository
                .findFirstByMatchIdAndPlayerId(sportEvent.getId(), source.getId())
                .orElse(new SrMatchPlayerStatistics(
                        playerRepository.findById(source.getId()).orElse(null)
                ));
        if (existing.getPlayer() == null) {
            return null;
        }

        return statisticsMapper.mapToDto(source.getStatistics(), existing);
    }


}
