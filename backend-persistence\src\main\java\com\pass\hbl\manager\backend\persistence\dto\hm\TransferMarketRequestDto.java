package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonRootName("Add transferMarket request")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Add transfer market request")
public class TransferMarketRequestDto {

    @NotNull
    @Schema(description = "Id of the league related to the transfer item", required = true)
    private String leagueId;

    @NotNull
    @Schema(description = "Id of the player offered for a transfer", required = true)
    private String playerId;

    @Schema(description = "Transfer price", required = true)
    private int price;

    @NotNull
    @Schema(description = "Player transfer duration", example = "SIX_HOURS", required = true)
    private String duration;
}
