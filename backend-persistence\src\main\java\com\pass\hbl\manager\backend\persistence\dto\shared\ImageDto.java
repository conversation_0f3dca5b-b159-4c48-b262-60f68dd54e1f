package com.pass.hbl.manager.backend.persistence.dto.shared;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@JsonRootName("Image")
@Getter
@Setter
@Schema(description = "Container object for an image including meta data and image binary")
public class ImageDto {

    @Schema(description = "unique id of the image")
    private String id;

    @Schema(description = "the image binary")
    @NotNull
    private byte[] content;

    @Schema(description = "the original name of the image file")
    private String name;

    @Schema(description = "the domain the image was used in")
    @NotNull
    private ImageDomain domain;

    @Schema(description = "the media type of the image")
    @NotNull
    private ImageMediaType mediaType;

    @JsonCreator
    public ImageDto() {
    }

    public ImageDto(byte[] content, String name, ImageDomain domain, ImageMediaType mediaType) {
        this.content = content;
        this.name = name;
        this.domain = domain;
        this.mediaType = mediaType;
    }
}
