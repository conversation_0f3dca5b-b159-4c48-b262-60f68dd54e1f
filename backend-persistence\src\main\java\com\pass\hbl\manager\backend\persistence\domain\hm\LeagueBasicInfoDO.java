package com.pass.hbl.manager.backend.persistence.domain.hm;

import java.util.UUID;

/**
 * Domain object for basic league information
 * Contains only the fields needed for simple league representation
 */
public interface LeagueBasicInfoDO {
    /**
     * Get the league ID
     * @return League ID
     */
    UUID getId();
    
    /**
     * Get the league name
     * @return League name
     */
    String getName();
}
