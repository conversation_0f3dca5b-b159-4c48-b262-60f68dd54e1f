package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.UUID;

@Table(name = "league_membership", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@SQLDelete(sql = "UPDATE hm.league_membership SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmLeagueMembership extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;
    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "member_id", referencedColumnName = "id", updatable = false)
    private HmUserProfile userProfile;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "league_id", referencedColumnName = "id", updatable = false)
    private HmLeague league;

    @ToString.Exclude
    @Column(name = "joined")
    private LocalDateTime joined;

    @ToString.Exclude
    @Column(name = "league_alias")
    private String alias;

    @ToString.Exclude
    @Column(name = "user_score")
    private int score;

    @ToString.Exclude
    @Column(name = "user_balance")
    private int balance;

    /**
     * user score at closing of the current round
     */
    @ToString.Exclude
    @Column(name = "score_backup")
    private Integer scoreBackup;

    /**
     * user balance at closing of the current round
     */
    @ToString.Exclude
    @Column(name = "round_closing_balance_backup")
    private Integer roundClosingBalanceBackup;

    /**
     * temp user score used while calculating round results
     */
    @ToString.Exclude
    @Column(name = "temp_score")
    private Integer tempScore;

    /**
     * temp user balance used while calculating round results
     */
    @ToString.Exclude
    @Column(name = "temp_balance")
    private Integer tempBalance;

    @ToString.Exclude
    @Column(name = "on_hold")
    private boolean onHold;


    public HmLeagueMembership(HmUserProfile userProfile, HmLeague league, LocalDateTime joined, int score, int balance) {
        this.userProfile = userProfile;
        this.league = league;
        this.joined = joined;
        this.score = score;
        this.balance = balance;
        this.scoreBackup = null;
        this.roundClosingBalanceBackup = null;
        this.tempScore = null;
        this.tempBalance = null;
        this.onHold = false;
    }
}
