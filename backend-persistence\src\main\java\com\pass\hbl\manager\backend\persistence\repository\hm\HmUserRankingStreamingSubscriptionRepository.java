package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserRankingStreamingSubscription;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.UUID;

public interface HmUserRankingStreamingSubscriptionRepository extends PagingAndSortingRepository<HmUserRankingStreamingSubscription, UUID> {

    List<HmUserRankingStreamingSubscription> getByActiveProfile(String profile);

    @Modifying
    @Query(value = "delete from hm.user_ranking_streaming_subscription where user_id = :userId and league_id = :leagueId", nativeQuery = true)
    void deleteByUserIdAndLeagueId(@Param("userId") UUID userId, @Param("leagueId") UUID leagueId);

    @Modifying
    @Query(value = "delete from hm.user_ranking_streaming_subscription where active_profile = :activeProfile", nativeQuery = true)
    void deleteByActiveProfile(@Param("activeProfile") String activeProfile);
}
