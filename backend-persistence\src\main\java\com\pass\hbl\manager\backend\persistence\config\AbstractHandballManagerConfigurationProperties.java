package com.pass.hbl.manager.backend.persistence.config;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AbstractHandballManagerConfigurationProperties {

    private HttpProxy httpProxy;
    private Importer importer;
    private DataCore dataCore;

    public DataCore getDataCore() {
        return dataCore;
    }

    public void setDataCore(DataCore dataCore) {
        this.dataCore = dataCore;
    }

    public HttpProxy getHttpProxy() {
        return this.httpProxy;
    }

    public void setHttpProxy(HttpProxy httpProxy) {
        this.httpProxy = httpProxy;
    }

    public Importer getImporter() {
        return this.importer;
    }

    public void setImporter(Importer importer) {
        this.importer = importer;
    }

    @Getter
    @Setter
    public static class HttpProxy {

        private boolean enabled;

        private String url;
    }

    @Getter
    @Setter
    public static class Importer {

        /**
         * Stage of sportrader. Either <code>trail</code> or <code>production</code>.
         */
        private String sportradarStage;

        /**
         * API Key of sportradar.
         */
        private String apiKey;
    }

    @Getter
    @Setter
    public static class DataCore {

        /**
         * DataCore base url for Staging (non-prod) environment
         */
        private String stagingBaseUrl;

        private String stagingAuthUrl;

        private String stagingStreamingAuthUrl;

        private String stagingClientId;

        private String stagingClientSecret;

        /**
         * DataCore base url for prod environment
         */
        private String prodBaseUrl;

        private String prodAuthUrl;

        private String prodStreamingAuthUrl;

        private String prodClientId;

        private String prodClientSecret;
    }
}
