package com.pass.hbl.manager.backend.persistence.entity.hm.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pass.hbl.manager.backend.persistence.dto.hm.SessionAttribute;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Converter
public class MapConverter implements AttributeConverter<Map<SessionAttribute, String>, String> {

    private final ObjectMapper objectMapper;


    public MapConverter() {
        objectMapper = new ObjectMapper();
        objectMapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
    }

    @Override
    public String convertToDatabaseColumn(Map<SessionAttribute, String> customerInfo) {

        String customerInfoJson = null;
        try {
            customerInfoJson = objectMapper.writeValueAsString(customerInfo);
        } catch (final JsonProcessingException e) {
            log.error("JSON writing error", e);
        }

        return customerInfoJson;
    }

    @Override
    public Map<SessionAttribute, String> convertToEntityAttribute(String customerInfoJSON) {

        Map<SessionAttribute, String> map = null;
        try {
            @SuppressWarnings("unchecked")
            Map<String, String> temp = objectMapper.readValue(customerInfoJSON, Map.class);
            if (temp != null) {
                map = Util.toStream(temp.entrySet())
                        .filter(e -> Objects.nonNull(SessionAttribute.getFromString(e.getKey())))
                        .map(e -> Pair.of(SessionAttribute.getFromString(e.getKey()), e.getValue()))
                        .collect(HashMap::new, (m, v)->m.put(v.getKey(), v.getValue()), HashMap::putAll);
            }
        } catch (final IOException e) {
            log.error("JSON reading error", e);
        }

        return map;
    }

}
