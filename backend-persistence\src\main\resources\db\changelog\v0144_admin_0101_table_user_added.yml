databaseChangeLog:
  - changeSet:
      author: system
      id: 144
      labels: initial
      comment: add table user
      changes:
        # hm.user_messaging_token
        - createTable:
            catalogName: handball_manager
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: id
                  type: uuid
              - column:
                  constraints:
                    nullable: false
                  name: username
                  type: varchar(256)
              - column:
                  constraints:
                    nullable: false
                  name: password
                  type: varchar(256)
              - column:
                  constraints:
                    nullable: false
                  name: email_address
                  type: varchar(256)
              - column:
                  name: roles
                  type: varchar(256)
              - column:
                  name: otp
                  type: integer
              - column:
                  name: created_at
                  type: timestamp
                  defaultValueDate: now()
                  defaultOnNull: true
              - column:
                  constraints:
                    nullable: false
                  defaultValueDate: "1970-01-01T00:00:00"
                  defaultOnNull: true
                  name: modified_at
                  type: timestamp
              - column:
                  name: deleted_at
                  constraints:
                    nullable: false
                  defaultValueDate: "1970-01-01T00:00:00"
                  defaultOnNull: true
                  type: timestamp
              - column:
                  constraints:
                    nullable: false
                  name: deleted
                  type: boolean
                  defaultValueBoolean: false
                  defaultOnNull: true
            remarks: storing data related to admin users. IDs are generated by JPA
            schemaName: admin
            tableName: user
