package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@JsonRootName("Player statistics")
@Getter
@Setter
@NoArgsConstructor
@Schema(description = "Player statistics by unit")
public class PlayerStatisticsDto {

    @Schema(description = "unit of the statistics", required = true)
    private StatisticsUnit unit;

    @Schema(description = "total score gained overall up to the given statistics unit", required = true)
    private int totalScore;

    @Schema(description = "average score gained overall up to the given statistics unit", required = true)
    private double averageScore;

    @Min(0)
    @Schema(description = "games played overall up to the given statistics unit", required = true)
    private int gamesPlayed;

    @Min(0)
    @Schema(description = "number of assists overall up to the given statistics unit", required = true)
    private int assistsNumber;

    @Min(0)
    @Schema(description = "average seconds played overall up to the given statistics unit", required = true)
    private int averageSecondsPlayed;

    @Min(0)
    @Schema(description = "goals thrown overall up to the given statistics unit")
    private Integer goalsThrown;

    @Min(0)
    @Schema(description = "throw rate overall up to the given statistics unit")
    private Double throwRate;

    @Min(0)
    @Schema(description = "goals saved overall up to the given statistics unit")
    private Integer savedGoals;

    @Min(0)
    @Schema(description = "save rate overall up to the given statistics unit")
    private Double saveRate;

    @Min(0)
    @Schema(description = "average goals saved overall up to the given statistics unit")
    private Double averageSavedGoals;

    public PlayerStatisticsDto(StatisticsUnit unit, int totalScore, double averageScore, int gamesPlayed, int assistsNumber, int averageSecondsPlayed, Integer goalsThrown, Double throwRate, Integer savedGoals, Double saveRate, Double averageSavedGoals) {
        this.unit = unit;
        this.totalScore = totalScore;
        this.averageScore = averageScore;
        this.gamesPlayed = gamesPlayed;
        this.assistsNumber = assistsNumber;
        this.averageSecondsPlayed = averageSecondsPlayed;
        this.goalsThrown = goalsThrown;
        this.throwRate = throwRate;
        this.savedGoals = savedGoals;
        this.saveRate = saveRate;
        this.averageSavedGoals = averageSavedGoals;
    }
}
