package com.pass.hbl.manager.backend.persistence.mapper.datacore.live;

import com.pass.hbl.manager.backend.persistence.entity.datacore.DcLiveMatch;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmMatch;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrAbstractMatch;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrMatch;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.AbstractSr2HmMapper;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.HmClubConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.HmSeasonConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.IdConverter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

@Component
public class Sr2HmDataCoreMatchMapper extends AbstractSr2HmMapper<SrAbstractMatch, HmMatch> {

    private final HmClubConverter clubConverter;
    private final HmSeasonConverter seasonConverter;

    public Sr2HmDataCoreMatchMapper(IdConverter idConverter, HmClubConverter clubConverter, HmSeasonConverter seasonConverter) {
        super(SrAbstractMatch.class, HmMatch.class, idConverter, false);
        this.clubConverter = clubConverter;
        this.seasonConverter = seasonConverter;
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected void customizeInit() {
        registerDateConverters();
        registerPrimitiveConverters();
        //fillTypeMap(SrMatch.class);
        fillTypeMap(DcLiveMatch.class);
    }

    @SuppressWarnings("DuplicatedCode")
    private void fillTypeMap(Class<? extends SrAbstractMatch> clazz) {
        TypeMap<? extends SrAbstractMatch, HmMatch> typeMap = getOrCreateTypeMap(clazz, HmMatch.class);
        // need to duplicate this call as otherwise it does not get registered on the concrete class
        //typeMap.addMappings(mapper -> mapper.using(getIdConverter()).map(SrAbstractMatch::getId, HmMatch::setId));
        typeMap.addMappings(mapper -> mapper.using(clubConverter).map(SrAbstractMatch::getHomeClubId, HmMatch::setHome));
        typeMap.addMappings(mapper -> mapper.using(clubConverter).map(SrAbstractMatch::getAwayClubId, HmMatch::setAway));
        typeMap.addMappings(mapper -> mapper.using(seasonConverter).map(SrAbstractMatch::getSeasonId, HmMatch::setSeason));
        typeMap.addMappings(mapper -> mapper.map(SrAbstractMatch::getHblRound, HmMatch::setHblRound));

        //skip values that do not exist in the datacore entity
        typeMap.addMappings(mapper -> mapper.skip(HmMatch::setId));
        typeMap.addMappings(mapper -> mapper.skip(HmMatch::setHalfTimeAwayScore));
        typeMap.addMappings(mapper -> mapper.skip(HmMatch::setHalfTimeHomeScore));
        typeMap.addMappings(mapper -> mapper.skip(HmMatch::setMatchTime));
        typeMap.addMappings(mapper -> mapper.skip(HmMatch::setStartTime));

        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getAway().setActive(false)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getAway().setAbbreviation(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getAway().setCurrentPlayers(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getAway().setCreatedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getAway().setDeleted(false)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getAway().setDeletedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getAway().setHistoricalPlayers(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getAway().setHblImageId(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getAway().setId(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getAway().setModifiedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getAway().setModified(false)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getAway().setName(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getAway().setPicture(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getAway().setStatistics(null)));

        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getHome().setActive(false)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getHome().setAbbreviation(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getHome().setCurrentPlayers(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getHome().setCreatedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getHome().setDeleted(false)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getHome().setDeletedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getHome().setHistoricalPlayers(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getHome().setHblImageId(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getHome().setId(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getHome().setModifiedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getHome().setModified(false)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getHome().setName(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getHome().setPicture(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getHome().setStatistics(null)));

        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getSeason().setId(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getSeason().setCreatedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getSeason().setDeleted(false)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getSeason().setDeletedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getSeason().setModifiedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getSeason().setModified(false)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getSeason().setRounds(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getSeason().setName(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getSeason().setEndDate(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getSeason().setStartDate(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getSeason().setYear(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.setStatistics(null)));
    }
}
