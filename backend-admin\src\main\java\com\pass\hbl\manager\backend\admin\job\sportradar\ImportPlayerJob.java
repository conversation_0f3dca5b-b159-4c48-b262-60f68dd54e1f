package com.pass.hbl.manager.backend.admin.job.sportradar;

import com.pass.hbl.manager.backend.persistence.job.ScheduledJob;
import com.pass.hbl.manager.backend.persistence.job.admin.AdminAbstractJob;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.datacore.masterdata.ImportPlayerDataCoreService;
import com.pass.hbl.manager.backend.persistence.service.sportradar.masterdata.ImportPlayerService;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

import static com.pass.hbl.manager.backend.admin.job.sportradar.ImportSeasonJob.PARAM_DATACORE;

@Slf4j
@Component
@ScheduledJob(description = "Import players from Sportradar", parameters = ImportPlayerJob.PARAM_CURRENT_ONLY + "=true;" + ImportPlayerJob.PARAM_TRANSFER_ONLY + "=false")
public class ImportPlayerJob extends AdminAbstractJob {

    public static final String PARAM_CURRENT_ONLY = "currentOnly";

    public static final String PARAM_TRANSFER_ONLY = "transferOnly";

    private final ImportPlayerService service;
    private final ImportPlayerDataCoreService importPlayerDataCoreService;
    private final Environment environment;
    private boolean isDataCoreApi;

    public ImportPlayerJob(ImportPlayerService service, ParameterService parameterService, ImportPlayerDataCoreService importPlayerDataCoreService, Environment environment) {
        super(parameterService);
        this.service = service;
        this.importPlayerDataCoreService = importPlayerDataCoreService;
        this.environment = environment;
    }

    @Override
    protected void work() throws Exception {
        if (isDataCoreApi) {
            log.info("SportRadar data imported from the DataCore Rest-Api");
            importPlayerDataCoreService.start(
                    getParameterAsBoolean(PARAM_CURRENT_ONLY),
                    getParameterAsBoolean(PARAM_TRANSFER_ONLY)
            );
        } else {
            service.start(getParameterAsBoolean(PARAM_CURRENT_ONLY), getParameterAsBoolean(PARAM_TRANSFER_ONLY));
        }
    }

    @Override
    protected void init() throws Exception {
        this.isDataCoreApi = getParameterAsBoolean(PARAM_DATACORE);
        List<String> activeProfiles = Arrays.asList(environment.getActiveProfiles());
        if (isDataCoreApi) {
            importPlayerDataCoreService.initDataCoreDefaults(activeProfiles);
        } else {
            service.initSportradarDefaults();
        }
    }

    @Override
    protected void tearDown() {

    }

    @Override
    protected void terminate() throws Exception {
        service.cancel();
    }
}
