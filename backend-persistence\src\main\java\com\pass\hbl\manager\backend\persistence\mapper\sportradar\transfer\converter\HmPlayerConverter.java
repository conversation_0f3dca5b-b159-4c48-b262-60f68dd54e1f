package com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminExternalDataMapping;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.repository.admin.AdminExternalDataMappingRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ExternalDataMappingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Component
@Slf4j
public class HmPlayerConverter extends AbstractConverter<String, HmPlayer> {

    private final ExternalDataMappingService externalDataMappingService;

    private final HmPlayerRepository playerRepository;
    private final Map<String, HmPlayer> cache;

    public HmPlayerConverter(ExternalDataMappingService externalDataMappingService, HmPlayerRepository playerRepository) {
        this.externalDataMappingService = externalDataMappingService;
        this.playerRepository = playerRepository;
        this.cache = new HashMap<>();
    }

    public void resetCache() {
        this.cache.clear();
    }

    @Override
    protected HmPlayer convert(String source) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        if (cache.containsKey(source)) {
            return cache.get(source);
        }
        Optional<HmPlayer> player = externalDataMappingService.get(Datasource.SPORTRADAR, ExternalEntity.PLAYER, source)
                .map(AdminExternalDataMapping::getHmId)
                .map(playerRepository::findById)
                .filter(Optional::isPresent)
                .map(Optional::get);
        if (player.isEmpty()) {
            log.warn("Unknown player with SrId=" + source);
            return null;
        }
        cache.put(source, player.get());
        return player.get();
    }
}
