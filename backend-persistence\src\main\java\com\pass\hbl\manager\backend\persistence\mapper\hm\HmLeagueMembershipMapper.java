package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.LeagueMembershipDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeagueMembership;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.HmEntityToStringConverter;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.StringToHmLeagueConverter;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.StringToHmUserProfileConverter;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserProfileRepository;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

@Component
public class HmLeagueMembershipMapper extends AbstractMapper<HmLeagueMembership, LeagueMembershipDto> {

    private final HmUserProfileRepository userProfileRepository;
    private final HmLeagueRepository leagueRepository;

    public HmLeagueMembershipMapper(HmUserProfileRepository userProfileRepository, HmLeagueRepository leagueRepository) {
        super(HmLeagueMembership.class, LeagueMembershipDto.class);
        this.userProfileRepository = userProfileRepository;
        this.leagueRepository = leagueRepository;
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected void customizeInit() {
        TypeMap<HmLeagueMembership, LeagueMembershipDto> e2d = getOrCreateTypeMap(HmLeagueMembership.class, LeagueMembershipDto.class);
        e2d.addMappings(mapper -> mapper.using(new HmEntityToStringConverter<HmLeague>()).map(HmLeagueMembership::getLeague, LeagueMembershipDto::setLeagueId));
        e2d.addMappings(mapper -> mapper.using(new HmEntityToStringConverter<HmUserProfile>()).map(HmLeagueMembership::getUserProfile, LeagueMembershipDto::setUserId));

        TypeMap<LeagueMembershipDto, HmLeagueMembership> d2e = getOrCreateTypeMap(LeagueMembershipDto.class, HmLeagueMembership.class);
        d2e.addMappings(mapper -> mapper.using(new StringToHmLeagueConverter(leagueRepository)).map(LeagueMembershipDto::getLeagueId, HmLeagueMembership::setLeague));
        d2e.addMappings(mapper -> mapper.using(new StringToHmUserProfileConverter(userProfileRepository)).map(LeagueMembershipDto::getUserId, HmLeagueMembership::setUserProfile));
    }
}
