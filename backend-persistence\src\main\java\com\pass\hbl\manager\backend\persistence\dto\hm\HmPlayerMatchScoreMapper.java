package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerRoundStatistics;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.springframework.stereotype.Component;

@Component
public class HmPlayerMatchScoreMapper extends AbstractMapper<HmPlayerRoundStatistics, PlayerMatchScoreDto> {

    public HmPlayerMatchScoreMapper() { super(HmPlayerRoundStatistics.class, PlayerMatchScoreDto.class);   }
}
