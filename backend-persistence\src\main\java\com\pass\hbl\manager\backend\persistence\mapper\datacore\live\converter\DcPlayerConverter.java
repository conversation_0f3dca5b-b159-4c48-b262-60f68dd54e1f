package com.pass.hbl.manager.backend.persistence.mapper.datacore.live.converter;

import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminExternalDataMapping;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ExternalDataMappingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import static com.pass.hbl.manager.backend.persistence.dto.admin.Datasource.DATACORE;

@Component
@Slf4j
public class DcPlayerConverter extends AbstractConverter<String, HmPlayer> {

    private final ExternalDataMappingService externalDataMappingService;

    private final HmPlayerRepository playerRepository;
    private final Map<String, HmPlayer> cache;

    public DcPlayerConverter(ExternalDataMappingService externalDataMappingService, HmPlayerRepository playerRepository) {
        this.externalDataMappingService = externalDataMappingService;
        this.playerRepository = playerRepository;
        this.cache = new ConcurrentHashMap<>();
    }

    public void resetCache() {
        this.cache.clear();
    }

    @Override
    protected HmPlayer convert(String source) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        if (cache.containsKey(source)) {
            return cache.get(source);
        }
        try {
            Optional<HmPlayer> playerOptional = externalDataMappingService.get(DATACORE, ExternalEntity.PLAYER, source)
                .map(AdminExternalDataMapping::getHmId)
                .map(playerRepository::findById)
                .filter(Optional::isPresent)
                .map(Optional::get);

            if (playerOptional.isEmpty()) {
                log.warn("Unknown player with dcId=" + source);
                return null;
            }
            HmPlayer player = playerOptional.get();
            cache.put(source, player);
            return player;
        } catch (Exception e) {
            log.error("Converting match with dcId=" + source + "to HmMatch failed", e);
            return null;
        }
    }
}
