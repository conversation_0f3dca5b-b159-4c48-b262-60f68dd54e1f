package com.pass.hbl.manager.backend.persistence.job.hm;

import com.pass.hbl.manager.backend.persistence.job.AbstractJob;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class HmAbstractJob extends AbstractJob {

    public HmAbstractJob(ParameterService parameterService) {
        super(parameterService);
    }
}
