package com.pass.hbl.manager.backend.persistence.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@MappedSuperclass
@Setter
@Getter
@NoArgsConstructor
public class AbstractStreamingSubscriptionEntity {

    @NotNull
    @Column(name = "user_id", nullable = false)
    private UUID userId;

    @NotNull
    @Column(name = "active_profile")
    private String activeProfile;

    public AbstractStreamingSubscriptionEntity(UUID userId, String activeProfile) {
        this.userId = userId;
        this.activeProfile = activeProfile;
    }
}
