package com.pass.hbl.manager.backend.admin.config.security;

import com.pass.hbl.manager.backend.admin.config.HandballManagerAdminConfigurationProperties;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.core.env.Environment;
import org.springframework.lang.NonNull;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static com.pass.hbl.manager.backend.admin.util.ApiConstants.API_KEY;

public class ApiKeyAuthenticationFilter extends OncePerRequestFilter {

    private final HandballManagerAdminConfigurationProperties properties;

    public ApiKeyAuthenticationFilter(HandballManagerAdminConfigurationProperties properties) {
        this.properties = properties;
    }

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull FilterChain filterChain) throws ServletException, IOException {
            String token = request.getHeader(API_KEY);
            if (!StringUtils.equals(properties.getSecurity().getApiKey(), token)) {
                response.setStatus(HttpStatus.SC_UNAUTHORIZED);
                return;
            }
        filterChain.doFilter(request, response);
    }
}
