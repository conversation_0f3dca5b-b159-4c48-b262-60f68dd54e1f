package com.pass.hbl.manager.backend.persistence.mapper.datacore.live.converter;

import com.pass.hbl.manager.backend.persistence.domain.datacore.DataObject;
import com.pass.hbl.manager.backend.persistence.domain.datacore.Entity;
import com.pass.hbl.manager.backend.persistence.domain.datacore.FixtureStatus;
import com.pass.hbl.manager.backend.persistence.dto.hm.Winner;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

import java.util.*;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Component
@Slf4j
public class DcLiveMatchWinnerConverter extends AbstractConverter<DataObject, Winner> {

    @Override
    protected Winner convert(DataObject source) {
        Map<String, Entity> entities = source.getEntities();
        if (isNull(source) || isNull(source.getStatus()) || isNull(entities) || entities.isEmpty()) {
            return null;
        }
        // TODO HBLMAN-565 check if the first item is home and second is away
        try {
            if (Objects.equals(source.getStatus().getFixture(), FixtureStatus.FINISHED.name())) {
                Set<String> keys = entities.keySet();
                List<String> keyList = new ArrayList<>(keys);
                if (keyList.size() == 2) {
                    // Get the match Ids
                    String match1Id = keyList.get(0);
                    String match2Id = keyList.get(1);
                    Entity entity1 = entities.get(match1Id);
                    Entity entity2 = entities.get(match2Id);
                    if (nonNull(entity1) && nonNull(entity2)) {
                        if (entity1.getScore() > entity2.getScore()) {
                            return Winner.HOME;
                        } else if (entity2.getScore() > entity1.getScore()) {
                            return Winner.AWAY;
                        } else {
                            return Winner.DRAW;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Get Winner from Datacore streaming live match status message failed", e);
            return null;
        }
        return Winner.N_A;
    }
}
