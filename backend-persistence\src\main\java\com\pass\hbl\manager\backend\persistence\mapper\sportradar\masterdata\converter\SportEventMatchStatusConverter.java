package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrMatchStatus;
import com.sportradar.handball.v2.model.EnumSportEventMatchStatus;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

@Component
public class SportEventMatchStatusConverter extends AbstractConverter<EnumSportEventMatchStatus, SrMatchStatus> {
    @Override
    protected SrMatchStatus convert(EnumSportEventMatchStatus source) {
        return source == null ? null : SrMatchStatus.valueOf(convertValue(source.getValue()));
    }

    private String convertValue(String s) {
        if (StringUtils.isEmpty(s)) {
            return null;
        }
        if (Character.isDigit(s.charAt(0))) {
            s = "_" + s;
        }
        return s.toUpperCase();
    }
}
