package com.pass.hbl.manager.backend.persistence.config;

import com.pass.hbl.manager.backend.persistence.util.Util;
import com.sportradar.handball.v2.ApiClient;
import com.sportradar.handball.v2.api.*;
import com.sportradar.handball.v2.auth.ApiKeyAuth;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.internal.util.Objects;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.InetSocketAddress;
import java.net.MalformedURLException;
import java.net.ProxySelector;
import java.net.URL;

@Slf4j
@Configuration
public class SportradarApiConfig {

    @Bean
    public CompetitionsApi getCompetitionsApi(AbstractHandballManagerConfigurationProperties properties) {
        return new CompetitionsApi(getApiClient(properties));
    }

    @Bean
    public SeasonsApi getSeasonsApi(AbstractHandballManagerConfigurationProperties properties) {
        return new SeasonsApi(getApiClient(properties));
    }

    @Bean
    public CompetitorsApi getCompetitorsApi(AbstractHandballManagerConfigurationProperties properties) {
        return new CompetitorsApi(getApiClient(properties));
    }

    @Bean
    public SummariesApi getSummariesApi(AbstractHandballManagerConfigurationProperties properties) {
        return new SummariesApi(getApiClient(properties));
    }

    @Bean
    public PlayersApi getPlayersApi(AbstractHandballManagerConfigurationProperties properties) {
        return new PlayersApi(getApiClient(properties));
    }

    @Bean
    public MappingsApi getMappingsApi(AbstractHandballManagerConfigurationProperties properties) {
        return new MappingsApi(getApiClient(properties));
    }

    @Bean
    public MergeApi getMergeApi(AbstractHandballManagerConfigurationProperties properties) {
        return new MergeApi(getApiClient(properties));
    }

    @Bean
    public SchedulesApi getSchedulesApi(AbstractHandballManagerConfigurationProperties properties) {
        return new SchedulesApi(getApiClient(properties));
    }

    @Bean
    public TimelinesApi getTimelinesApi(AbstractHandballManagerConfigurationProperties properties) {
        return new TimelinesApi(getApiClient(properties));
    }

    @Bean
    public StatisticsApi getStatisticsApi(AbstractHandballManagerConfigurationProperties properties) {
        return new StatisticsApi(getApiClient(properties));
    }

    @Bean
    public SportEventsApi getSportEventsApi(AbstractHandballManagerConfigurationProperties properties) {
        return new SportEventsApi(getApiClient(properties));
    }

    @Bean
    public PushApi getPushApi(AbstractHandballManagerConfigurationProperties properties) {
        return new PushApi(getApiClient(properties));
    }

    private ApiClient getApiClient(AbstractHandballManagerConfigurationProperties properties) {

        String proxyString = Objects.firstNonNull(
                properties.getHttpProxy().isEnabled() ? properties.getHttpProxy().getUrl() : null,
                System.getenv("https_proxy"),
                System.getenv("HTTPS_PROXY"),
                System.getenv("http_proxy"),
                System.getenv("HTTP_PROXY"),
                StringUtils.EMPTY);

        log.info("using proxy=" + proxyString + " for accessing sportradar "
                + properties.getImporter().getSportradarStage()
                + " with apiKey=" + properties.getImporter().getApiKey());

        ProxySelector proxy;
        if (!StringUtils.equalsIgnoreCase(StringUtils.EMPTY, proxyString)) {
            try {
                URL url = new URL(proxyString);
                proxy = ProxySelector.of(new InetSocketAddress(url.getHost(), url.getPort()));
            } catch (MalformedURLException e) {
                throw new RuntimeException("Malformed proxy URL: " + proxyString);
            }
        } else {
            proxy = ProxySelector.getDefault();
        }

        ApiClient apiClient = com.sportradar.handball.v2.Configuration.getDefaultApiClient();
        apiClient.setBasePath(apiClient.getBasePath().replace("trial", properties.getImporter().getSportradarStage()));

        // Configure API key authorization: ApiKeyAuth
        ApiKeyAuth ApiKeyAuth = (ApiKeyAuth) apiClient.getAuthentication("ApiKeyAuth");
        ApiKeyAuth.setApiKey(properties.getImporter().getApiKey());

        // set proxy
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        Util.toStream(apiClient.getHttpClient().networkInterceptors()).forEach(builder::addNetworkInterceptor);
        apiClient.setHttpClient(builder.proxySelector(proxy).build());

        return apiClient;
    }
}
