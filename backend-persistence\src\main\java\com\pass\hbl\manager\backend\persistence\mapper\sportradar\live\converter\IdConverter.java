package com.pass.hbl.manager.backend.persistence.mapper.sportradar.live.converter;

import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

public abstract class IdConverter<NumericType> extends AbstractConverter<NumericType, String> {

    private final String prefix;

    protected IdConverter(String prefix) {
        this.prefix = prefix;
    }

    @Override
    public String convert(NumericType source) {
        return source == null ? null : prefix + source;
    }

    @Component
    public static class CompetitorIdConverter extends IdConverter<Integer> {
        public CompetitorIdConverter() {
            super("sr:competitor:");
        }
    }

    @Component
    public static class MatchIdConverter extends IdConverter<Integer> {
        public MatchIdConverter() {
            super("sr:sport_event:");
        }
    }

    @Component
    public static class PlayerIdConverter extends IdConverter<Integer> {
        public PlayerIdConverter() {
            super("sr:player:");
        }
    }

    @Component
    public static class EventIdConverter extends IdConverter<Long> {
        public EventIdConverter() {
            super("");
        }
    }
}
