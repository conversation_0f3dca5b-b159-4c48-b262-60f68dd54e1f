package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import jdk.jfr.BooleanFlag;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Map;

@JsonRootName("LeagueInfo")
@Getter
@Setter
@ToString
@Schema(description = "Overview info of a league")
public class LeagueInfoDto extends AbstractPictureDto<LeagueInfoDto, String> {

    @Schema(description = "League id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true, accessMode = Schema.AccessMode.READ_ONLY)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "Name of the league", example = "AB-Town Heroes", required = true)
    @NotBlank
    @Size(max = 32)
    private String name;

    @Schema(description = "Id of the owner of the league", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotNull
    private String ownerId;

    @Schema(description = "Optional description of the league")
    private String description;

    @Schema(description = "Public access flag, false by default")
    @BooleanFlag
    private Boolean publicAccess;

    @Schema(description = "Season id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true, accessMode = Schema.AccessMode.READ_ONLY)
    @NotNull
    private String seasonId;

    @Schema(description = "Amount of members in the league. This field is read only and cannot be modified", accessMode = Schema.AccessMode.READ_ONLY)
    @Min(0)
    private int memberCount;

    @Schema(description = "Maximum allowed members in the league. The system defines an upper bound, the value cannot be lowered below the member count.")
    @Min(0)
    private int maxSize;

    @Schema(description = "Optional map of league infos")
    private Map<LeagueInfo, String> leagueInfo;

    @Schema(description = "active state of the league")
    @JsonProperty(value = "isActive")
    private boolean isActive;

    @JsonCreator
    public LeagueInfoDto() {
    }

    public LeagueInfoDto(String id, String name, String ownerId, String description, Boolean publicAccess, String seasonId, int memberCount, int maxSize) {
        this.id = id;
        this.name = name;
        this.ownerId = ownerId;
        this.description = description;
        this.publicAccess = publicAccess;
        this.seasonId = seasonId;
        this.memberCount = memberCount;
        this.maxSize = maxSize;
    }
}
