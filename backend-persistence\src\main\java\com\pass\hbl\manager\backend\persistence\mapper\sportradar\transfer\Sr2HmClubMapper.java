package com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmClub;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrCompetitor;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.IdConverter;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class Sr2HmClubMapper extends AbstractSr2HmMapper<SrCompetitor, HmClub> {

    public Sr2HmClubMapper(IdConverter idConverter) {
        super(SrCompetitor.class, HmClub.class, idConverter);
    }

    @Override
    protected HmClub customizeMapToDto(HmClub hmClub, SrCompetitor srCompetitor, Map<String, Object> context) {
        if (hmClub == null) {
            return null;
        }
        hmClub.setActive(true);
        return hmClub;
    }
}
