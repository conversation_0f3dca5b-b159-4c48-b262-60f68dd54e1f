package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@JsonRootName("PlayerHistoryDto")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(description = "Reduced player history dto object for player")
public class PlayerHistoryDto  {

    @NotBlank
    @Schema(description = "First name", example = "John", required = true)
    private String firstName;

    @NotBlank
    @Schema(description = "Last name", example = "Doe", required = true)
    private String lastName;

    @NotBlank
    @Schema(description = "image id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542")
    private String hblImageId;

    @NotNull
    @Schema(description = "Position of the player", required = true)
    private Position position;

    @Schema(description = "Total score of already played games in current season")
    private Integer totalScore;

}
