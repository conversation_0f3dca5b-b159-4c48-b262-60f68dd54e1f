package com.pass.hbl.manager.backend.admin.util;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletResponse;

@Getter
@Setter
public class PaginatedResultsRetrievedEvent extends ApplicationEvent {

    private String resource;

    private UriComponentsBuilder uriBuilder;

    private HttpServletResponse response;

    private int page;

    private int totalPages;

    private int size;

    public PaginatedResultsRetrievedEvent(Object source, String resource, UriComponentsBuilder uriBuilder, HttpServletResponse response, int page, int totalPages, int size) {
        super(source);
        this.resource = resource;
        this.uriBuilder = uriBuilder;
        this.response = response;
        this.page = page;
        this.totalPages = totalPages;
        this.size = size;
    }
}
