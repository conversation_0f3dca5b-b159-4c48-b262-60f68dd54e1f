package com.pass.hbl.manager.backend.admin.util;

public interface ApiConstants {

    // update the version number and require an app update if required (see api service)
    int CURRENT_API_VERSION = 1;
    // example "v1/admin"
    String VERSION = "v" + CURRENT_API_VERSION + "/admin";

    String SCHEDULER_API = "/" + VERSION + "/scheduler";
    String PARAMETER_API = "/" + VERSION + "/parameter";
    String LOG_API = "/" + VERSION + "/log";
    String LOCALIZATION_API = "/" + VERSION + "/localization";
    String AWARD_API = "/" + VERSION + "/award";
    String EXTERNAL_MAPPING_API = "/" + VERSION + "/externalMapping";
    String USER_API = "/" + VERSION + "/user";
    String ADMIN_USER_API = "/" + VERSION + "/adminUser";
    String ADMIN_USER_ADD_API = "/" + VERSION + "/adminUser/add";
    String PLAYER_API = "/" + VERSION + "/player";
    String LEAGUE_API = "/" + VERSION + "/league";
    String TEAM_API = "/" + VERSION + "/team";
    String SEASON_API = "/" + VERSION + "/season";
    String TRANSFER_MARKET_API = "/" + VERSION + "/transferMarket";
    String LIVE_API = "/" + VERSION + "/live";


    String MAGIC_TOKEN = "magic-token";
    String SECURITY_SCHEMA_NAME = "bearerAuth";

    String API_KEY = "apiKey";

    // Spring security constants
    String ADMIN = "ADMIN";
    String ROLE_ADMIN = "ROLE_" + ADMIN;

    String ADMIN_WRITE = "ADMIN_WRITE";
    String ROLE_ADMIN_WRITE = "ROLE_" + ADMIN_WRITE;

    String ROLE_SYSTEM = "ROLE_SYSTEM";

    String UTC_TIMEZONE = "Etc/UTC";
    String PLAYER_SCORE_DATA_ROUND = "player_score_data_R";
}
