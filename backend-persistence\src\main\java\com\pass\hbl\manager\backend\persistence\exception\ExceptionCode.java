package com.pass.hbl.manager.backend.persistence.exception;

import lombok.Getter;

public enum ExceptionCode {

    UNKNOWN(9999),
    ENTITY_NOT_EXIST(1000),
    LIVE_DATA(1500),
    FORBIDDEN_OPERATION(2000),
    INVALID_OPERATION(3000),
    FORMAT(4000),
    UNAUTHENTICATED(4500),
    TECHNICAL(5000),
    SCHEDULING(6000),
    IMPORT(7000),
    VALIDATION(8000),
    CONVERSION(9000),
    MAPPING(9500),
    COULD_NOT_HAPPEN(-1000),
    UNEXPECTED(-2000);


    @Getter
    private final int code;

    ExceptionCode(int code) {
        this.code = code;
    }
}
