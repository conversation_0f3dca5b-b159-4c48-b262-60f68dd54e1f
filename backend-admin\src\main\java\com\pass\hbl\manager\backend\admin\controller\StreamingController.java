package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.service.datacore.streaming.DataCoreRestStreamingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.ArrayList;
import java.util.List;

import static com.pass.hbl.manager.backend.admin.util.ApiConstants.ROLE_ADMIN_WRITE;
import static com.pass.hbl.manager.backend.admin.util.ApiConstants.ROLE_SYSTEM;
import static java.util.Collections.emptyList;

@RestController
@RequestMapping(ApiConstants.LIVE_API)
@Validated
@Tag(name = "team", description = "API for admin streaming management")
public class StreamingController {

    private final DataCoreRestStreamingService dataCoreRestStreamingService;

    public StreamingController(DataCoreRestStreamingService dataCoreRestStreamingService) {
        this.dataCoreRestStreamingService = dataCoreRestStreamingService;
    }

    @Operation(summary = "Adds a match to fixture id list used for datacore live streaming")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support")
    })
    @PostMapping("/fixture/{fixtureId}/stream")
    @RolesAllowed({ROLE_SYSTEM, ROLE_ADMIN_WRITE})
    public void streamMatch(@PathVariable String fixtureId, @RequestParam Boolean add) {
        if (add) {
            List<String> fixtureIds = new ArrayList<>(dataCoreRestStreamingService.getFixtureIds());
            fixtureIds.add(fixtureId);
            dataCoreRestStreamingService.setFixtureIds(fixtureIds);
        } else {
            dataCoreRestStreamingService.setFixtureIds(List.of(fixtureId));
        }
    }

    @Operation(summary = "Clear the list of datacore fixtures used for live streaming")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support")
    })
    @PostMapping("/fixture/clear")
    @RolesAllowed({ROLE_SYSTEM, ROLE_ADMIN_WRITE})
    public void clearMatchesStreaming() {
        dataCoreRestStreamingService.setFixtureIds(emptyList());
    }
}
