package com.pass.hbl.manager.backend.persistence.mapper.datacore.live;

import com.pass.hbl.manager.backend.persistence.dto.hm.Position;
import com.pass.hbl.manager.backend.persistence.entity.datacore.DcLiveMatchPlayerStatistics;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmClub;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerInClub;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerRoundStatistics;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrAbstractPlayerStatistics;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrMatchPlayerStatistics;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.live.converter.DcMatchConverter;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.live.converter.DcPlayerConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.AbstractSr2HmMapper;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.IdConverter;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerInClubRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerMatchEventRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerRepository;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class Sr2HmDataCorePlayerStatisticsMapper extends AbstractSr2HmMapper<SrAbstractPlayerStatistics, HmPlayerRoundStatistics> {

    @Getter
    private final DcMatchConverter matchConverter;
    @Getter
    private final DcPlayerConverter playerConverter;

    private final HmPlayerMatchEventRepository playerMatchEventRepository;

    private final HmPlayerInClubRepository playerInClubRepository;

    public Sr2HmDataCorePlayerStatisticsMapper(IdConverter idConverter, DcMatchConverter matchConverter, DcPlayerConverter playerConverter, HmPlayerMatchEventRepository playerMatchEventRepository, HmPlayerInClubRepository playerInClubRepository) {
        super(SrAbstractPlayerStatistics.class, HmPlayerRoundStatistics.class, idConverter, false);
        this.matchConverter = matchConverter;
        this.playerConverter = playerConverter;
        this.playerMatchEventRepository = playerMatchEventRepository;
        this.playerInClubRepository = playerInClubRepository;
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected void customizeInit() {
        registerDateConverters();
        registerPrimitiveConverters();
        fillTypeMap(SrMatchPlayerStatistics.class);
        fillTypeMap(DcLiveMatchPlayerStatistics.class);
    }

    public void resetConverterCaches() {
        matchConverter.resetCache();
        playerConverter.resetCache();
    }

    @SuppressWarnings("DuplicatedCode")
    private void fillTypeMap(Class<? extends SrAbstractPlayerStatistics> clazz) {
        TypeMap<? extends SrAbstractPlayerStatistics, HmPlayerRoundStatistics> typeMap = getOrCreateTypeMap(clazz, HmPlayerRoundStatistics.class);
        typeMap.addMappings(mapper -> mapper.using(playerConverter).map(SrAbstractPlayerStatistics::getPlayerSrId, HmPlayerRoundStatistics::setPlayer));
        typeMap.addMappings(mapper -> mapper.using(matchConverter).map(SrAbstractPlayerStatistics::getMatchSrId, HmPlayerRoundStatistics::setMatch));

        typeMap.addMappings(mapper -> mapper.skip(HmPlayerRoundStatistics::setTotalScore));
        typeMap.addMappings(mapper -> mapper.skip(HmPlayerRoundStatistics::setId));
        typeMap.addMappings(mapper -> mapper.skip(HmPlayerRoundStatistics::setCreatedAt));
        typeMap.addMappings(mapper -> mapper.skip(HmPlayerRoundStatistics::setDeleted));
        typeMap.addMappings(mapper -> mapper.skip(HmPlayerRoundStatistics::setDeletedAt));
        typeMap.addMappings(mapper -> mapper.skip(HmPlayerRoundStatistics::setModified));
        typeMap.addMappings(mapper -> mapper.skip(HmPlayerRoundStatistics::setModifiedAt));

        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getMatch().setCreatedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getMatch().setDeleted(false)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getMatch().setDeletedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getMatch().setId(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getMatch().setModifiedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getMatch().setModified(false)));

        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getPlayer().setCreatedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getPlayer().setDeleted(false)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getPlayer().setDeletedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getPlayer().setId(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getPlayer().setModifiedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getPlayer().setModified(false)));
    }

    @Override
    protected HmPlayerRoundStatistics customizeMapToDto(HmPlayerRoundStatistics hm, SrAbstractPlayerStatistics sr, Map<String, Object> context) {
        HmPlayer player = hm.getPlayer();
        if (player == null || hm.getMatch() == null) {
            log.warn("Cannot convert statistics, unknown match or player: " + sr);
            return null;
        }

        hm.setRound(hm.getMatch().getRound());
        DcLiveMatchPlayerStatistics dcLiveMatchPlayerStatistics = (DcLiveMatchPlayerStatistics) sr;
        if (Objects.equals(player.getPosition().toString(), Position.G.name())) {
            hm.setSecondsPlayed(dcLiveMatchPlayerStatistics.getGoalKeeperSecondsPlayed());
        }

        //hm.setTotalScore(roundScoreRepository.findFirstByPlayerIdAndMatchId(hm.getPlayer().getId(), hm.getMatch().getId()).map(HmPlayerRoundScore::getScore).orElse(null));

        // Important: The total score is calculated based on HmPlayerMatchEvent instead of HmPlayerRoundScore entities.
        // When trying to retrieve the total score using EntityManager#find(), we will get back the result from the 1st Level Cache
        // that is storing our uncommitted player match events (transfer events); otherwise it will query HmPlayerRoundScore which
        // retrieves only committed changes.
        hm.setTotalScore(playerMatchEventRepository.findTotalScoreByPlayerIdAndMatchId(player.getId(), hm.getMatch().getId()));

        //List<HmPlayerInClub> currentClub = hm.getPlayer().getCurrentClub();
        //IMPORTANT: PlayerInClub should be explicitly retrieved since the Player-Caching with CopyOnWrite Arraylist
        // in PlayerConverter doesn't keep the transactional context of the HmPlayer entities.
        // So otherwise player.getCurrentClub will throw a lazy initialization exception
        List<HmPlayerInClub> currentClub = playerInClubRepository.findCurrentClub(player.getId(), LocalDateTime.now());
        if (Objects.equals(currentClub.stream().map(HmPlayerInClub::getClub).map(HmClub::getId).findFirst().orElse(null), hm.getMatch().getAway().getId())) {
            hm.setChallenger(hm.getMatch().getHome());
        } else {
            hm.setChallenger(hm.getMatch().getAway());
        }
        return hm;
    }
}
