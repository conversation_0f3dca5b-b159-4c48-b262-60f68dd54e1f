package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerMatchEventDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmMatch;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerMatchEvent;
import com.pass.hbl.manager.backend.persistence.entity.shared.SharedLocalization;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.HmEntityToStringConverter;
import com.pass.hbl.manager.backend.persistence.repository.shared.SharedLocalizationRepository;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.AbstractConverter;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;

import static com.pass.hbl.manager.backend.persistence.dto.shared.EntityType.MATCH_EVENT;
import static com.pass.hbl.manager.backend.persistence.util.Constants.DEFAULT_LOCALE;
import static com.pass.hbl.manager.backend.persistence.util.MappingConstants.LOCALE;
import static java.util.Objects.isNull;

@Slf4j
@Component
public class HmPlayerMatchEventMapper extends AbstractMapper<HmPlayerMatchEvent, PlayerMatchEventDto> {

    private final SharedLocalizationRepository sharedLocalizationRepository;

    private final HashMap<Locale, HashMap<String, String>> translationCache = new HashMap<>();

    public HmPlayerMatchEventMapper(SharedLocalizationRepository sharedLocalizationRepository) {
        super(HmPlayerMatchEvent.class, PlayerMatchEventDto.class);
        this.sharedLocalizationRepository = sharedLocalizationRepository;
    }

    @Override
    protected void customizeInit() {
        TypeMap<HmPlayerMatchEvent, PlayerMatchEventDto> e2d = getOrCreateTypeMap(HmPlayerMatchEvent.class, PlayerMatchEventDto.class);
        Converter<String, Integer> matchTimeConverter = new AbstractConverter<>() {

            @Override
            protected Integer convert(String source) {
                String[] arr = source.split(":");
                try {
                    int time = Integer.parseInt(arr[0]);
                    return time == 60? time: time + 1;
                } catch (Exception e) {
                    return null;
                }
            }
        };
        e2d.addMappings(mapper -> mapper.using(new HmEntityToStringConverter<HmPlayer>()).map(HmPlayerMatchEvent::getPlayer, PlayerMatchEventDto::setPlayerId));
        e2d.addMappings(mapper -> mapper.using(new HmEntityToStringConverter<HmMatch>()).map(HmPlayerMatchEvent::getMatch, PlayerMatchEventDto::setMatchId));
        e2d.addMappings(mapper -> mapper.using(matchTimeConverter).map(HmPlayerMatchEvent::getMatchTime, PlayerMatchEventDto::setEventTime));
    }

    @Override
    protected PlayerMatchEventDto customizeMapToDto(PlayerMatchEventDto dto, HmPlayerMatchEvent entity, Map<String, Object> context) {
        String key = isNull(dto.getEventCharacteristic())? dto.getEvent().name(): dto.getEvent().name() + "_"
                + dto.getEventCharacteristic().name();

        String value;
        Locale locale = DEFAULT_LOCALE;
        if (context != null && context.containsKey(LOCALE)) {
            locale = (Locale) context.get(LOCALE);
        }

        if (translationCache.containsKey(locale) && translationCache.get(locale).containsKey(key)) {
            // if cached use from cache
            value = translationCache.get(locale).get(key);
        } else {
            // else get it from database and cache it
            Optional<SharedLocalization> optional = sharedLocalizationRepository.findFirstByEntityTypeAndKeyAndLocale(MATCH_EVENT, key, locale);
            value = optional.map(SharedLocalization::getValue).orElse(null);
            if (!translationCache.containsKey(locale)) {
                translationCache.put(locale, new HashMap<>());
            }
            translationCache.get(locale).put(key, value);
        }

        dto.setDescription(value);
        return super.customizeMapToDto(dto, entity, context);
    }
}
