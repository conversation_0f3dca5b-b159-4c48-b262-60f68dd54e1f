package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrSportEventStatus;
import com.sportradar.handball.v2.model.EnumSportEventStatus;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

@Component
public class SportEventStatusConverter extends AbstractConverter<EnumSportEventStatus, SrSportEventStatus> {
    @Override
    protected SrSportEventStatus convert(EnumSportEventStatus source) {
        return source == null ? null : SrSportEventStatus.valueOf(source.getValue().toUpperCase());
    }
}
