package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "Venue")
@NoArgsConstructor
@Data
public class VenueXml {

    @JacksonXmlProperty(localName = "Country")
    private CountryXml country;

    @JacksonXmlProperty(localName = "City")
    private CityXml city;

    @JacksonXmlProperty(localName = "Stadium")
    private StadiumXml stadium;
}
