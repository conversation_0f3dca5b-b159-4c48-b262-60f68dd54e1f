package com.pass.hbl.manager.backend.persistence.domain.datacore;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Map;

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@NoArgsConstructor
public class DcSportEvent {
    @JsonProperty("class")
    private String classType;

    private String clock;

    private String entityId;

    private String eventId;

    private LocalDateTime eventTime;

    private String eventType;

    private Options options;

    private int periodId;

    private String personId;

    private String playId;

    private Map<String, Integer> scores;

    private int sequence;

    private String status;

    private String subType;

    private boolean success;

    private LocalDateTime timestamp;

    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Getter
    @Setter
    @NoArgsConstructor
    public static class Options {

        private String attackType;

        private String failureReason;

        private String location;
    }

}