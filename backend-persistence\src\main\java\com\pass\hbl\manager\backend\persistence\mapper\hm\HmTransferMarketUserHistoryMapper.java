package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.TransferMarketBidUserDo;
import com.pass.hbl.manager.backend.persistence.dto.hm.BidStatus;
import com.pass.hbl.manager.backend.persistence.dto.hm.TransferMarketBidUserDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.TransferStatus;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.service.hm.PlayerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;


@Component
@Slf4j
public class HmTransferMarketUserHistoryMapper extends AbstractMapper<TransferMarketBidUserDo, TransferMarketBidUserDto> {

    public static final String USER_ID = "USER_ID";
    private final PlayerService playerService;


    public HmTransferMarketUserHistoryMapper(PlayerService playerService) {
        super(TransferMarketBidUserDo.class, TransferMarketBidUserDto.class);
        this.playerService = playerService;
    }

    @Override
    protected TransferMarketBidUserDto customizeMapToDto(TransferMarketBidUserDto transferMarketBidUserDto, TransferMarketBidUserDo transferMarketBidUserDo, Map<String, Object> context) {
        String userId = context.get(USER_ID).toString();
        transferMarketBidUserDto.setTransferStatus(getTransferStatus(transferMarketBidUserDo, userId));
        HmPlayer player;
        try {
            player = playerService.getByIdFromCache(UUID.fromString(transferMarketBidUserDo.getPlayerId()));
        } catch (EntityNotExistException | FormatException e) {
            throw new RuntimeException(e);
        }
        transferMarketBidUserDto.setPlayer(playerService.mapToHistoryDto(player, null));
        return super.customizeMapToDto(transferMarketBidUserDto, transferMarketBidUserDo);
    }

    private static TransferStatus getTransferStatus(TransferMarketBidUserDo transferMarketBidUserDo, String userId) {
        //if the Owner of the offer is the user and the Bid accepted so the status is SOLD
        if (transferMarketBidUserDo.getOwnerId().equals(userId)) {
            if (Objects.equals(BidStatus.ACCEPTED.toString(), transferMarketBidUserDo.getStatus())) {
                return TransferStatus.SOLD;
            }
        }
        //if the Bid accepted so the status is PURCHASED and the owner of the bid is not the user
        if (Objects.equals(BidStatus.ACCEPTED.toString(), transferMarketBidUserDo.getStatus())) {
            return TransferStatus.PURCHASED;
        }
        //if the Bid REJECTED so the status is REJECTED and the owner of the bid is not the user
        if (Objects.equals(BidStatus.REJECTED.toString(), transferMarketBidUserDo.getStatus())) {
            return TransferStatus.REJECTED;
        }
        if (transferMarketBidUserDo.getDeletedAt() != null && transferMarketBidUserDo.getAuctionEnd() != null) {
            // Truncate both LocalDateTime objects to seconds
            LocalDateTime deletedAtTruncated = transferMarketBidUserDo.getDeletedAt().truncatedTo(ChronoUnit.MINUTES);
            LocalDateTime auctionEndTruncated = transferMarketBidUserDo.getAuctionEnd().truncatedTo(ChronoUnit.MINUTES);
            boolean isDeletedAtAfterOrEqualAuctionEnd = deletedAtTruncated.isAfter(auctionEndTruncated) || deletedAtTruncated.isEqual(auctionEndTruncated);
            if (isDeletedAtAfterOrEqualAuctionEnd) {
                //if the Bid still open, and it's deleted after or equal the auctionEnd  it means expired
                if (Objects.equals(transferMarketBidUserDo.getStatus(), BidStatus.OPEN.toString()) ) {
                    return TransferStatus.EXPIRED;
                }
            }
        }
        return null;
    }
}
