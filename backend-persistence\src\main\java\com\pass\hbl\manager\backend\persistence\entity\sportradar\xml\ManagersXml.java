package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "Managers")
@NoArgsConstructor
@Data
public class ManagersXml {

    @JacksonXmlProperty(localName = "Team1")
    private ManagerTeam1Xml team1;

    @JacksonXmlProperty(localName = "Team2")
    private ManagerTeam2Xml team2;
}
