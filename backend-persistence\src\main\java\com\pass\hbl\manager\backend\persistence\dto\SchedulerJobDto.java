package com.pass.hbl.manager.backend.persistence.dto;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@JsonRootName("SchedulerJob")
@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SchedulerJobDto extends AbstractDto<SchedulerJobDto, String> {

    public static final String PARAM_DEPENDENCY = "dependency";
    public static final String PARAM_WAIT_FOR_DEPENDENCY = "waitForDependency";
    public static final String PARAM_FOLLOWED_BY = "followedBy";

    @Schema(name = "id", description = "UUID of the job", required = true)
    @Size(min = 36, max = 36)
    @NotBlank
    private String id;

    @Schema(name = "name", description = "Name of the job", required = true)
    @Size(max = 256)
    @NotBlank
    private String name;

    @Schema(name = "description", description = "Description of the job", required = true)
    @Size(max = 256)
    @NotBlank
    private String description;

    @Schema(name = "mode", description = "Mode of the job", required = true, implementation = SchedulerJobMode.class)
    @NotNull
    private SchedulerJobMode mode;

    @Schema(name = "status", description = "Status of the job", required = true, implementation = SchedulerJobStatus.class)
    @NotNull
    private SchedulerJobStatus status;

    @Schema(name = "cronExpression", description = "cron expression of the job")
    @Size(max = 64)
    private String cronExpression;

    @Schema(name = "timeStarted", description = "start time of the job", implementation = LocalDateTime.class)
    private LocalDateTime timeStarted;

    @Schema(name = "timeFinished", description = "end time of the job", implementation = LocalDateTime.class)
    private LocalDateTime timeFinished;

    @Schema(name = "timeLastExecution", description = "time the job finished last", implementation = LocalDateTime.class)
    private LocalDateTime timeLastExecution;

    @Schema(name = "timeLastSuccess", description = "time the job successfully finished", implementation = LocalDateTime.class)
    private LocalDateTime timeLastSuccess;

    @Schema(name = "lastRunResult", description = "Result of the last run", implementation = SchedulerJobResult.class, required = true)
    @NotNull
    private SchedulerJobResult lastRunResult;

    @Schema(name = "lastRunMessage", description = "Message of the last run")
    private String lastRunMessage;

    @Schema(name = "lastRunDuration", description = "Duration in seconds of the last run")
    private Integer lastRunDuration;

    @Schema(name = "jobClass", description = "Java class implementation of the job", required = true)
    @NotBlank
    @Size(max = 256)
    private String jobClass;

    @Schema(name = "parameters", description = "Parameters of the job")
    private Map<String, String> parameters = new HashMap<>();

    public SchedulerJobDto() {
    }
}
