package com.pass.hbl.manager.backend.persistence.repository.admin;

import com.pass.hbl.manager.backend.persistence.entity.admin.AdminParameter;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Optional;
import java.util.UUID;

public interface AdminParameterRepository extends PagingAndSortingRepository<AdminParameter, UUID> {
    Optional<AdminParameter> findByName(String paramName);
    void deleteByName(String name);
}
