package com.pass.hbl.manager.backend.persistence.job;

import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobMode;
import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobStatus;
import com.pass.hbl.manager.backend.persistence.entity.AbstractSchedulerJobEntity;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminSchedulerJob;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.SchedulingException;
import com.pass.hbl.manager.backend.persistence.service.AbstractSchedulerService;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static java.util.Objects.nonNull;

@Getter
@Setter
@Slf4j
public abstract class AbstractJob implements Runnable {

    protected enum RunningMode {NORMAL, TERMINATE, TERMINATE_IMMEDIATE}

    private UUID id;

    private final ParameterService parameterService;

    private SchedulerJobMode mode;

    private RunningMode runningMode;

    private boolean running;

    private AbstractSchedulerService<?, ?, ?> service;
    private Map<String, String> additionalParams;

    protected AbstractJob(ParameterService parameterService) {
        this.parameterService = parameterService;
        this.additionalParams = new HashMap<>();
    }

    protected abstract void work() throws Exception;

    protected abstract void init() throws Exception;

    protected abstract void tearDown() throws Exception;

    protected abstract void terminate() throws Exception;

    public synchronized void initialize(UUID id, AbstractSchedulerService<?, ?, ?> service, SchedulerJobMode mode) {
        this.setId(id);
        this.service = service;
        this.setMode(mode);
        this.setRunningMode(RunningMode.NORMAL);
    }

    @Override
    public void run() {
        AbstractSchedulerJobEntity entity;
        UUID id = this.getId();
        if (id == null) {
            log.error(getClass() + ": job id is null. Will not execute.");
            return;
        }

        try {
            entity = service.getSchedulerJob(id.toString());
        } catch (Throwable e) {
            log.error(getClass() + ": Cannot read job with id=" + id, e);
            return;
        }

        try {
            if (service.isJobRunning(id.toString())) {
                log.warn(getClass() + ": Job [id=" + entity.getId() + ", name=" + entity.getName() + "] is currently running. Skipping this execution not to run twice.");
                return;
            }
        } catch (Throwable e) {
            log.error(getClass() + ": Job [id=" + entity.getId() + ", name=" + entity.getName() + "] is currently running. Skipping this execution not to run twice.", e);
        }

        try {
            if (entity.getStatus() == SchedulerJobStatus.RUNNING) {
                log.info(getClass() + ": Job is currently running. Exiting.");
                return;
            }

            if (entity.getStatus() == SchedulerJobStatus.DISABLED) {
                log.info(getClass() + ": Job is disabled. Exiting.");
                return;
            }

            if (service.isDependencyRunning(entity)) {
                log.info(getClass() + ": Waiting for dependency job " + entity.getParameter(AdminSchedulerJob.PARAM_DEPENDENCY) + " to finish. Exit job for this period.");
                return;
            }

            this.setRunning(true);
            this.init();
            service.running(this);
            this.work();
            service.success(this);

        } catch (Throwable e) {
            try {
                service.failure(this, e);
            } catch (Throwable schedulingException) {
                log.error(getClass() + ": Could now persist job error state. Job scheduler may now be inconsistent, manual action required.", schedulingException);
            }
        } finally {
            try {
                service.finished(id, this.getMode());
            } catch (Throwable e) {
                log.error(getClass() + ": Error finishing job [id=" + id + "]", e);
            }
            this.setRunning(false);
            try {
                tearDown();
            } catch (Throwable e) {
                log.error(getClass() + ": Error tearing down job [id=" + id + "]", e);
            }
        }
    }

    protected LogMessageService getLogService() {
        return service.getLogMessageService();
    }

    protected String getParameter(String name) throws SchedulingException, FormatException {
        String additionalParameter = getAdditionalParameter(name);
        return nonNull(additionalParameter)? additionalParameter:  getService().getParameter(getId().toString(), name);
    }

    protected boolean getParameterAsBoolean(@SuppressWarnings("SameParameterValue") String name) throws SchedulingException, FormatException {
        String additionalParameter = getAdditionalParameter(name);
        return nonNull(additionalParameter)? Util.toBoolean(additionalParameter): getService().getParameterAsBoolean(getId().toString(), name);
    }

    protected Integer getParameterAsInteger(@SuppressWarnings("SameParameterValue") String name) throws SchedulingException, FormatException {
        String additionalParameter = getAdditionalParameter(name);
        return nonNull(additionalParameter)? Util.toInteger(additionalParameter):getService().getParameterAsInteger(getId().toString(), name);
    }

    private String getAdditionalParameter(String paramName) {
        if (additionalParams == null) {
            additionalParams = new HashMap<>();
        }
        return additionalParams.get(paramName);
    }

    protected List<String> getParameterList(String name) throws SchedulingException, FormatException {
        String value = getParameter(name);
        return StringUtils.isEmpty(value) ? Collections.emptyList() : Arrays.stream(StringUtils.split(value, '+')).toList();
    }

    public boolean terminate(boolean interruptIfRunning) {
        this.setRunningMode(interruptIfRunning ? RunningMode.TERMINATE_IMMEDIATE : RunningMode.TERMINATE);
        try {
            terminate();
        } catch (Exception e) {
            log.error("Error terminating job " + id + ". Process may continue running. Please check manually", e);
            return false;
        }
        return true;
    }

    public Duration getMaxRunDelay() {
        return Duration.ZERO;
    }

    public Duration getMaxAge() {
        return Duration.of(365, ChronoUnit.DAYS);
    }

    protected boolean isTerminate() {
        return this.getRunningMode() == RunningMode.TERMINATE_IMMEDIATE || this.getRunningMode() == RunningMode.TERMINATE;
    }

    protected boolean isTerminateImmediate() {
        return this.getRunningMode() == RunningMode.TERMINATE_IMMEDIATE;
    }
}
