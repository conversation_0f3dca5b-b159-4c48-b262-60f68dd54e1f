package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

@JsonRootName("Player match score")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Player match score")
public class PlayerRoundScoreDetailsDto {

    @Schema(description = "player info", example = "Doe", required = true)
    private PlayerInfoDto player;

    @NotNull
    @ToString.Exclude
    @Schema(description = "club of the player", required = true)
    private ClubDto club;

    @Schema(description = "total score gained overall up to the given match", required = true)
    private int totalScore;

    @Schema(description = "money gained overall up to the given match", required = true)
    private int valueGrowth;

    @Schema(description = "opponent club by match id", required = true)
    Map<String, ClubDto> opponents;

    @Schema(description = "player match events the given match", required = true)
    private List<PlayerMatchEventDto> playerMatchEvents;
}
