package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@JsonRootName("UserMessagingToken")
@Getter
@Setter
@AllArgsConstructor
@Schema(description = "Token assignment to a user")
public class UserMessagingTokenDto extends AbstractDto<UserMessagingTokenDto, String> {

    @Schema(description = "user token id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "user owning the token", required = true)
    @NotNull
    private UserDto user;

    @Schema(description = "user token", required = true)
    @Size(max = 4096)
    @NotNull
    private String token;
}
