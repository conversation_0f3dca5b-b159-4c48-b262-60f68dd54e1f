package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrCompetitor;
import com.pass.hbl.manager.backend.persistence.repository.sportradar.SrCompetitorRepository;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

@Component
public class CompetitorConverter extends AbstractConverter<String, SrCompetitor> {

    private final SrCompetitorRepository repository;

    public CompetitorConverter(SrCompetitorRepository repository) {
        this.repository = repository;
    }

    @Override
    protected SrCompetitor convert(String source) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        return repository.findById(source).orElse(null);
    }
}
