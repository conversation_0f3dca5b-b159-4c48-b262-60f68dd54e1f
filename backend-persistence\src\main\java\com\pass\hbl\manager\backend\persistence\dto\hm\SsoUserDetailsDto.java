package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;
import java.util.Map;

@JsonRootName("SsoUserDetails")
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(description = "User details returned from Single Sign-On interface")
public class SsoUserDetailsDto {

    @Schema(description = "Long id the user is registered with", example = "subject_CU9bfbc5ee54d744b8b2e4d2398f8a1ec2", required = true)
    @NotBlank
    @Size(min = 46, max = 46)
    private String id;

    @NotNull
    @Schema(description = "user attributes", required = true)
    private SsoUserAttributesDto attributes;

    /*@NotNull
    @Schema(description = "user creation date", example = "2018-07-14T17:45:55.948353600", required = true)
    @JsonProperty("created_at")
    private ZonedDateTime createdAt;

    @NotNull
    @JsonProperty("updated_at")
    @Schema(description = "user details update date", example = "2018-07-14T17:45:55.948353600", required = true)
    private ZonedDateTime updatedAt;*/

    @JsonCreator
    public SsoUserDetailsDto() {
    }

    @Override
    public String toString() {
        return "SsoUserDetailsDto{" +
                "id='" + id + '\'' +
                ", attributes=" + attributes +
                '}';
    }
}
