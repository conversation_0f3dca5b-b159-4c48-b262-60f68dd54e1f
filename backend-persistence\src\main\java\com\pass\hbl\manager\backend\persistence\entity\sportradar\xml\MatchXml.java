package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "Match")
@NoArgsConstructor
@Data
public class MatchXml {

    @JacksonXmlProperty(localName = "Id", isAttribute = true)
    private Integer id;

    @JacksonXmlProperty(localName = "Removed", isAttribute = true)
    private Boolean removed;

    @JacksonXmlProperty(localName = "BetradarMatchId", isAttribute = true)
    private Integer betradarMatchId;

    @JacksonXmlProperty(localName = "attendance", isAttribute = true)
    private Integer attendance;

    @JacksonXmlProperty(localName = "MatchDate")
    private MatchDateXml matchDate;

    @JacksonXmlProperty(localName = "Team1")
    private Team1Xml team1;

    @JacksonXmlProperty(localName = "Team2")
    private Team2Xml team2;

    @JacksonXmlProperty(localName = "Status")
    private StatusXml status;

    @JacksonXmlProperty(localName = "Winner")
    private WinnerXml winner;

    @JacksonXmlProperty(localName = "LastGoal")
    private LastGoalXml lastGoal;

    @JacksonXmlProperty(localName = "CurrentPeriodStart")
    private CurrentPeriodStartXml currentPeriodStart;

    @JacksonXmlProperty(localName = "Time")
    private TimeXml time;

    @JacksonXmlProperty(localName = "Scores")
    private ScoresXml scores;

    @JacksonXmlProperty(localName = "Lineups")
    private LineupsXml lineups;

    @JacksonXmlProperty(localName = "Timeline")
    private TimelineXml timeline;

    @JacksonXmlProperty(localName = "Managers")
    private ManagersXml managers;

    @JacksonXmlProperty(localName = "Officials")
    private OfficialsXml officials;

    @JacksonXmlProperty(localName = "Venue")
    private VenueXml venue;
}
