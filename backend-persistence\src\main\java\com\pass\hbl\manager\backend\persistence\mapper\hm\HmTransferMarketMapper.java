package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.BidDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.TransferMarketDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.UserDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.*;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.modelmapper.AbstractConverter;
import org.modelmapper.Condition;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;


@Component
@Slf4j
public class HmTransferMarketMapper extends AbstractMapper<HmTransferMarket, TransferMarketDto> {

    private final HmUserMapper userMapper;
    private final HmBidMapper bidMapper;
    private final HmPlayerMapper playerMapper;

    public HmTransferMarketMapper(HmUserMapper userMapper, HmBidMapper bidMapper, HmPlayerMapper playerMapper) {
        super(HmTransferMarket.class, TransferMarketDto.class);
        this.userMapper = userMapper;
        this.bidMapper = bidMapper;
        this.playerMapper = playerMapper;
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected void customizeInit() {

        getModelMapper().getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);

        TypeMap<HmTransferMarket, TransferMarketDto> e2d = getModelMapper().createTypeMap(HmTransferMarket.class, TransferMarketDto.class);

        Condition<HmTransferMarket, TransferMarketDto> conditionOwnerIsNotDeleted = context -> {
            try {
                //noinspection ResultOfMethodCallIgnored
                context.getSource().getOwner().getId().toString();
                return true;
            } catch (Exception e) {
                return false;
            }
        };

        Converter<HmLeague, String> leagueStringConverter = new AbstractConverter<>() {
            @Override
            protected String convert(HmLeague hmLeague) {
                return hmLeague == null ? null : hmLeague.getId().toString();
            }
        };
        Converter<HmPlayer, String> playerStringConverter = new AbstractConverter<>() {
            @Override
            protected String convert(HmPlayer source) {
                return source == null ? null : source.getId().toString();
            }
        };
        Converter<HmPlayer, PlayerDto> playerConverter = new AbstractConverter<>() {
            @Override
            protected PlayerDto convert(HmPlayer source) {
                return playerMapper.mapToDto(source);
            }
        };
        Converter<HmTransferMarket, UserDto> userConverter = new AbstractConverter<>() {
            @Override
            protected UserDto convert(HmTransferMarket source) {
                try {
                    return source == null ? null : userMapper.mapToDto(source.getOwner());
                } catch (Exception e) {
                    return UserDto.getAnonymous();
                }
            }
        };
        Converter<List<HmTransferMarketBid>, List<BidDto>> bidConverter = new AbstractConverter<>() {
            @Override
            protected List<BidDto> convert(List<HmTransferMarketBid> source) {
                if (CollectionUtils.isEmpty(source)) {
                    return Collections.emptyList();
                }
                return source.stream().map(bidMapper::mapToDto).toList();
            }
        };

        e2d.addMappings(mapper -> mapper.map(HmTransferMarket::getId, TransferMarketDto::setId));
        e2d.addMappings(mapper -> mapper.when(conditionOwnerIsNotDeleted).using(userConverter).map(HmTransferMarket::self, TransferMarketDto::setOwner));
        e2d.addMappings(mapper -> mapper.using(leagueStringConverter).map(HmTransferMarket::getLeague, TransferMarketDto::setLeagueId));
        e2d.addMappings(mapper -> mapper.using(playerConverter).map(HmTransferMarket::getPlayer, TransferMarketDto::setPlayer));
        e2d.addMappings(mapper -> mapper.using(playerStringConverter).map(HmTransferMarket::getPreviousPlayer, TransferMarketDto::setPreviousPlayerId));
        e2d.addMappings(mapper -> mapper.map(HmTransferMarket::getPrice, TransferMarketDto::setPrice));
        e2d.addMappings(mapper -> mapper.using(bidConverter).map(HmTransferMarket::getBids, TransferMarketDto::setBids));
        e2d.addMappings(mapper -> mapper.map(HmTransferMarket::getAuctionEnd, TransferMarketDto::setAuctionEnd));
    }

    @Override
    protected TransferMarketDto customizeMapToDto(TransferMarketDto transferMarketDto, HmTransferMarket transferMarket) {
        if (transferMarketDto.getOwner() == null) {
            transferMarketDto.setOwner(UserDto.getAnonymous());
        }
        return super.customizeMapToDto(transferMarketDto, transferMarket);
    }
}
