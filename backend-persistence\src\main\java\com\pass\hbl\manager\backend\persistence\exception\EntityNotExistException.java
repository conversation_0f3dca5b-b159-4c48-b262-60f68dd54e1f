package com.pass.hbl.manager.backend.persistence.exception;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EntityNotExistException extends CodedException {

    private String entityName;

    private String key;

    private String value;

    public EntityNotExistException(Class<?> entityClass, String value) {
        this (entityClass.getSimpleName(), value);
    }

    public EntityNotExistException(Class<?> entityClass, String key, String value) {
        this (entityClass.getSimpleName(), key, value);
    }

    public EntityNotExistException(String entityName, String value) {
        this(entityName, "id", value);
    }

    public EntityNotExistException(String entityName, String key, String value) {
        super(ExceptionCode.ENTITY_NOT_EXIST, "Entity " + entityName + " with " + key + "=" + value + " does not exist", entityName, key, value);
        this.entityName = entityName;
        this.key = key;
        this.value = value;
    }
}
