package com.pass.hbl.manager.backend.persistence.repository.datacore.masterdata;

import com.pass.hbl.manager.backend.persistence.entity.datacore.DcCompetitor;

import com.pass.hbl.manager.backend.persistence.entity.datacore.DcSeason;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface DcCompetitorRepository extends PagingAndSortingRepository<DcCompetitor, String> {

    boolean existsByIdAndSeasonId(String competitorId, String seasonId);

    List<DcCompetitor> findBySeasonId(String seasonId);

    Optional<DcCompetitor> findByIdAndSeason(String competitorId, DcSeason season);
}