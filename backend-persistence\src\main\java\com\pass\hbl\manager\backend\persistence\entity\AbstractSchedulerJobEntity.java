package com.pass.hbl.manager.backend.persistence.entity;

import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobMode;
import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobResult;
import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobStatus;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

@Getter
@Setter
@ToString
@NoArgsConstructor
@MappedSuperclass
public class AbstractSchedulerJobEntity extends AbstractEntity {

    public static final String PARAM_DEPENDENCY = "dependency";
    public static final String PARAM_WAIT_FOR_DEPENDENCY = "waitForDependency";
    public static final String PARAM_FOLLOWED_BY = "followedBy";

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @Column(name = "name")
    @Size(max = 256)
    @NotBlank
    private String name;

    @ToString.Exclude
    @Column(name = "description")
    @Size(max = 256)
    @NotBlank
    private String description;

    @ToString.Exclude
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "mode")
    private SchedulerJobMode mode;

    @ToString.Exclude
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private SchedulerJobStatus status;

    @ToString.Exclude
    @Size(max = 64)
    @Column(name = "cron_expression")
    private String cronExpression;


    @ToString.Exclude
    @Column(name = "time_started")
    private LocalDateTime timeStarted;

    @ToString.Exclude
    @Column(name = "time_finished")
    private LocalDateTime timeFinished;

    @ToString.Exclude
    @Column(name = "time_last_execution")
    private LocalDateTime timeLastExecution;

    @ToString.Exclude
    @Column(name = "time_last_success")
    private LocalDateTime timeLastSuccess;

    @ToString.Exclude
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "last_run_result")
    private SchedulerJobResult lastRunResult;

    @ToString.Exclude
    @Column(name = "last_run_message")
    private String lastRunMessage;

    @ToString.Exclude
    @Column(name = "last_run_duration")
    private Integer lastRunDuration;

    @NotBlank
    @Size(max = 256)
    @Column(name = "job_class")
    private String jobClass;

    @Size(max = 256)
    @Column(name = "parameters")
    private String parameters;

    @Column(name = "one_time_job")
    private boolean oneTimeJob;

    @Column(name = "active_profile")
    private String activeProfile;


    public AbstractSchedulerJobEntity(String name, String description, SchedulerJobMode mode, SchedulerJobStatus status, SchedulerJobResult lastRunResult, String jobClass, boolean oneTimeJob, String activeProfile) {
        this.name = name;
        this.description = description;
        this.mode = mode;
        this.status = status;
        this.lastRunResult = lastRunResult;
        this.jobClass = jobClass;
        this.oneTimeJob = oneTimeJob;
        this.activeProfile = activeProfile;
    }


    public boolean getParameterAsBoolean(String paramName) {
        return Util.toBoolean(getParameter(paramName));
    }

    public Integer getParameterAsInteger(String paramName) throws FormatException {
        return Util.toInteger(getParameter(paramName));
    }

    public String getParameter(String paramName) {
        return getParameterMap().get(paramName);
    }

    public Map<String, String> getParameterMap() {
        return Util.convertToMap(parameters);
    }

    public void setParameterMap(Map<String, String> map) {
        setParameters(Util.convertToString(map));
    }
}
