package com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.dto.hm.Position;
import com.pass.hbl.manager.backend.persistence.exception.ImportException;
import lombok.SneakyThrows;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class PositionConverter extends AbstractConverter<String, Position> {
    @SneakyThrows
    @Override
    protected Position convert(String source) {
        if (!StringUtils.hasText(source)) {
            return null;
        }
        try {
            return Position.valueOf(source);
        } catch (IllegalArgumentException e) {
            throw new ImportException(Datasource.SPORTRADAR, "Received unknown player position %s from Sportradar", e, source);
        }
    }
}
