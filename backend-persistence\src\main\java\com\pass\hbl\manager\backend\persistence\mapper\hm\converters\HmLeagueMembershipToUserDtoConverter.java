package com.pass.hbl.manager.backend.persistence.mapper.hm.converters;

import com.pass.hbl.manager.backend.persistence.dto.hm.UserDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeagueMembership;
import com.pass.hbl.manager.backend.persistence.mapper.hm.HmUserMapper;
import org.modelmapper.AbstractConverter;

public class HmLeagueMembershipToUserDtoConverter extends AbstractConverter<HmLeagueMembership, UserDto> {

    private final HmUserMapper hmUserMapper;

    public HmLeagueMembershipToUserDtoConverter(HmUserMapper hmUserMapper) {
        this.hmUserMapper = hmUserMapper;
    }

    @Override
    protected UserDto convert(HmLeagueMembership source) {
        if (source == null) {
            return null;
        }
        try {
            return hmUserMapper.mapToDto(source.getUserProfile());
        } catch (Exception e) {
            // happens if member is deleted
            return UserDto.getAnonymous();
        }
    }
}
