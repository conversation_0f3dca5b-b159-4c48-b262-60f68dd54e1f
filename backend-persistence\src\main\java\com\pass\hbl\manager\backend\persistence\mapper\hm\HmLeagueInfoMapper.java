package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.LeagueInfoDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.service.hm.helpers.LeagueHelper;
import org.springframework.stereotype.Component;

@Component
public class HmLeagueInfoMapper extends AbstractMapper<HmLeague, LeagueInfoDto> {

    private final LeagueHelper leagueHelper;

    public HmLeagueInfoMapper(LeagueHelper leagueHelper) {
        super(HmLeague.class, LeagueInfoDto.class);
        this.leagueHelper = leagueHelper;
    }

    @Override
    protected LeagueInfoDto customizeMapToDto(LeagueInfoDto leagueInfoDto, HmLeague league) {
        leagueInfoDto.setActive(league.isActive());
        leagueInfoDto.setMemberCount(leagueHelper.getLeagueSizeExcludingOnHoldMembers(league));
        return super.customizeMapToDto(leagueInfoDto, league);
    }
}
