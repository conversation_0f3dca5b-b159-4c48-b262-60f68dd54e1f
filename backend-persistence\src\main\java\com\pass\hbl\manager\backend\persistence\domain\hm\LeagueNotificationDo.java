package com.pass.hbl.manager.backend.persistence.domain.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.NotificationEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Domain object for transfer market push notification
 */
@Getter
@Setter
public class LeagueNotificationDo extends AbstractNotificationDo {

    private String leagueId;

    private String userId;

    private String picture;

    public LeagueNotificationDo(String leagueId, String userId, NotificationEvent notificationEvent, String picture) {
        this.leagueId = leagueId;
        this.userId = userId;
        this.notificationEvent = notificationEvent;
        this.picture = picture;
    }
}