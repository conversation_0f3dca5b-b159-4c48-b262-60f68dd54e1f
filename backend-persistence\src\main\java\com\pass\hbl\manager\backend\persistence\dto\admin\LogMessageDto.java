package com.pass.hbl.manager.backend.persistence.dto.admin;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@JsonRootName("LogMessage")
@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LogMessageDto extends AbstractDto<LogMessageDto, String> {

    @Schema(name = "id", description = "UUID of the job", required = true)
    @Size(min = 36, max = 36)
    @NotBlank
    private String id;

    @Schema(name = "name", description = "Log message name or process", required = true)
    @Size(max = 1024)
    @NotBlank
    private String name;

    @Schema(name = "message", description = "Log message", required = true)
    @Size(max = 1024)
    @NotBlank
    private String message;

    @Schema(name = "message", description = "Log exception")
    private String exception;

    @Schema(name = "level", description = "level of the log message", required = true)
    @NotNull
    private LogMessageLevel level;

    @Schema(name = "timestamp", description = "timestamp of the log message", required = true)
    @NotNull
    private LocalDateTime timestamp;
}
