package com.pass.hbl.manager.backend.persistence.dto.datacore;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonRootName("get datacore token request")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Schema(description = "Get datacore access token request data")
public class DataCoreTokenRequestDto {

    @NotNull
    @Schema(description = "credentialId", required = true)
    private String credentialId;

    @NotNull
    @Schema(description = "credentialSecret", required = true)
    private String credentialSecret;

    @NotNull
    @Schema(description = "organization", required = true)
    private Organization organization;

    @NotNull
    @Schema(description = "sport", example = "handball", required = true)
    private String sport;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Organization {
        private List<String> id;
    }
}
