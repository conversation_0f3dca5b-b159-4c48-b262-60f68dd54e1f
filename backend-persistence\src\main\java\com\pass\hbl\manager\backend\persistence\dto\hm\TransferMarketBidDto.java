package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@JsonRootName("Transfer Market Bid")
@Getter
@Setter
@NoArgsConstructor
@Schema(description = "Transfer market bid info")
public class TransferMarketBidDto extends AbstractDto<TransferMarketBidDto, String> {

    @Schema(description = "Bid id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true, accessMode = Schema.AccessMode.READ_ONLY)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @NotNull
    @Schema(description = "User offering the bid", required = true)
    private UserDto bidder;

    @NotNull
    @Schema(description = "User owning the player", required = true)
    private String ownerId;

    @NotNull
    @Schema(description = "league id", required = true)
    private String leagueId;

    @NotNull
    @Schema(description = "player id", required = true)
    private String playerId;

    @Min(0)
    @Schema(description = "Value of the bid", required = true)
    private int bid;

    @NotNull
    @Schema(description = "Status of the bid", example = "ACCEPTED", required = true )
    private BidStatus status;

    public TransferMarketBidDto(String id, UserDto bidder, String ownerId, String leagueId, String playerId, int bid, BidStatus status) {
        this.id = id;
        this.bidder = bidder;
        this.ownerId = ownerId;
        this.leagueId = leagueId;
        this.playerId = playerId;
        this.bid = bid;
        this.status = status;
    }
}
