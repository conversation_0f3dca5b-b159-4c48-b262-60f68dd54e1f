package com.pass.hbl.manager.backend.admin.job.hbl;

import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.SchedulingException;
import com.pass.hbl.manager.backend.persistence.exception.TechnicalException;
import com.pass.hbl.manager.backend.persistence.job.ScheduledJob;
import com.pass.hbl.manager.backend.persistence.job.admin.AdminAbstractJob;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.hbl.DownloadPictureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
@ScheduledJob(description = "Download player photos", parameters = "url=https://images.dc.prod.cloud.atriumsports.com/%s/%s?size=raw")
public class DownloadPlayerPhotosJob extends AdminAbstractJob {

    private final DownloadPictureService service;

    public DownloadPlayerPhotosJob(ParameterService parameterService, DownloadPictureService service) {
        super(parameterService);
        this.service = service;
    }

    @Override
    protected void work() throws SchedulingException, FormatException {
        String url = getParameter("url");

        // Update DataCore-Api: add suffix to the url to indicate the file size
        url = url + "=raw";
        service.checkPlayers(url, getParameterAsBoolean("overwrite"), getParameterAsBoolean("activeOnly"));
    }

    @Override
    protected void init() {

    }

    @Override
    protected void tearDown() {

    }

    @Override
    protected void terminate() {
        service.cancel();
    }
}
