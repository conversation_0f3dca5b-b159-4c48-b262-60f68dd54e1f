package com.pass.hbl.manager.backend.admin.job.sportradar;

import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobMode;
import com.pass.hbl.manager.backend.persistence.job.ScheduledJob;
import com.pass.hbl.manager.backend.persistence.job.admin.AdminAbstractJob;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.datacore.masterdata.ImportMatchDataCoreService;
import com.pass.hbl.manager.backend.persistence.service.sportradar.masterdata.ImportMatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

import static com.pass.hbl.manager.backend.admin.job.sportradar.ImportSeasonJob.PARAM_DATACORE;

@Slf4j
@Component
@ScheduledJob(description = "Import matches from Sportradar", parameters = ImportMatchJob.PARAM_CURRENT_ONLY + "=true;" + ImportMatchJob.PARAM_TRANSFER_ONLY + "=false;" + PARAM_DATACORE + "=true", mode = SchedulerJobMode.CRON, cronExpression = "0 0 0 ? * SUN")
public class ImportMatchJob extends AdminAbstractJob {

    public static final String PARAM_CURRENT_ONLY = "currentOnly";
    public static final String PARAM_ROUND = "round";

    public static final String PARAM_MATCH = "matchId";

    public static final String PARAM_TRANSFER_ONLY = "transferOnly";
    private final ImportMatchService importMatchService;
    private final ImportMatchDataCoreService importMatchDataCoreService;
    private final Environment environment;
    private boolean isDataCoreApi;

    public ImportMatchJob(ImportMatchService importMatchService, ParameterService parameterService, ImportMatchDataCoreService importMatchDataCoreService, Environment environment) {
        super(parameterService);
        this.importMatchService = importMatchService;
        this.importMatchDataCoreService = importMatchDataCoreService;
        this.environment = environment;
    }

    @Override
    protected void work() throws Exception {
        boolean paramCurrentOnly = getParameterAsBoolean(PARAM_CURRENT_ONLY);
        Integer paramRound = getParameterAsInteger(PARAM_ROUND);
        String paramDcMatch = getParameter(PARAM_MATCH);
        Integer paramMatch = getParameterAsInteger(PARAM_MATCH);
        boolean paramTransferOnly = getParameterAsBoolean(PARAM_TRANSFER_ONLY);
        if (isDataCoreApi) {
            log.info("SportRadar data imported from the DataCore Rest-Api");
            importMatchDataCoreService.start(
                    paramCurrentOnly,
                    paramRound,
                    paramDcMatch,
                    paramTransferOnly
            );
        } else {
            importMatchService.start(
                    paramCurrentOnly,
                    paramRound,
                    paramMatch,
                    paramTransferOnly
            );
        }
    }

    @Override
    protected void init() throws Exception {
        this.isDataCoreApi = getParameterAsBoolean(PARAM_DATACORE);
        List<String> activeProfiles = Arrays.asList(environment.getActiveProfiles());
        if (isDataCoreApi) {
            importMatchDataCoreService.initDataCoreDefaults(activeProfiles);
        } else {
            importMatchService.initSportradarDefaults();
        }

    }

    @Override
    protected void tearDown() {

    }

    @Override
    protected void terminate() throws Exception {
        importMatchService.cancel();
    }
}
