package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.BidDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.UserDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmTransferMarketBid;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.AbstractConverter;
import org.modelmapper.Condition;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class HmBidMapper extends AbstractMapper<HmTransferMarketBid, BidDto> {

    private final HmUserMapper userMapper;

    public HmBidMapper(HmUserMapper userMapper) {
        super(HmTransferMarketBid.class, BidDto.class);
        this.userMapper = userMapper;
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected void customizeInit() {

        getModelMapper().getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);

        TypeMap<HmTransferMarketBid, BidDto> e2d = getModelMapper().createTypeMap(HmTransferMarketBid.class, BidDto.class);

        Condition<HmTransferMarketBid, BidDto> conditionBidderIsNotDeleted = context -> {
            try {
                //noinspection ResultOfMethodCallIgnored
                context.getSource().getBidder().getId().toString();
                return true;
            } catch (Exception e) {
                return false;
            }
        };
        Converter<HmTransferMarketBid, UserDto> userConverter = new AbstractConverter<>() {
            @Override
            protected UserDto convert(HmTransferMarketBid source) {
                try {
                    return source == null ? null : userMapper.mapToDto(source.getBidder());
                } catch (Exception e) {
                    return UserDto.getAnonymous();
                }
            }
        };

        e2d.addMappings(mapper -> mapper.map(HmTransferMarketBid::getId, BidDto::setId));
        e2d.addMappings((mapper -> mapper.when(conditionBidderIsNotDeleted).using(userConverter).map(HmTransferMarketBid::self, BidDto::setBidder)));
        e2d.addMappings(mapper -> mapper.map(HmTransferMarketBid::getBid, BidDto::setBid));
        e2d.addMappings(mapper -> mapper.map(HmTransferMarketBid::getStatus, BidDto::setStatus));
        e2d.addMappings(mapper -> mapper.map(HmTransferMarketBid::getCreatedAt, BidDto::setCreatedAt));
    }

    @Override
    protected BidDto customizeMapToDto(BidDto bidDto, HmTransferMarketBid hmTransferMarketBid) {
        if (bidDto.getBidder() == null) {
            bidDto.setBidder(UserDto.getAnonymous());
        }
        return super.customizeMapToDto(bidDto, hmTransferMarketBid);
    }
}
