package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.pass.hbl.manager.backend.persistence.domain.datacore.FixtureStatus;
import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrMatchStatus;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrSportEventStatus;
import com.pass.hbl.manager.backend.persistence.exception.MappingException;
import lombok.Getter;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

public enum MatchStatus {

    /*
     * this mapping is not officially
     */

    NOT_STARTED         ( 1,  0, SrMatchStatus.NOT_STARTED,        SrSportEventStatus.NOT_STARTED),
    STARTED             ( 2, 20, SrMatchStatus.STARTED,            SrSportEventStatus.STARTED),
    POSTPONED           ( 3, 60, SrMatchStatus.POSTPONED,          SrSportEventStatus.POSTPONED),
    SUSPENDED           ( 4, 81, SrMatchStatus.PAUSE,              SrSportEventStatus.SUSPENDED),
    CANCELLED           ( 5, 70, SrMatchStatus.CANCELLED,          SrSportEventStatus.CANCELLED),
    START_DELAYED       ( 6, 61, SrMatchStatus.START_DELAYED,      SrSportEventStatus.DELAYED),
    PERIOD_1            ( 7,  6, SrMatchStatus._1ST_HALF,          SrSportEventStatus.LIVE),
    INTERRUPTED         ( 8, 80, SrMatchStatus.INTERRUPTED,        SrSportEventStatus.INTERRUPTED),
    ENDED               ( 9,100, SrMatchStatus.ENDED,              SrSportEventStatus.ENDED),
    PERIOD_2            (10,  7, SrMatchStatus._2ND_HALF,          SrSportEventStatus.LIVE),
    ABOUT_TO_START      (11,  0, SrMatchStatus.NOT_STARTED,        SrSportEventStatus.NOT_STARTED),
    BREAK               (12, 31, SrMatchStatus.HALFTIME,           SrSportEventStatus.LIVE),
    OVERTIME            (13, 40, SrMatchStatus.OVERTIME,           SrSportEventStatus.LIVE),
    PENALTIES           (14, 50, SrMatchStatus.PENALTIES,          SrSportEventStatus.LIVE),
    ABANDONED           (15, 90, SrMatchStatus.ABANDONED,          SrSportEventStatus.LIVE),
    WALKOVER            (16, 91, SrMatchStatus.ENDED,              SrSportEventStatus.ENDED),
    RETIRED             (17, 92, SrMatchStatus.ENDED,              SrSportEventStatus.ENDED),
    HOME_DEFAULTED      (18, 97, SrMatchStatus.ENDED,              SrSportEventStatus.ENDED),
    AWAY_DEFAULTED      (19, 98, SrMatchStatus.ENDED,              SrSportEventStatus.ENDED),
    ENDED_AFTER_OVERTIME(20,110, SrMatchStatus.AET,                SrSportEventStatus.ENDED),
    ENDED_AFTER_PENALTY (21,120, SrMatchStatus.AP,                 SrSportEventStatus.ENDED),
    TIMEOUT             (22, 30, SrMatchStatus.PAUSE,              SrSportEventStatus.LIVE),
    EXTRA_1ST           (23, 41, SrMatchStatus._1ST_EXTRA,         SrSportEventStatus.LIVE),
    EXTRA_2ND           (24, 42, SrMatchStatus._2ND_EXTRA,         SrSportEventStatus.LIVE),
    AWAITING_PENALTIES  (25, 34, SrMatchStatus.AWAITING_PENALTIES, SrSportEventStatus.LIVE),
    AWAITING_EXTRA      (26, 32, SrMatchStatus.AWAITING_EXTRA,     SrSportEventStatus.LIVE),
    EXTRA_TIME_HALFTIME (27, 40, SrMatchStatus.EXTRA_TIME_HALFTIME,SrSportEventStatus.LIVE);

    /*
     * unused
     * - mv2Status: closed
     */
    @Getter
    private final int orderNo;

    @Getter
    private final int liveCode;

    @Getter
    private final SrMatchStatus hv2MatchStatus;

    @Getter
    private final SrSportEventStatus hv2Status;

    MatchStatus(int orderNo, int liveCode, SrMatchStatus hv2MatchStatus, SrSportEventStatus hv2Status) {
        this.orderNo = orderNo;
        this.liveCode = liveCode;
        this.hv2MatchStatus = hv2MatchStatus;
        this.hv2Status = hv2Status;
    }

    public static MatchStatus getByDataCoreFixtureStatus(FixtureStatus fixtureStatus) throws MappingException {
        MatchStatus matchStatus = MatchStatus.NOT_STARTED;
        //ABANDONED, CANCELLED, CONFIRMED, DELAYED, FINISHED, INTERRUPTED, PENDING, PROTESTED, IN_PROGRESS, WARM_UP, ON_PITCH
        if (Objects.nonNull(fixtureStatus)) switch (fixtureStatus) {
            case ABANDONED -> matchStatus = MatchStatus.ABANDONED;
            case CANCELLED -> matchStatus = MatchStatus.CANCELLED;
            //TODO HBLMAN-565 check
            case CONFIRMED -> matchStatus = MatchStatus.ENDED;
            case DELAYED -> matchStatus = MatchStatus.START_DELAYED;
            case FINISHED -> matchStatus = MatchStatus.ENDED;
            case INTERRUPTED -> matchStatus = MatchStatus.INTERRUPTED;
            case PENDING -> matchStatus = MatchStatus.SUSPENDED;
            case PROTESTED -> matchStatus = MatchStatus.INTERRUPTED;
            case IN_PROGRESS -> matchStatus = MatchStatus.STARTED;
            case WARM_UP -> matchStatus = MatchStatus.ABOUT_TO_START;
            case ON_PITCH -> matchStatus = MatchStatus.STARTED;
            //TODO check Logic
            case SCHEDULED  -> matchStatus = MatchStatus.NOT_STARTED;
            case BYE  -> matchStatus = MatchStatus.RETIRED;
            case IF_NEEDED  -> matchStatus = MatchStatus.NOT_STARTED;
            default -> throw new MappingException("Cannot map live fixture status", Datasource.DATACORE, fixtureStatus.name(), MatchStatus.class);
        }
        return matchStatus;
    }

    public static MatchStatus getByLiveCode(int liveCode) throws MappingException {
        return Arrays.stream(MatchStatus.values())
                .sorted(Comparator.comparing(MatchStatus::getOrderNo))
                .filter(ms -> ms.liveCode == liveCode).findFirst()
                .orElseThrow(() -> new MappingException("Cannot map live status code", Datasource.SPORTRADAR, String.valueOf(liveCode), MatchStatus.class));
    }

    public static MatchStatus getByHv2MatchStatus(@NotNull SrMatchStatus matchStatus) throws MappingException {
        return Arrays.stream(MatchStatus.values())
                .sorted(Comparator.comparing(MatchStatus::getOrderNo))
                .filter(ms -> Objects.equals(ms.hv2MatchStatus, matchStatus)).findFirst()
                .orElseThrow(() -> new MappingException("Cannot map live status code", Datasource.SPORTRADAR, matchStatus.toString(), MatchStatus.class));
    }

    public static List<Integer> getLiveMatchEndStatusCodes() {
        return Arrays.stream(MatchStatus.values())
                .filter(s -> s.hv2Status == SrSportEventStatus.ENDED)
                .map(MatchStatus::getLiveCode)
                .sorted(Comparator.naturalOrder())
                .toList();
    }

    public static Set<SrMatchStatus> getMatchEndSrMatchStatus() {
        return Arrays.stream(MatchStatus.values())
                .filter(s -> s.hv2Status == SrSportEventStatus.ENDED)
                .map(MatchStatus::getHv2MatchStatus)
                .collect(Collectors.toSet());
    }
}
