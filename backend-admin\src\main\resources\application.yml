server:
  port: 8181
  servlet:
    context-path: /adminapi
  error:
    include-message: always
  forward-headers-strategy: NATIVE
spring:
  main:
    allow-circular-references: true
  application:
    name: HBL Manager Backend
  datasource:
    url: ************************************************
    username: hbl
    password: hbl
    driver-class-name: org.postgresql.Driver
    hikari:
      idle-timeout: 60000
      maximum-pool-size: 2000
      minimum-idle: 20
  mail:
    host: **********
    port: 25
    sender: <EMAIL>
    properties:
      mail.smtp.auth: false
      mail.smtp.starttls.enable: true
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 50
          time_zone: UTC
        ddl-auto: none
        order_insert: true
        order_update: true
    open-in-view: false
  liquibase:
    change-log: classpath:/db/db.changelog-master.yml
    enabled: true
management:
  server:
    port: 8182
  endpoint:
    chaosmonkey:
      enabled: true
    chaosmonkeyjmx:
      enabled: true
    health:
      group:
        custom:
          include: datasource,ping,diskspace
          show-components: always
          show-details: always
  metrics:
    tags:
      application: ${spring.application.name}
    distribution:
      percentiles:
        http:
          server:
            requests: 0.5, 0.9, 0.95, 0.99
  endpoints:
    web:
      exposure:
        include: "*"
chaos:
  monkey:
    enabled: true
    watcher:
      service: true
      controller: true
      rest-controller: true
      repository: true
      component: false
logging:
  config: file:/conf/logback-spring.xml
handball-manager:
  importer:
    sportradar-stage: production
    # noinspection SpellCheckingInspection
    api-key: n4gdhnm9d53kyypetumvkzjq
  datacore:
    scheduler-enabled: true
    staging-base-url: https://api.dc.stg.connect-nonprod.sportradar.dev/v1
    staging-auth-url: https://token.stg.connect-nonprod.sportradar.dev/v1/oauth2/rest/token
    staging-streaming-auth-url: https://token.stg.connect-nonprod.sportradar.dev/v1/stream/fixture/access
    staging-client-id: 137ZsT1EqzFPvlb1jOSaJaOXAz0SDx
    staging-client-secret: 1Sa7B4Mwa7AVn7WvF0Qh1qEijbhaJv
    # prod parameters
    prod-base-url: https://api.dc.connect.sportradar.com/v1
    prod-auth-url: https://token.connect.sportradar.com/v1/oauth2/rest/token
    prod-streaming-auth-url: https://token.connect.sportradar.com/v1/stream/fixture/access
    # Stage credentials
    prod-client-id: 341Zyfg2L2xqb4v1rNysOrp5NX40Ts
    prod-client-secret: azMT1zYyGfzBERncXCkh3zfqmlPvJF
  security:
    api-key: 0cb1ca734d51c1379545h29f52a337fd
    admin-user: admin
    admin-password: admin
  http-proxy:
    enabled: true
    url: http://10.10.0.250:3128
