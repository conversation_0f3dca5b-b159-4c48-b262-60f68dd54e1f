package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.hateoas.RepresentationModel;

@Getter
@Setter
public abstract class AbstractPictureDto<T extends RepresentationModel<? extends T>, IDType> extends AbstractDto<T, IDType> {

    @Schema(description = "Logo or picture as URL href")
    private String picture;
}
