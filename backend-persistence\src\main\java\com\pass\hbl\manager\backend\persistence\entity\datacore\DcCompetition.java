package com.pass.hbl.manager.backend.persistence.entity.datacore;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.AbstractSportradarEntity;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrCategory;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Table(name = "competition", schema = "datacore", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class DcCompetition extends AbstractSportradarEntity {

    @NotNull
    @Column(name = "name")
    private String name;

    @NotNull
    @Column(name = "age_group")
    private String ageGroup;

    @Column(name = "organization_id")
    private String organizationId;
}
