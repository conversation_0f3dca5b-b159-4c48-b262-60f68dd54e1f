package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.*;

import static java.util.Objects.nonNull;


@Table(name = "transfer_market", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@Entity
@ToString
@SQLDelete(sql = "UPDATE hm.transfer_market SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmTransferMarket extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "owner_id", referencedColumnName = "id", updatable = false, nullable = false)
    private HmUserProfile owner;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "league_id", referencedColumnName = "id", nullable = false, updatable = false)
    private HmLeague league;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "player_id", referencedColumnName = "id", nullable = false, updatable = false)
    private HmPlayer player;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "offer")
    private List<HmTransferMarketBid> bids;

    @NotNull
    @ToString.Exclude
    @Column(name = "price", updatable = false)
    private int price;

    @NotNull
    @ToString.Exclude
    @Column(name = "auction_end", updatable = false)
    private LocalDateTime auctionEnd;

    @ToString.Exclude
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "previous_player_id", referencedColumnName = "id", updatable = false)
    private HmPlayer previousPlayer;

    public HmTransferMarket(HmUserProfile owner, HmLeague league, HmPlayer player, int price, LocalDateTime auctionEnd) {
        this(owner, league, player, Collections.emptyList(), price, auctionEnd);
    }

    public HmTransferMarket(HmUserProfile owner, HmLeague league, HmPlayer player, List<HmTransferMarketBid> bids, int price, LocalDateTime auctionEnd) {
        this.owner = owner;
        this.league = league;
        this.player = player;
        this.bids = bids;
        this.price = price;
        this.auctionEnd = auctionEnd;
    }


    public List<HmTransferMarketBid> getBids() {
        if (nonNull(this.bids)) {
            List<HmTransferMarketBid> sortedBids = new ArrayList<>(this.bids);
            sortedBids.sort(Comparator.comparing(HmTransferMarketBid::getCreatedAt).reversed());
            return sortedBids;
        } else {
            return null;
        }
    }
}
