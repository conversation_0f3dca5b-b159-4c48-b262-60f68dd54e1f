package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.Event;
import com.pass.hbl.manager.backend.persistence.dto.hm.EventCharacteristic;
import com.pass.hbl.manager.backend.persistence.dto.hm.EventScope;
import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Table(name = "hpi_calculation", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@SQLDelete(sql = "UPDATE hm.hpi_calculation SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmHpiCalculation extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @Column(name = "sr_type")
    private Integer srType;

    @Column(name = "sr_extra")
    private Integer srExtra;

    @Column(name = "sr_position")
    private Integer srPosition;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "sr_scope")
    private EventScope scope;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "event")
    private Event event;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_characteristic")
    private EventCharacteristic eventCharacteristic;

    @Column(name = "score")
    private int score;

    @Transient
    private String description;
}
