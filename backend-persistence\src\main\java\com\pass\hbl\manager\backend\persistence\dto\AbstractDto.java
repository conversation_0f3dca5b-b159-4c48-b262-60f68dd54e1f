package com.pass.hbl.manager.backend.persistence.dto;

import lombok.ToString;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serial;
import java.io.Serializable;

@ToString
public abstract class AbstractDto<T extends RepresentationModel<? extends T>, IDType> extends RepresentationModel<T> implements Serializable {

    // relevant for Redis cache
    @Serial
    private static final long serialVersionUID = 1L;

    public abstract IDType getId();
}
