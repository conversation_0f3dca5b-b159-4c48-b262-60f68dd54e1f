package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.LeagueScoreDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeagueScore;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmRound;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.modelmapper.AbstractConverter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

@Component
public class HmLeagueScoreMapper extends AbstractMapper<HmLeagueScore, LeagueScoreDto> {

    public HmLeagueScoreMapper() {
        super(HmLeagueScore.class, LeagueScoreDto.class);
    }

    @Override
    protected void customizeInit() {

        TypeMap<HmLeagueScore, LeagueScoreDto> e2d = getModelMapper().createTypeMap(HmLeagueScore.class, LeagueScoreDto.class);

        getModelMapper().addConverter(new AbstractConverter<HmRound, String>() {
            @Override
            protected String convert(HmRound entity) {
                return entity.getId().toString();
            }
        });
        e2d.addMappings(mapper -> mapper.map(HmLeagueScore::getRound, LeagueScoreDto::setRoundId));

        getModelMapper().addConverter(new AbstractConverter<HmLeague, String>() {

            @Override
            protected String convert(HmLeague entity) {
                return entity.getId().toString();
            }
        });
        e2d.addMappings(mapper -> mapper.map(HmLeagueScore::getLeague, LeagueScoreDto::setLeagueId));
    }
}
