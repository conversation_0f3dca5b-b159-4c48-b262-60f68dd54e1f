package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Table(name = "player_of_month", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@SQLDelete(sql = "UPDATE hm.player_of_month SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmPlayerOfMonth extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "player_id", referencedColumnName = "id", updatable = false)
    private HmPlayer player;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "season_id", referencedColumnName = "id", updatable = false)
    private HmSeason season;

    @NotNull
    @Column(name = "month_number")
    private int monthNumber;

    @Column(name = "total_score")
    private Integer totalScore;

    public HmPlayerOfMonth(HmPlayer player, HmSeason season, int monthNumber, Integer totalScore) {
        this.player = player;
        this.season = season;
        this.monthNumber = monthNumber;
        this.totalScore = totalScore;
    }
}
