package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.UUID;

@Table(name = "player_in_club", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@SQLDelete(sql = "UPDATE hm.player_in_club SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmPlayerInClub extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false, cascade = {CascadeType.MERGE, CascadeType.REFRESH})
    @JoinColumn(name = "player_id", referencedColumnName = "id", updatable = false)
    private HmPlayer player;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "club_id", referencedColumnName = "id", updatable = false)
    private HmClub club;

    @NotNull
    @ToString.Exclude
    @Column(name = "joined")
    private LocalDateTime joined;

    @ToString.Exclude
    @Column(name = "`left`")
    private LocalDateTime left;


    public HmPlayerInClub(HmPlayer player, HmClub club, LocalDateTime joined) {
        this.player = player;
        this.club = club;
        this.joined = joined;
    }
}
