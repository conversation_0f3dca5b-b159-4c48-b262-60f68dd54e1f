package com.pass.hbl.manager.backend.persistence.job;

import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobMode;

import java.lang.annotation.*;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ScheduledJob {

    SchedulerJobMode mode() default SchedulerJobMode.MANUAL;

    String name() default "";

    String description();

    String cronExpression() default "";

    String parameters() default "";

    boolean oneTimeJob() default false;
}
