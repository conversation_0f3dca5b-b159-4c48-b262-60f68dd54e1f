package com.pass.hbl.manager.backend.persistence.entity.hm;


import com.pass.hbl.manager.backend.persistence.dto.hm.AwardCode;
import com.pass.hbl.manager.backend.persistence.entity.AbstractPictureEntity;
import com.pass.hbl.manager.backend.persistence.entity.shared.SharedLocalization;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;

@Table(name = "award", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@Entity
@ToString
@NoArgsConstructor
@SQLDelete(sql = "UPDATE hm.award SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmAward extends AbstractPictureEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @Column(name = "stackable")
    private Boolean stackable;

    @Column(name = "code")
    @Enumerated(EnumType.STRING)
    private AwardCode code;

    @Column(name = "display_order")
    private int displayOrder;

    @Column(name = "experience_points")
    private Integer experiencePoints;

    @Column(name = "money")
    private Integer money;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @JoinColumn(name = "entity_id", referencedColumnName = "id")
    private List<SharedLocalization> descriptions;

    public HmAward(Boolean stackable, AwardCode code, int displayOrder, Integer experiencePoints, Integer money, UUID picture) {
        this.stackable = stackable;
        this.code = code;
        this.displayOrder = displayOrder;
        this.experiencePoints = experiencePoints;
        this.money = money;
        this.setPicture(picture);
    }
}
