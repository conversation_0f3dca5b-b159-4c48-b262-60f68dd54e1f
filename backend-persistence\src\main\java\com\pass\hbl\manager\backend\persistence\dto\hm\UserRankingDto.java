package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

@JsonRootName("UserRanking")
@EqualsAndHashCode
@Getter
@Setter
@NoArgsConstructor
@Schema(description = "User info object used for manager ranking by league and round")
public class UserRankingDto implements Serializable {

    // relevant for Redis cache
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "Name of the member in the given league", required = true)
    @NotNull
    private UserDto user;

    @Schema(description = "Score of the player in the given league", example = "846", required = true)
    private int score;

    public UserRankingDto(UserDto user, int score) {
        this.user = user;
        this.score = score;
    }
}
