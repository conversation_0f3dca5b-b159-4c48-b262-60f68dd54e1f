package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "MatchDate")
@NoArgsConstructor
@Data
public class MatchDateXml {

    @JacksonXmlText
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss Z")
    private ZonedDateTime value;
}
