package com.pass.hbl.manager.backend.persistence.entity.hm;


import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.*;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.UUID;

@Table(name = "user_purchase_notification", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@ToString
@SQLDelete(sql = "UPDATE hm.user_purchase_notification SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmUserPurchaseNotification extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", referencedColumnName = "id", updatable = false)
    private HmUserProfile user;

    @NotNull
    @Column(name = "token")
    private String token;

    @NotNull
    @Column(name = "notification_type")
    private int notificationType;

    @Column(name = "expiry_date")
    private LocalDateTime expiryDate;

    public HmUserPurchaseNotification(HmUserProfile user, String token, int notificationType, LocalDateTime expiryDate) {
        this.user = user;
        this.token = token;
        this.notificationType = notificationType;
        this.expiryDate = expiryDate;
    }
}
