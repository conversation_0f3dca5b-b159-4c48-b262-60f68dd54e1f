package com.pass.hbl.manager.backend.persistence.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.internal.util.Objects;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.*;

@Configuration
@Slf4j
public class ProxyConfig {


    private static class ProxyHolder {

        private static volatile ProxyHolder instance;

        @Getter
        private final Proxy proxy;

        public static ProxyHolder getInstance(AbstractHandballManagerConfigurationProperties properties) throws MalformedURLException {
            if (instance == null) {
                synchronized (ProxyHolder.class) {
                    if (instance == null) {
                        instance = new ProxyHolder(properties);
                    }
                }
            }
            return instance;
        }

        private ProxyHolder(AbstractHandballManagerConfigurationProperties properties) throws MalformedURLException {
            String proxyString = Objects.firstNonNull(
                    properties.getHttpProxy().isEnabled() ? properties.getHttpProxy().getUrl() : null,
                    System.getenv("https_proxy"),
                    System.getenv("HTTPS_PROXY"),
                    System.getenv("http_proxy"),
                    System.getenv("HTTP_PROXY"),
                    StringUtils.EMPTY);
            log.info("setting system proxy=" + proxyString);

            if (StringUtils.isEmpty(proxyString)) {
                proxy = Proxy.NO_PROXY;
                return;
            }

            URL proxyUrl = new URL(proxyString);
            System.setProperty("http.proxyHost", proxyUrl.getHost());
            System.setProperty("http.proxyPort", String.valueOf(proxyUrl.getPort()));
            System.setProperty("https.proxyHost", proxyUrl.getHost());
            System.setProperty("https.proxyPort", String.valueOf(proxyUrl.getPort()));
            proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyUrl.getHost(), proxyUrl.getPort()));
        }
    }

    @Bean
    @Qualifier("hbl-proxy")
    public Proxy getProxy(AbstractHandballManagerConfigurationProperties properties) throws MalformedURLException {
        return ProxyHolder.getInstance(properties).getProxy();
    }
}
