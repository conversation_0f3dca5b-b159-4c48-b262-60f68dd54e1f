package com.pass.hbl.manager.backend.persistence.mapper.datacore.transfer;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.dto.hm.Position;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminExternalDataMapping;
import com.pass.hbl.manager.backend.persistence.entity.datacore.DcPlayer;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmClub;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerInClub;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.transfer.converter.DcPlayerIdConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.CountryConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.GenderConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.PositionConverter;
import com.pass.hbl.manager.backend.persistence.service.admin.ExternalDataMappingService;
import com.pass.hbl.manager.backend.persistence.service.hm.ClubService;
import com.pass.hbl.manager.backend.persistence.service.hm.PlayerService;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;


@Component
public class Dc2HmPlayerMapper extends AbstractDc2HmMapper<DcPlayer, HmPlayer> {

    private final ExternalDataMappingService externalDataMappingService;
    private final GenderConverter genderConverter;
    private final CountryConverter countryConverter;
    private final ClubService clubService;
    private final PlayerService playerService;
    private final PositionConverter positionConverter;


    public Dc2HmPlayerMapper(DcPlayerIdConverter idConverter,
                             ExternalDataMappingService externalDataMappingService,
                             GenderConverter genderConverter,
                             CountryConverter countryConverter,
                             ClubService clubService,
                             PlayerService playerService, PositionConverter positionConverter) {
        super(DcPlayer.class, HmPlayer.class, null, idConverter);
        this.externalDataMappingService = externalDataMappingService;
        this.genderConverter = genderConverter;
        this.countryConverter = countryConverter;
        this.clubService = clubService;
        this.playerService = playerService;
        this.positionConverter = positionConverter;
    }

    @Override
    protected void customizeInit() {
        TypeMap<DcPlayer, HmPlayer> typeMap = getOrCreateTypeMap(DcPlayer.class, HmPlayer.class);
        // do not map position always, only for new players. Mapping done in #customizeMapToDto
        //typeMap.addMappings(mapper -> mapper.skip(HmPlayer::setPosition));
        // comment out the previous line (skip mapping) and uncomment this line (add position converter) if you need to update the player position
        typeMap.addMappings(mapper -> mapper.using(positionConverter).map(DcPlayer::getPosition, HmPlayer::setPosition));
        typeMap.addMappings(mapper -> mapper.using(countryConverter).map(DcPlayer::getNationality, HmPlayer::setNationality));
        getModelMapper().addConverter(genderConverter);
    }

    /**
     * Handle assignment player to club in this custom method instead of general mapper initialization as we need here
     * more complex access to the managed HmPlayer entity which is not possible otherwise.
     *
     * @param hmPlayer the hm entity
     * @param dcPlayer the sportradar entity
     * @return the hm entity
     */

    @Override
    protected HmPlayer customizeMapToDto(HmPlayer hmPlayer, DcPlayer dcPlayer) {

        if (hmPlayer == null) {
            return null;
        }

        AtomicReference<List<HmPlayerInClub>> allClubs = new AtomicReference<>();

        if (hmPlayer.getId() == null) {
            // new player
            allClubs.set(new ArrayList<>());
            // set position only for new players
            hmPlayer.setPosition(Position.valueOf(dcPlayer.getPosition()));
        } else {
            // updated player
            playerService.getOptionalById(hmPlayer.getId()).ifPresentOrElse(p -> allClubs.set(p.getAllClubs()), () -> allClubs.set(new ArrayList<>()));
        }
        hmPlayer.setActive(true);
        allClubs.get().sort(Comparator.comparing(HmPlayerInClub::getJoined));

        HmClub club = dcPlayer.getCompetitor() != null ?
                externalDataMappingService
                        .get(Datasource.DATACORE, ExternalEntity.COMPETITOR, dcPlayer.getCompetitor().getId())
                        .map(AdminExternalDataMapping::getHmId)
                        .map(clubService::getOptionalById)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .orElse(null)
                : null;

        Optional<HmPlayerInClub> currentClub = allClubs.get().stream().filter(c -> Objects.isNull(c.getLeft())).findFirst();

        // if no change, then nothing to do and return
        if (currentClub.isPresent() && club != null && Objects.equals(currentClub.get().getClub(), club)) {
            return hmPlayer;
        }

        // new club (no matter if leaving old club or starting new without current club
        LocalDateTime joiningTime = LocalDateTime.now();
        currentClub.ifPresent(hmPlayerInClub -> hmPlayerInClub.setLeft(joiningTime.minusSeconds(1)));
        allClubs.get().add(new HmPlayerInClub(hmPlayer, club, joiningTime));
        hmPlayer.setAllClubs(allClubs.get());
        return hmPlayer;
    }
}
