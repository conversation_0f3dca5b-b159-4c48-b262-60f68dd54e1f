package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Arrays;
import java.util.Objects;

@Schema(implementation = LeagueInfo.class)
public enum LeagueInfo {
    PREVIOUS_LEAGUE_ID,
    PREVIOUS_LEAGUE_WINNER_ID;

    @JsonCreator
    public static LeagueInfo getFromString(String s) {
        return Arrays.stream(values()).filter(sa -> Objects.equals(sa.toString(), s)).findFirst().orElse(null);
    }
}
