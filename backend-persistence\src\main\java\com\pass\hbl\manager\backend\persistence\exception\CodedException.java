package com.pass.hbl.manager.backend.persistence.exception;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class CodedException extends Exception {

    @Getter
    private final ExceptionCode code;

    @SuppressWarnings("FieldMayBeFinal")
    @Getter
    private List<String> args;

    public CodedException(ExceptionCode code) {
        this(code, null);
    }

    public CodedException(ExceptionCode code, String message) {
        this(code, message, (String[]) null);
    }

    public CodedException(ExceptionCode code, String message, String... args) {
        this(code, message, null, args);
    }

    public CodedException(ExceptionCode code, String message, Throwable cause, String... args) {
        super("[" + code.getCode() + "] " + message, cause);
        this.code = code;
        this.args = new ArrayList<>();
        addArgs(args);
    }

    protected void addArgs(String... args) {
        if (args != null) {
            this.args.addAll(Arrays.stream(args).toList());
        }
    }
}
