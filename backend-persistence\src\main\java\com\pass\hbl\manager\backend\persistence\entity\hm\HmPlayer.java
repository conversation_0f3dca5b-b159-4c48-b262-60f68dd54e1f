package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.Gender;
import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerStatus;
import com.pass.hbl.manager.backend.persistence.dto.hm.Position;
import com.pass.hbl.manager.backend.persistence.entity.AbstractPictureEntity;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static java.util.Objects.nonNull;

@Table(name = "player", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@Entity
@ToString
@SQLDelete(sql = "UPDATE hm.player SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmPlayer extends AbstractPictureEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @Column(name = "hbl_image_id")
    private String hblImageId;

    //@NotBlank
    @Column(name = "first_name")
    private String firstName;

    @NotBlank
    @Column(name = "last_name")
    private String lastName;

    /**
     * {@link java.util.Locale.IsoCountryCode#PART1_ALPHA2}
     */
    @ToString.Exclude
    @Size(min = 2, max = 2)
    @Column(name = "nationality")
    private String nationality;

    @ToString.Exclude
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private PlayerStatus status;

    @ToString.Exclude
    @Column(name = "height")
    private Integer height;

    @ToString.Exclude
    @Column(name = "weight")
    private Integer weight;

    @ToString.Exclude
    @Enumerated(EnumType.STRING)
    @Column(name = "gender")
    private Gender gender;

    @Enumerated(EnumType.STRING)
    @Column(name = "position")
    private Position position;

    @ToString.Exclude
    @Size(max = 32)
    @Column(name = "nickname")
    private String nickname;

    @ToString.Exclude
    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;

    @ToString.Exclude
    @Column(name = "jersey_number")
    private Integer jerseyNumber;

    @ToString.Exclude
    @Column(name = "active")
    private Boolean active;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "player")
    @Where(clause = "joined < now() and (`left` is null or `left` > now())")
    private List<HmPlayerInClub> currentClub;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "player", cascade = {CascadeType.REFRESH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.DETACH})
    private List<HmPlayerInClub> allClubs;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "player")
    private List<HmTransferMarket> transferMarketAppearances;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "player")
    private List<HmPlayerRoundStatistics> statistics;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "player_id", referencedColumnName = "id")
    @Where(clause = "current_value = true")
    private List<HmPlayerMarketValue> currentMarketValue;

    public HmClub getClub() {
        return Util.toStream(getCurrentClub()).findFirst().map(HmPlayerInClub::getClub).orElse(null);
    }

    public Integer getMarketValue() {
        List<HmPlayerMarketValue> currentMarketValue = getCurrentMarketValue();
        if (nonNull(currentMarketValue)) {
            Optional<HmPlayerMarketValue> marketValueOptional = Util.toStream(currentMarketValue)
                    .filter(HmPlayerMarketValue::isCurrentValue)
                    .max(Comparator.comparing(HmPlayerMarketValue::getCreatedAt));
            return marketValueOptional.map(HmPlayerMarketValue::getMarketValue).orElse(null);
        }
        return null;
    }
}
