package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * Simple league data transfer object with minimal information (id and name)
 * Used for lists of leagues in user statistics
 */
@JsonRootName("SimpleLeague")
@Getter
@Setter
@Schema(description = "Simple league object with minimal information")
public class LeagueNameDto {

    @Schema(description = "League id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "League name", example = "Elite Handball League", required = true)
    @NotBlank
    @Size(max = 256)
    private String name;

    @JsonCreator
    public LeagueNameDto() {
    }

    public LeagueNameDto(String id, String name) {
        this.id = id;
        this.name = name;
    }
}
