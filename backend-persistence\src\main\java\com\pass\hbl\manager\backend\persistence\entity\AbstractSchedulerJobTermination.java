package com.pass.hbl.manager.backend.persistence.entity;

import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobMode;
import lombok.*;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Table(name = "scheduler_job_termination", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@ToString
public class AbstractSchedulerJobTermination {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @Column(name = "job_id")
    private UUID jobId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "mode")
    private SchedulerJobMode mode;
    @NotNull
    @Column(name = "active_profile")
    private String activeProfile;

    @Column(name = "one_time_job")
    private boolean oneTimeJob;

    @Column(name = "terminate_immediate")
    private boolean terminateImmediate;

    public AbstractSchedulerJobTermination(UUID jobId, SchedulerJobMode mode, String activeProfile, boolean oneTimeJob, boolean terminateImmediate) {
        this.jobId = jobId;
        this.mode = mode;
        this.activeProfile = activeProfile;
        this.oneTimeJob = oneTimeJob;
        this.terminateImmediate = terminateImmediate;
    }

    @Override
    @SuppressWarnings({"EqualsWhichDoesntCheckParameterClass", "EqualsDoesntCheckParameterClass", "com.haulmont.jpb.EqualsDoesntCheckParameterClass"})
    public boolean equals(Object o) {
        return EqualsBuilder.reflectionEquals(this, o);
    }

    @Override
    public int hashCode() {
        return HashCodeBuilder.reflectionHashCode(this);
    }
}
