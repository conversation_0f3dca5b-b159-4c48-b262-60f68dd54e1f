package com.pass.hbl.manager.backend.persistence.entity.admin;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

@Table(name = "external_data_mapping", schema = "admin", catalog = "handball_manager")
@Getter
@Setter
@ToString
@Entity
@SQLDelete(sql = "UPDATE admin.external_data_mapping SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class AdminExternalDataMapping extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "datasource")
    private Datasource datasource;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "entity")
    private ExternalEntity entity;

    @NotNull
    @Column(name = "source_id")
    private String sourceId;

    @NotNull
    @Column(name = "hm_id")
    private UUID hmId;

    @Transient
    private transient AtomicReference<AbstractEntity> hmEntity;

    public AdminExternalDataMapping() {
    }

    public AdminExternalDataMapping(Datasource datasource, ExternalEntity entity, String sourceId, AbstractEntity hmEntity) {
        this.datasource = datasource;
        this.entity = entity;
        this.sourceId = sourceId;
        if (hmEntity.getId() != null) {
            this.hmId = hmEntity.getId();
        }
        this.hmEntity = new AtomicReference<>(hmEntity);
    }

    @Override
    protected void onPersist() {
        super.onPersist();
        if (hmId != null || hmEntity == null || hmEntity.get() == null || hmEntity.get().getId() == null) {
            return;
        }
        hmId = hmEntity.get().getId();
    }

    public boolean isContainedIn(Iterable<AdminExternalDataMapping> others) {
        if (others == null) {
            return false;
        }
        return Util.toStream(others).anyMatch(this::equalsWithoutId);
    }
    public boolean equalsWithoutId(AdminExternalDataMapping other) {
        if (other == null) {
            return false;
        }
        return Objects.equals(this.datasource, other.datasource) &&
                Objects.equals(this.entity, other.entity) &&
                Objects.equals(this.sourceId, other.sourceId) &&
                Objects.equals(this.hmId, other.hmId);
    }
}
