package com.pass.hbl.manager.backend.persistence.domain.hm;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Domain object for basic user statistics data
 * Contains only the fields needed for user statistics
 */
public interface UserStatisticsBasicDO {
    /**
     * Get the user ID
     * @return User ID
     */
    String getId();
    
    /**
     * Get the user SSO ID
     * @return User SSO ID
     */
    String getSsoId();
    
    /**
     * Get the username
     * @return Username
     */
    String getUsername();
    
    /**
     * Get the registration date
     * @return Registration date
     */
    LocalDateTime getCreatedAt();
    
    /**
     * Get the app language
     * @return App language
     */
    String getAppLanguage();
    
    /**
     * Get the user level
     * @return User level
     */
    Integer getLevel();
    
    /**
     * Get the app version
     * @return App version
     */
    String getAppVersion();
    
    /**
     * Get the premium status
     * @return Premium status
     */
    Boolean getPremium();
    
    /**
     * Get the premium expiration date
     * @return Premium expiration date
     */
    LocalDateTime getPremiumExpiration();
    
    /**
     * Get the deleted status
     * @return Deleted status
     */
    Boolean getDeleted();
}
