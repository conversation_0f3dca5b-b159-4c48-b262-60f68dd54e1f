package com.pass.hbl.manager.backend.persistence.config;

import com.pass.hbl.manager.backend.persistence.service.datacore.masterdata.DataCoreTokenHandler;
import com.pass.hbl.manager.backend.persistence.util.DataCoreEnvironment;
import com.pass.hbl.manager.backend.persistence.util.DataCoreUtils;
import com.pass.hbl.manager.backend.persistence.util.Util;
import com.sportradar.datacore.rest.ApiClient;
import com.sportradar.datacore.rest.api.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.internal.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import javax.annotation.PostConstruct;
import java.net.InetSocketAddress;
import java.net.MalformedURLException;
import java.net.ProxySelector;
import java.net.URL;
import java.util.Arrays;
import java.util.List;

import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static com.pass.hbl.manager.backend.persistence.util.Constants.PROD5_PROFILE;

@Slf4j
@Configuration
public class DataCoreApiConfig {

    @Autowired
    Environment environment;

    @Autowired
    DataCoreTokenHandler dataCoreTokenHandler;

    @PostConstruct
    public void init() {
        // Initialize DataCore Rest-Api access token
        dataCoreTokenHandler.init();
        // Initialize DataCore Streaming-Api access data (broker-url, client-ID, topics)
        // Currently moved to ApplicationReadyEvent so that it doesn't affect the start of the service
        //streamingAccessHandler.initializeMqttSubscriptions();//init();
    }

    @Bean
    public CompetitionsApi getDataCoreCompetitionsApi(AbstractHandballManagerConfigurationProperties properties) {
        return new CompetitionsApi(getApiClient(properties));
    }

    @Bean
    public SeasonsApi getDataCoreSeasonsApi(AbstractHandballManagerConfigurationProperties properties) {
        return new SeasonsApi(getApiClient(properties));
    }

    @Bean
    public SeasonTeamsApi getDataCoreCompetitorsApi(AbstractHandballManagerConfigurationProperties properties) {
        return new SeasonTeamsApi(getApiClient(properties));
    }

    @Bean
    public MatchesApi getDataCoreMatchesApi(AbstractHandballManagerConfigurationProperties properties) {
        return new MatchesApi(getApiClient(properties));
    }

    @Bean
    public TeamsApi getDataCoreTeamsApi(AbstractHandballManagerConfigurationProperties properties) {
        return new TeamsApi(getApiClient(properties));
    }

    @Bean
    public MatchTeamsApi getDataCoreMatchTeamsApi(AbstractHandballManagerConfigurationProperties properties) {
        return new MatchTeamsApi(getApiClient(properties));
    }

    @Bean
    public SeasonRosterApi getDataCoreSeasonRosterApi(AbstractHandballManagerConfigurationProperties properties) {
        return new SeasonRosterApi(getApiClient(properties));
    }

    @Bean
    public MatchLiveSummaryApi getMatchLiveSummaryApi(AbstractHandballManagerConfigurationProperties properties) {
        return new MatchLiveSummaryApi(getApiClient(properties));
    }

    @Bean
    public PersonMatchStatisticsApi getPersonMatchStatisticsApi(AbstractHandballManagerConfigurationProperties properties) {
        return new PersonMatchStatisticsApi(getApiClient(properties));
    }

    @Bean
    public MatchPlayByPlayApi getMatchPlayByPlayApi(AbstractHandballManagerConfigurationProperties properties) {
        return new MatchPlayByPlayApi(getApiClient(properties));
    }

    private ApiClient getApiClient(AbstractHandballManagerConfigurationProperties properties) {

        String proxyString = Objects.firstNonNull(
                properties.getHttpProxy().isEnabled() ? properties.getHttpProxy().getUrl() : null,
                System.getenv("https_proxy"),
                System.getenv("HTTPS_PROXY"),
                System.getenv("http_proxy"),
                System.getenv("HTTP_PROXY"),
                StringUtils.EMPTY);

        log.info("using proxy=" + proxyString + " for accessing sportradar "
                + properties.getImporter().getSportradarStage()
                + " with apiKey=" + properties.getImporter().getApiKey());

        ProxySelector proxy;
        if (!StringUtils.equalsIgnoreCase(StringUtils.EMPTY, proxyString)) {
            try {
                URL url = new URL(proxyString);
                proxy = ProxySelector.of(new InetSocketAddress(url.getHost(), url.getPort()));
            } catch (MalformedURLException e) {
                throw new RuntimeException("Malformed proxy URL: " + proxyString);
            }
        } else {
            proxy = ProxySelector.getDefault();
        }

        ApiClient apiClient = com.sportradar.datacore.rest.Configuration.getDefaultApiClient();
        List<String> activeProfiles = Arrays.asList(environment.getActiveProfiles());
        String accessToken = dataCoreTokenHandler.getToken();
        DataCoreEnvironment dataCoreEnvironment= DataCoreUtils.getdataCoreEnvironment(activeProfiles);

        if (dataCoreEnvironment.equals(DataCoreEnvironment.DATACORE_PROD))
        {
            String prodBaseUrl = properties.getDataCore().getProdBaseUrl();
            apiClient.setAccessToken(accessToken);
            apiClient.setBasePath(prodBaseUrl);
        // User the non-prod URL for Local & DEV environment
        } else {
            String stagingBaseUrl = properties.getDataCore().getStagingBaseUrl();
            apiClient.setAccessToken(accessToken);
            apiClient.setBasePath(stagingBaseUrl);
        }

        // set proxy
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        Util.toStream(apiClient.getHttpClient().networkInterceptors()).forEach(builder::addNetworkInterceptor);
        OkHttpClient okHttpClient = builder.proxySelector(proxy).build();
        apiClient.setHttpClient(okHttpClient);
        return apiClient;
    }
}
