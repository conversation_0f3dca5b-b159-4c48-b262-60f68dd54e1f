package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "TeamPlayer")
@NoArgsConstructor
@Data
public class TeamPlayerXml {

    @JacksonXmlProperty(localName = "id", isAttribute = true)
    private Long id;

    @JacksonXmlProperty(localName = "pos", isAttribute = true)
    private String pos;

    @JacksonXmlProperty(localName = "Player")
    private PlayerXml player;

    @JacksonXmlProperty(localName = "ShirtNumber")
    private ShirtNumberXml shirtNumber;

    @JacksonXmlProperty(localName = "PlayerTeam")
    private PlayerTeamXml playerTeam;

    @JacksonXmlProperty(localName = "Substitute")
    private SubstituteXml substitute;

    @JacksonXmlProperty(localName = "Nationality")
    private NationalityXml nationality;

    @JacksonXmlProperty(localName = "Age")
    private AgeXml age;

    @JacksonXmlProperty(localName = "PlayerStatistics")
    private PlayerStatisticsXml playerStatistics;
}
