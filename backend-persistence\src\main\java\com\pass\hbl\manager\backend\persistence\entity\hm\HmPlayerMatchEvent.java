package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.Event;
import com.pass.hbl.manager.backend.persistence.dto.hm.EventCharacteristic;
import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Table(name = "player_match_event", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@ToString
@Slf4j
@Entity
@SQLDelete(sql = "UPDATE hm.player_match_event SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmPlayerMatchEvent extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "match_id", referencedColumnName = "id", updatable = false)
    private HmMatch match;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "player_id", referencedColumnName = "id", updatable = false)
    private HmPlayer player;

    //@NotEmpty
    @Column(name = "match_time")
    private String matchTime;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "event")
    private Event event;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_characteristic")
    private EventCharacteristic eventCharacteristic;

    @Column(name = "score")
    private int score;
}
