package com.pass.hbl.manager.backend.persistence.exception;

import lombok.Getter;

@Getter
public class ForbiddenOperationException extends CodedException {

    private final String operation;

    private final String user;

    private final String reason;

    public ForbiddenOperationException(String operation, String user, String reason) {
        super(ExceptionCode.FORBIDDEN_OPERATION, "Operation '" + operation + "' not allowed for user '" + user + "'. Reason: " + reason, operation, user, reason);
        this.operation = operation;
        this.user = user;
        this.reason = reason;
    }
}
