package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrCompetition;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter.CategoryConverter;
import com.sportradar.handball.v2.model.CompetitionWithCategory;
import org.springframework.stereotype.Component;

@Component
public class SrCompetitionMapper extends AbstractMapper<SrCompetition, CompetitionWithCategory> {

    private final CategoryConverter categoryConverter;

    public SrCompetitionMapper(CategoryConverter categoryConverter) {
        super(SrCompetition.class, CompetitionWithCategory.class);
        this.categoryConverter = categoryConverter;
    }

    @Override
    protected void customizeInit() {
        getModelMapper().addConverter(categoryConverter);
    }
}
