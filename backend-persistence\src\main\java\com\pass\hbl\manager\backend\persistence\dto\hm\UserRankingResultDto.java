package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.time.ZonedDateTime;
import java.util.List;

@JsonRootName("UserRankingResult")
@EqualsAndHashCode
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "User ranking object used for streaming manager ranking by league and round")
public class UserRankingResultDto {

    @Schema(description = "true, if currently a match day is running", example = "sundays in a regular round", required = true)
    @JsonProperty(value = "isMatchDayRunning")
    boolean isMatchDayRunning;

    @Schema(description = "nearest game start related to a previous or next game")
    private ZonedDateTime nearestGameStart;

    @Schema(description = "List of overall user user ranking items", required = true)
    @NotNull
    private List<UserLiveRankingDto> userRankings;
}
