package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@JsonRootName("RoundStatistics")
@Getter
@Setter
@Schema(description = "Reduced round statistics object")
public class PlayerRoundStatisticsDto extends AbstractDto<PlayerRoundStatisticsDto, String> {

    @NotNull
    @Schema(description = "id of the statistics object", required = true)
    private String id;

    @NotNull
    @Schema(description = "id of the player", required = true)
    private String playerId;

    @NotNull
    @Schema(description = "id of the round", required = true)
    private String roundId;

    @Schema(description = "optional id of the club the points were gained against")
    private String challengerId;

    @Schema(description = "optional id of the match the points were gained in")
    private String matchId;

    @NotNull
    @Schema(description = "total score gained overall up to the given round", required = true)
    private int totalScore;

    @NotNull
    @Schema(description = "average score gained overall up to the given round", required = true)
    private float averageScore;

    @NotNull
    @Min(0)
    @Schema(description = "games played overall up to the given round", required = true)
    private int gamesPlayed;

    @NotNull
    @Min(0)
    @Schema(description = "number of assist overall up to the given round", required = true)
    private int assistsNumber;

    @NotNull
    @Min(0)
    @Schema(description = "seconds played overall up to the given round", required = true)
    private int secondsPlayed;

    @NotNull
    @Min(0)
    @Schema(description = "goals thrown overall up to the given round", required = true)
    private int goalsThrown;

    @NotNull
    @Min(0)
    @Schema(description = "throw rate overall up to the given round", required = true)
    private float throwRate;

    @NotNull
    @Min(0)
    @Schema(description = "goals conceded overall up to the given round", required = true)
    private int goalsConceded;

    @Min(0)
    @Schema(description = "goals saved overall up to the given round", required = true)
    private int savedGoals;

    @NotNull
    @Min(0)
    @Schema(description = "number of throws overall up to the given round", required = true)
    private int throwsNumber;
}
