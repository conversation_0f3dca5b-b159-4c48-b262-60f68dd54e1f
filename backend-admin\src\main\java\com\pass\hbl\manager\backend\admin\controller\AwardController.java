package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.dto.hm.AwardDto;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.service.hm.AwardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.core.converters.models.PageableAsQueryParam;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.security.RolesAllowed;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping(value = ApiConstants.AWARD_API)
@Validated
@Tag(name = "award", description = "API for admin award management")
@RolesAllowed({ApiConstants.ROLE_ADMIN})
public class AwardController extends AbstractController {

    private final AwardService service;

    protected AwardController(ApplicationEventPublisher eventPublisher,
                              AwardService service) {
        super(eventPublisher);
        this.service = service;
    }


    @Operation(summary = "Create an award")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior",
                    content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = AwardDto.class))}),
            @ApiResponse(responseCode = "400", description = "Award already exists",
                    content = {@Content}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support",
                    content = {@Content})
    })
    @PostMapping(value = "/create", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public AwardDto createAward(@Parameter(name = "awardDto", description = "request parameter for award creation", required = true, schema = @Schema(implementation = AwardDto.class))
                                @RequestPart @Valid AwardDto awardDto,
                                @Parameter(name = "file", description = "optional award icon")
                                @RequestPart MultipartFile file) throws IOException {

        return service.createAward(awardDto, file);
    }


    @Operation(summary = "Get all awards")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Found awards", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = AwardDto.class))}),
            @ApiResponse(responseCode = "404", description = "No awards found", content = {@Content})
    })
    @GetMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    @PageableAsQueryParam
    public List<AwardDto> getAwards(@Parameter(hidden = true) Pageable pageable,
                                    UriComponentsBuilder uriBuilder,
                                    HttpServletRequest request,
                                    HttpServletResponse response, @RequestHeader(value = "accept-language", required = false) String language) throws FormatException {
        return getListFromPageableResult(this, uriBuilder, request, response, service.getAllAsDto(pageable, language));
    }


    @Operation(summary = "Get an award by the given id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Found award", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = AwardDto.class))}),
            @ApiResponse(responseCode = "404", description = "No award found", content = {@Content})
    })
    @GetMapping(value = "/{awardId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public AwardDto getById(@Parameter(name = "awardId", description = "id of the award", required = true, schema = @Schema(implementation = String.class))
                            @PathVariable() @NotBlank String awardId, @RequestHeader(value = "accept-language", required = false) String language) throws FormatException, EntityNotExistException {
        return service.getByIdAsDto(awardId, language);
    }


    @Operation(summary = "Modify an existing Award")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Normal behavior", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = AwardDto.class))}),
            @ApiResponse(responseCode = "400", description = "Award already exists", content = {@Content}),
            @ApiResponse(responseCode = "404", description = "Error case, contact support", content = {@Content})
    })
    @PutMapping(value = "/update", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public AwardDto updateAward(@Parameter(name = "award", description = "award to be updated", schema = @Schema(implementation = AwardDto.class))
                                @RequestPart @NotNull AwardDto awardDto,
                                @Parameter(name = "file", description = "optional award icon")
                                @RequestPart MultipartFile file) throws FormatException, EntityNotExistException, IOException {
        return service.update(awardDto, file);
    }


    @Operation(summary = "Delete an award")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Success", content = {@Content}),
            @ApiResponse(responseCode = "403", description = "Forbidden", content = {@Content}),
            @ApiResponse(responseCode = "404", description = "Exception case", content = {@Content})
    })
    @DeleteMapping(value = "/{awardId}")
    public void deleteAward(@Parameter(name = "awardId", description = "Id of the current award", required = true)
                            @PathVariable
                            @NotBlank String awardId) throws FormatException {
        service.deleteAward(awardId);
    }
}
