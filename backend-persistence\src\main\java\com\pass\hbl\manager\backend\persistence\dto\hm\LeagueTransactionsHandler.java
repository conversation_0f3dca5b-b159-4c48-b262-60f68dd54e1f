package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmTransferMarketTransactionDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmUserAwardCreatedDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmUserRoundScoreTransactionDO;
import com.pass.hbl.manager.backend.persistence.dto.shared.EntityType;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmAward;
import com.pass.hbl.manager.backend.persistence.entity.shared.SharedLocalization;
import com.pass.hbl.manager.backend.persistence.repository.shared.SharedLocalizationRepository;
import com.pass.hbl.manager.backend.persistence.service.hm.AwardService;
import com.pass.hbl.manager.backend.persistence.service.hm.TransferMarketService;
import com.pass.hbl.manager.backend.persistence.service.hm.UserProfileService;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.LeagueScoreHandler;
import com.pass.hbl.manager.backend.persistence.util.Constants;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

import static com.pass.hbl.manager.backend.persistence.dto.hm.BalanceTransactionType.*;
import static com.pass.hbl.manager.backend.persistence.entity.AbstractEntity.ZERO_DATE;
import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
@Transactional
@Service
public class LeagueTransactionsHandler {

    private final TransferMarketService transferMarketService;
    private final AwardService awardService;
    private final UserProfileService userProfileService;

    private final LeagueScoreHandler leagueScoreHandler;
    private final SharedLocalizationRepository sharedLocalizationRepository;

    private final ConcurrentHashMap<Locale, HashMap<String, String>> translationCache = new ConcurrentHashMap<>();

    public LeagueTransactionsHandler(@Lazy TransferMarketService transferMarketService, @Lazy AwardService awardService,
                                     @Lazy UserProfileService userProfileService, @Lazy LeagueScoreHandler leagueScoreHandler, @Lazy SharedLocalizationRepository sharedLocalizationRepository) {
        this.transferMarketService = transferMarketService;
        this.awardService = awardService;
        this.userProfileService = userProfileService;
        this.leagueScoreHandler = leagueScoreHandler;
        this.sharedLocalizationRepository = sharedLocalizationRepository;
    }


    public List<UserLeagueTransactionDto> getUserTransactionHistory(String userId, String leagueId) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime sinceDate = LocalDateTime.of(now.toLocalDate().minusDays(30), LocalTime.MIN);
        Locale locale;
        try {
            locale = Util.getLocaleByLanguageTagOrDefault(userProfileService.getUserAppLanguage(UUID.fromString(userId)));
        } catch (Exception e) {
            locale = Constants.DEFAULT_LOCALE;
        }

        // 1- get the transactions related to the points added or subtracted by the manager score in round. This score is
        // calculated in the RoundResultJob or RoundResultUpdateJob
        List<UserLeagueTransactionDto> userRoundScoreTransactions = getUserRoundScoreTransactions(userId, leagueId, sinceDate, locale);
        List<UserLeagueTransactionDto> transactionDtos = new ArrayList<>(userRoundScoreTransactions);

        // 2- get the transactions related to money added by user awards
        List<UserLeagueTransactionDto> userAwardsTransactions = getUserAwardsTransactions(userId, leagueId, sinceDate, locale);
        transactionDtos.addAll(userAwardsTransactions);

        // 3- get transfer market transactions purchases & sales
        List<UserLeagueTransactionDto> transferMarketPurchases = getTransferMarketTransactions(userId, leagueId, sinceDate, locale);
        transactionDtos.addAll(transferMarketPurchases);

        // Sort the transaction by date descending
        transactionDtos.sort(Comparator.comparing(UserLeagueTransactionDto::getTransactionDate).reversed());
        return transactionDtos;
    }

    private List<UserLeagueTransactionDto> getUserRoundScoreTransactions(String id, String leagueId, LocalDateTime sinceDate, Locale locale)  {
        UUID userId = UUID.fromString(id);
        List<HmUserRoundScoreTransactionDO> userRoundScores = leagueScoreHandler.getUserRoundScoresSinceDate(userId, UUID.fromString(leagueId), sinceDate);
        return userRoundScores.stream().map(urs -> {
            String description = getDescriptionByLocale(locale, ROUND_SCORE_MONEY, urs.getRoundNumber());
            int money = isNull(urs.getScore()) ? 0 : urs.getScore() * SCORE_MONEY_FACTOR;
            boolean isCredit = urs.getScore() > 0;
            ZonedDateTime transactionDate;
            LocalDateTime modifiedAt = urs.getModifiedAt();
            if (Objects.equals(modifiedAt, ZERO_DATE)) {
                modifiedAt = null;
            }
            if (nonNull(modifiedAt)) {
                // modified_at represents the date in which the user round score was corrected by RoundResultUpdateJob
                transactionDate = ZonedDateTime.of(modifiedAt, TimeZone.getDefault().toZoneId());
            } else if (nonNull(urs.getCreatedAt())) {
                // if modified_at is not set, use created_at as date in which the user round score was created by RoundResultJob
                transactionDate = ZonedDateTime.of(urs.getCreatedAt(), TimeZone.getDefault().toZoneId());
            } else {
                // double-check transaction date is unknown
                return null;
            }
            return new UserLeagueTransactionDto(money, description, isCredit, transactionDate, ROUND_SCORE_MONEY, null);
        }).filter(Objects::nonNull).toList();
    }

    private List<UserLeagueTransactionDto> getUserAwardsTransactions(String id, String leagueId, LocalDateTime sinceDate, Locale locale) {
        UUID userId = UUID.fromString(id);
        List<Pair<HmUserAwardCreatedDO, HmAward>> userAwardsMoneyPairs = awardService.getUserAwardsSinceDate(userId, UUID.fromString(leagueId), sinceDate);
        return userAwardsMoneyPairs.stream().map(pair -> {
            HmAward award = pair.getValue();
            Optional<SharedLocalization> localizationOptional = award.getDescriptions().stream().filter(sharedLocalization -> sharedLocalization.getLocale().equals(locale) && sharedLocalization.getKey().equals(AWARD_KEY_NAME)).findFirst();
            // get the award code language specific i.e. "Spieltag sieger"
            AtomicReference<String> awardCode = new AtomicReference<>("");
            localizationOptional.ifPresent(localization -> awardCode.set(localization.getValue()));

            String description = getDescriptionByLocale(locale, AWARD_CREDIT, awardCode.get());
            int money = isNull(award.getMoney()) ? 0 : award.getMoney();
            boolean isCredit = money > 0;
            ZonedDateTime transactionDate = ZonedDateTime.of(pair.getKey().getCreatedAt(), TimeZone.getDefault().toZoneId());
            String picture = isNull(award.getPicture())? null :award.getPicture().toString();
            return new UserLeagueTransactionDto(money, description, isCredit, transactionDate, AWARD_CREDIT, picture);

        }).toList();
    }

    private List<UserLeagueTransactionDto> getTransferMarketTransactions(String id, String leagueId, LocalDateTime sinceDate, Locale locale) {
        UUID userId = UUID.fromString(id);

        // player purchases by user and league
        List<HmTransferMarketTransactionDO> playerPurchases = transferMarketService.getPlayerPurchasesSinceDate(userId, UUID.fromString(leagueId), sinceDate);
        List<UserLeagueTransactionDto> purchasesTransactionDtos = playerPurchases.stream().map(DO -> {
            String description = getDescriptionByLocale(locale, PLAYER_PURCHASE, DO.getPlayerFirstName() + " " + DO.getPlayerLastName());
            int money = DO.getBid();
            boolean isCredit = false;
            ZonedDateTime transactionDate = ZonedDateTime.of(DO.getDeletedAt(), TimeZone.getDefault().toZoneId());
            String picture = isNull(DO.getPicture())? null: DO.getPicture();
            return new UserLeagueTransactionDto(money, description, isCredit, transactionDate, PLAYER_PURCHASE, picture);
        }).toList();
        List<UserLeagueTransactionDto> transferMarketTransactions = new ArrayList<>(purchasesTransactionDtos);

        // player sales by user and league
        List<HmTransferMarketTransactionDO> playerSales = transferMarketService.getPlayerSalesSinceDate(userId, UUID.fromString(leagueId), sinceDate);
        List<UserLeagueTransactionDto> salesTransactionDtos = playerSales.stream().map(DO -> {
            String description = getDescriptionByLocale(locale, PLAYER_SALE, DO.getPlayerFirstName() + " " + DO.getPlayerLastName());
            int money = DO.getBid();
            boolean isCredit = true;
            ZonedDateTime transactionDate = ZonedDateTime.of(DO.getDeletedAt(), TimeZone.getDefault().toZoneId());
            String picture = isNull(DO.getPicture())? null: DO.getPicture();
            return new UserLeagueTransactionDto(money, description, isCredit, transactionDate, PLAYER_SALE, picture);
        }).toList();
        transferMarketTransactions.addAll(salesTransactionDtos);
        return transferMarketTransactions;
    }

    private String getDescriptionByLocale(Locale locale, BalanceTransactionType balanceTransactionType, String bodyKeyword) {
        String bodyTemplate;
        String key = balanceTransactionType.name() + BODY_SUFFIX;
        if (translationCache.containsKey(locale) && translationCache.get(locale).containsKey(key)) {
            // if cached use from cache
            bodyTemplate = translationCache.get(locale).get(key);
        } else {
            // else get it from database and cache it
            Optional<SharedLocalization> optional = sharedLocalizationRepository.findFirstByEntityTypeAndKeyAndLocale(EntityType.TRANSACTION, key, locale);
            bodyTemplate = optional.map(SharedLocalization::getValue).orElse(null);
            if (!translationCache.containsKey(locale)) {
                translationCache.put(locale, new HashMap<>());
            }
            translationCache.get(locale).put(key, bodyTemplate);
        }
        String body = "";
        if (nonNull(bodyTemplate)) {
            String[] args = {bodyKeyword};
            body = MessageFormat.format(bodyTemplate, (Object[]) args);

        }
        return body;
    }
}
