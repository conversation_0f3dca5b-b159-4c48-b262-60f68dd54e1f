package com.pass.hbl.manager.backend.persistence.mapper.datacore.live;

import com.pass.hbl.manager.backend.persistence.dto.hm.DcEventType;
import com.pass.hbl.manager.backend.persistence.dto.hm.Event;
import com.pass.hbl.manager.backend.persistence.entity.datacore.DcLiveMatchPlayByPlay;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmDataCoreHpiCalculation;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerMatchEvent;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.live.converter.DcMatchConverter;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.live.converter.DcPlayerConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.AbstractSr2HmMapper;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.IdConverter;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmDataCoreHpiCalculationRepository;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
@Component
public class Sr2HmDataCorePlayerMatchEventMapper extends AbstractSr2HmMapper<DcLiveMatchPlayByPlay, HmPlayerMatchEvent> {

    public static final String EVENT_KEY = "EVENT";
    public static final String GOALKEEPER_KEY = "GOALKEEPER";

    private final DcMatchConverter matchConverter;
    private final DcPlayerConverter playerConverter;

    private final HmDataCoreHpiCalculationRepository hpiCalculationRepository;

    @Getter
    private List<HmDataCoreHpiCalculation> hpiCalculations;

    public Sr2HmDataCorePlayerMatchEventMapper(IdConverter idConverter,
                                               DcMatchConverter matchConverter,
                                               DcPlayerConverter playerConverter,
                                               HmDataCoreHpiCalculationRepository hpiCalculationRepository) {
        super(DcLiveMatchPlayByPlay.class, HmPlayerMatchEvent.class, idConverter);
        this.matchConverter = matchConverter;
        this.playerConverter = playerConverter;
        this.hpiCalculationRepository = hpiCalculationRepository;
    }

    @PostConstruct
    private void init() {
        // cache all HPI values
        // drawback: DB changes in the HPI calculation are currently not taken into account
        //TODO HBLMAN-565 check rules with event_characteristic = 'NULL'
        hpiCalculations = Util.toStream(this.hpiCalculationRepository.findAll()).collect(Collectors.toList());
    }


    public void resetConverterCaches() {
        // Converter caches are cleared if the streaming is not active
        matchConverter.resetCache();
        playerConverter.resetCache();
    }

    @Override
    protected void customizeInit() {
        Converter<DcLiveMatchPlayByPlay, String> toMatchTimeConverter = ctx -> {
            try {
                // make sure the source is not null
                if (ctx.getSource() == null) {
                    return null;
                }
                //make sure the clock is not null
                String clock = ctx.getSource().getClock();
                if (clock == null) {
                    log.info("Clock in DcLiveMatchPlayByPlay is null");
                    return null;
                }
                // Parse ISO 8601 duration
                Duration duration = Duration.parse(clock);

                Integer periodId = ctx.getSource().getPeriodId();
                if(nonNull(periodId)) {
                    if (periodId == 2) {
                        duration = duration.plusMinutes(30);
                    }
                } else {
                    log.info("PeriodId in DcLiveMatchPlayByPlay is null");
                }

                // Extract minutes and seconds
                long totalSeconds = duration.getSeconds();
                long minutes = totalSeconds / 60;
                long seconds = totalSeconds % 60;

                // Format minutes and seconds mm:ss
                return String.format("%02d:%02d", minutes, seconds);
            } catch (Exception e) {
                log.error("failed to convert " + ctx.getSource() + " to match time. Reason: " + e);
                return null;
            }
        };

        TypeMap<DcLiveMatchPlayByPlay, HmPlayerMatchEvent> typeMap = getOrCreateTypeMap(DcLiveMatchPlayByPlay.class, HmPlayerMatchEvent.class);
        typeMap.addMappings(mapper -> mapper.using(matchConverter).map(DcLiveMatchPlayByPlay::getMatchId, HmPlayerMatchEvent::setMatch));
        typeMap.addMappings(mapper -> mapper.using(playerConverter).map(DcLiveMatchPlayByPlay::getPlayerId, HmPlayerMatchEvent::setPlayer));
        typeMap.addMappings(mapper -> mapper.using(toMatchTimeConverter).map(source -> source, HmPlayerMatchEvent::setMatchTime));
    }

    @Override
    protected HmPlayerMatchEvent customizeMapToDto(HmPlayerMatchEvent hm, DcLiveMatchPlayByPlay dc, Map<String, Object> ctx) {
        if (dc == null) {
            return null;
        }
        if (hm.getMatch() == null || hm.getPlayer() == null) {
            log.warn("Unmappable event with unknown match or player. Skipping: " + dc);
            return null;
        }

        // if mapping context is not set map the event as standard event not assist and not goalkeeper
        if (isNull(ctx) || ctx.isEmpty()) {
            ctx = Map.of(GOALKEEPER_KEY, false);
        }
        boolean isFieldPlayerOriginalEvent;
        boolean isAssistEvent;

        if (ctx.containsKey(GOALKEEPER_KEY)) {
            Boolean isGoalkeeper = (Boolean) ctx.get(GOALKEEPER_KEY);
            isFieldPlayerOriginalEvent = isNull(isGoalkeeper) || !isGoalkeeper;
            isAssistEvent = false;
        } else if (ctx.containsKey(EVENT_KEY)) {
            isFieldPlayerOriginalEvent = false;
            isAssistEvent = true;
        } else {
            isFieldPlayerOriginalEvent = true;
            isAssistEvent = false;
        }
        // An event could be an original event or assist event.
        // In case of original event it could be goalkeeper or field player event
        if (!isAssistEvent) {
        //if (isNull(ctx) || ctx.isEmpty()) {
            // first filter by type and attack_type: example type = goal -> attack_type = fast break)
            List<HmDataCoreHpiCalculation> rulesFilteredByTypeThenAttackTypeThenSubtypeThenFailureReason = hpiCalculations.stream()
                    .filter(hpi -> Objects.equals(hpi.getDcEventType(), dc.getEventType()))
                    .filter(hpi -> {
                        if (dc.getEventType().equalsIgnoreCase(DcEventType.SUSPENSION.getValue())) {
                            // type = 43 (time_penalty) extra could be any value
                            return isNull(hpi.getDcAttackType()) || Objects.equals(hpi.getDcAttackType(), dc.getAttackType());
                        } else {
                            return nonNull(dc.getAttackType()) ? Objects.equals(hpi.getDcAttackType(), dc.getAttackType())
                                    : isNull(hpi.getDcAttackType());
                        }
                    }).filter(hpi -> Objects.equals(hpi.getDcSubtype(), dc.getSubtype()))
                    .filter(hpi -> Objects.equals(hpi.getDcFailureReason(), dc.getFailureReason()))
                    .toList();
            HmDataCoreHpiCalculation hpiCalculation;
            // ----------Final selection

            // first find a rule with exactly the same location (example: RIGHT, LEFT, MIDDLE) for field player
            if (isFieldPlayerOriginalEvent) {
                hpiCalculation = rulesFilteredByTypeThenAttackTypeThenSubtypeThenFailureReason.stream()
                        .filter(hpi -> Objects.equals(hpi.getDcLocation(), dc.getLocation()))
                        // additional check to handle the case of multiple calculations
                        // dc_event_type = 'goal' AND dc_subtype = 'backCourt' AND dc_attack_type = 'BREAK_THROUGH'
                        // AND dc_failure_reason IS NULL AND dc_location is NULL
                        .filter(hpi -> isNull(hpi.getGoalkeeper()))
                        .findFirst().orElse(null);
                // or first find a rule with exactly the same location (example: RIGHT, LEFT, MIDDLE) for goalkeeper
            } else {
                hpiCalculation = rulesFilteredByTypeThenAttackTypeThenSubtypeThenFailureReason.stream()
                        .filter(hpi -> Objects.equals(hpi.getDcLocation(), dc.getLocation()))
                        .filter(hpi -> nonNull(hpi.getGoalkeeper()) && hpi.getGoalkeeper())
                        .findFirst().orElse(null);
            }

            // now check if it is a rule for goalkeeper or not

            // if not found try to find a rule with unknown location (location (LEFT/RIGHT/CENTRE = null)
            if (isNull(hpiCalculation)) {
                if (isFieldPlayerOriginalEvent) {
                    // field player
                   hpiCalculation = rulesFilteredByTypeThenAttackTypeThenSubtypeThenFailureReason.stream()
                            .filter(hpi -> isNull(hpi.getDcLocation()))
                           // additional check to handle the case of multiple calculations
                            .filter(hpi -> isNull(hpi.getGoalkeeper()))
                            .findFirst().orElse(null);

                } else {
                    // goalkeeper
                    hpiCalculation = rulesFilteredByTypeThenAttackTypeThenSubtypeThenFailureReason.stream()
                            .filter(hpi -> isNull(hpi.getDcLocation()))
                            .filter(hpi -> nonNull(hpi.getGoalkeeper()) && hpi.getGoalkeeper())
                            .findFirst().orElse(null);
                }
            }

            // if not found try to find a rule with unknown position (subtype)
            if (isNull(hpiCalculation)) {
                hpiCalculation = rulesFilteredByTypeThenAttackTypeThenSubtypeThenFailureReason.stream()
                        .filter(hpi -> isNull(hpi.getDcSubtype()))
                        .findFirst().orElse(null);

            }

            if (hpiCalculation == null) {
                String template = isFieldPlayerOriginalEvent? "field player": "goalkeeper";
                log.warn("DC " + template + " event cannot be translated to HPI. Skipping: " + dc);
                return null;
            }


            hm.setEvent(hpiCalculation.getEvent());
            hm.setEventCharacteristic(hpiCalculation.getEventCharacteristic());
            hm.setScore(hpiCalculation.getScore());

            return hm;
            // In case of Assist the context contains the linked Event to that Assist.
            // Input: Event-type = "assist" -> ctx: output event of the original match event = "BACKCOURT".
            // Output: the event characteristic & score will be determined
        } else {
            Event event = (Event) ctx.get(EVENT_KEY);
            if (nonNull(event)) {
                Optional<HmDataCoreHpiCalculation> ruleFilteredByTypeThenOutputEventOptional = hpiCalculations.stream()
                        .filter(hpi -> Objects.equals(hpi.getDcEventType(), dc.getEventType()))
                        .filter(hpi -> Objects.equals(hpi.getEvent().toString(), event.toString()))
                        .findFirst();
                if (ruleFilteredByTypeThenOutputEventOptional.isPresent()) {
                    HmDataCoreHpiCalculation hpiCalculation = ruleFilteredByTypeThenOutputEventOptional.get();
                    hm.setEvent(hpiCalculation.getEvent());
                    hm.setEventCharacteristic(hpiCalculation.getEventCharacteristic());
                    hm.setScore(hpiCalculation.getScore());
                    return hm;
                } else {
                    log.warn("DC assist event cannot be translated to HPI. Skipping: " + dc);
                    return null;
                }

            } else {
                log.warn("DC assist event cannot be translated to HPI. [Event] property of the linked Player match event is null. Skipping: " + dc);
                return null;
            }
        }
    }
}
