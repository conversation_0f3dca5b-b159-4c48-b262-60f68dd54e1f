package com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter;

import com.pass.hbl.manager.backend.persistence.dto.hm.Gender;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class GenderConverter extends AbstractConverter<String, Gender> {
    @Override
    protected Gender convert(String source) {
        return StringUtils.hasText(source) ? Gender.valueOf(source.toUpperCase()) : null;
    }
}
