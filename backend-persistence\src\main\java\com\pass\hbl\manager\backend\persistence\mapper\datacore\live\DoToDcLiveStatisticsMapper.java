package com.pass.hbl.manager.backend.persistence.mapper.datacore.live;

import com.pass.hbl.manager.backend.persistence.domain.datacore.DcLiveMatchStatisticsDo;
import com.pass.hbl.manager.backend.persistence.entity.datacore.DcLiveMatchPlayerStatistics;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Map;

@Slf4j
@Component
public class DoToDcLiveStatisticsMapper extends AbstractMapper<DcLiveMatchStatisticsDo.PersonStatistics, DcLiveMatchPlayerStatistics> {

    public DoToDcLiveStatisticsMapper() {
        super(DcLiveMatchStatisticsDo.PersonStatistics.class, DcLiveMatchPlayerStatistics.class);
    }

    @Override
    protected DcLiveMatchPlayerStatistics customizeMapToDto(DcLiveMatchPlayerStatistics dcLiveMatchPlayerStatistics, DcLiveMatchStatisticsDo.PersonStatistics personStatistics) {

            if (personStatistics.getMinutes() != null) {
                try {
                    Duration duration = Duration.parse(personStatistics.getMinutes());
                    // Extract minutes and seconds
                    long minutes = duration.toMinutes();
                    long seconds = duration.getSeconds() % 60; // Remaining seconds after minutes
                    dcLiveMatchPlayerStatistics.setGoalKeeperSecondsPlayed((int) ((minutes * 60) + seconds));
                } catch (Exception e) {
                    log.error("cannot cast " + personStatistics.getMinutes());
                }
            }

        return super.customizeMapToDto(dcLiveMatchPlayerStatistics, personStatistics);
    }
}
