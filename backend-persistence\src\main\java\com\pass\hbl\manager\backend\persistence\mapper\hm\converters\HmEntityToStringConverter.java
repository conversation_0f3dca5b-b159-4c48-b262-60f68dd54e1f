package com.pass.hbl.manager.backend.persistence.mapper.hm.converters;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import org.modelmapper.AbstractConverter;

public class HmEntityToStringConverter<T extends AbstractEntity> extends AbstractConverter<T, String> {
    @Override
    protected String convert(T source) {
        return source == null ? null : source.getId().toString();
    }
}
