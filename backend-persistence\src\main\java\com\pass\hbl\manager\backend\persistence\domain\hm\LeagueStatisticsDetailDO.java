package com.pass.hbl.manager.backend.persistence.domain.hm;

import java.util.UUID;

/**
 * Domain object for league statistics details
 * Contains only the fields needed for league statistics response
 */
public interface LeagueStatisticsDetailDO {
    /**
     * Get the league ID
     * @return League ID
     */
    UUID getId();
    
    /**
     * Get the league name
     * @return League name
     */
    String getName();
    
    /**
     * Get the total member count
     * @return Total member count
     */
    int getTotalMemberCount();
}
