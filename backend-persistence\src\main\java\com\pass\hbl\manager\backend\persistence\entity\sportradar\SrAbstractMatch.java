package com.pass.hbl.manager.backend.persistence.entity.sportradar;

import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.dto.hm.MatchStatus;
import com.pass.hbl.manager.backend.persistence.dto.hm.Winner;
import com.pass.hbl.manager.backend.persistence.exception.MappingException;

import java.time.LocalDateTime;

public class SrAbstractMatch extends AbstractSportradarEntity {

    @Override
    public ExternalEntity getExternalEntity() {
        return ExternalEntity.MATCH;
    }

    public Integer getHblRound() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public void setRound(Integer round) {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public String getSeasonId() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public void setSeasonId(String seasonId) {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public LocalDateTime getStartTime() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public String getHomeClubId() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public String getAwayClubId() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public Integer getHomeScore() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public Integer getAwayScore() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public Integer getHalfTimeHomeScore() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public Integer getHalfTimeAwayScore() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public MatchStatus getMatchStatus() throws MappingException {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public String getMatchTime() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public Winner getWinner() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }
}
