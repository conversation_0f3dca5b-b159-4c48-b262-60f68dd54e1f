package com.pass.hbl.manager.backend.persistence.domain.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

/**
 * Domain object for apple notification payload v2
 */

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppleResponseBodyV2DecodedPayloadDo {

    @Schema(description = "notification type", example = "SUBSCRIBED")
    private String notificationType;

    @Schema(description = "notification type", example = "INITIAL_BUY")
    private String subtype;

    @Schema(description = "A unique identifier for the notification")
    private UUID notificationUUID;

    @Schema(description = "The app metadata and signed renewal and transaction information")
    private ApplePayloadDataDo data;

    @JsonCreator
    public AppleResponseBodyV2DecodedPayloadDo() {
    }
}
