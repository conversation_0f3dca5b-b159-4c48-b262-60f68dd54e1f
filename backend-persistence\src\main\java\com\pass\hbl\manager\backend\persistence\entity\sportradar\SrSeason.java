package com.pass.hbl.manager.backend.persistence.entity.sportradar;

import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Table(name = "season", schema = "sportradar", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class SrSeason extends AbstractSportradarEntity {

    @NotNull
    @Column(name = "name")
    private String name;

    @NotNull
    @Column(name = "start_date")
    private LocalDate startDate;

    @NotNull
    @Column(name = "end_date")
    private LocalDate endDate;

    @NotNull
    @Column(name = "year")
    private String year;

    @Column(name = "disabled")
    private Boolean disabled;

    @NotNull
    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.REFRESH, CascadeType.MERGE, CascadeType.PERSIST})
    @JoinColumn(name = "competition_id", referencedColumnName = "id", nullable = false)
    private SrCompetition competition;

    @Override
    public ExternalEntity getExternalEntity() {
        return ExternalEntity.SEASON;
    }
}
