package com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer;

import com.pass.hbl.manager.backend.persistence.dto.hm.EventScope;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmHpiCalculation;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerMatchEvent;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrLiveMatchEvent;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.HmMatchConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.HmPlayerConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.IdConverter;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmHpiCalculationRepository;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeMap;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
public abstract class Sr2HmPlayerMatchEventMapper extends AbstractSr2HmMapper<SrLiveMatchEvent, HmPlayerMatchEvent> {

    private final EventScope scope;

    private final HmMatchConverter matchConverter;
    private final HmPlayerConverter playerConverter;

    private final HmHpiCalculationRepository hpiCalculationRepository;
    private List<HmHpiCalculation> hpiCalculations;

    public Sr2HmPlayerMatchEventMapper(IdConverter idConverter,
                                       EventScope scope,
                                       HmMatchConverter matchConverter,
                                       HmPlayerConverter playerConverter,
                                       HmHpiCalculationRepository hpiCalculationRepository) {
        super(SrLiveMatchEvent.class, HmPlayerMatchEvent.class, idConverter);
        this.scope = scope;
        this.matchConverter = matchConverter;
        this.playerConverter = playerConverter;
        this.hpiCalculationRepository = hpiCalculationRepository;
    }

    @PostConstruct
    private void init() {
        // cache all HPI values to be faster until L2 cache is implemented.
        // drawback: DB changes in the HPI calculation are currently not taken into account
        hpiCalculations = Util.toStream(this.hpiCalculationRepository.findAll()).collect(Collectors.toList());
    }

    public void resetConverterCaches() {
        matchConverter.resetCache();
        playerConverter.resetCache();
    }

    @Override
    protected void customizeInit() {
        TypeMap<SrLiveMatchEvent, HmPlayerMatchEvent> typeMap = getOrCreateTypeMap(SrLiveMatchEvent.class, HmPlayerMatchEvent.class);
        typeMap.addMappings(mapper -> mapper.using(matchConverter).map(SrLiveMatchEvent::getMatchId, HmPlayerMatchEvent::setMatch));
        typeMap.addMappings(mapper -> mapper.map(SrLiveMatchEvent::getTime, HmPlayerMatchEvent::setMatchTime));
        switch (scope) {
            case PLAYER_1 -> typeMap.addMappings(mapper -> mapper.using(playerConverter).map(SrLiveMatchEvent::getPlayerId1, HmPlayerMatchEvent::setPlayer));
            case PLAYER_2 -> typeMap.addMappings(mapper -> mapper.using(playerConverter).map(SrLiveMatchEvent::getPlayerId2, HmPlayerMatchEvent::setPlayer));
            case GOALKEEPER -> typeMap.addMappings(mapper -> mapper.using(playerConverter).map(SrLiveMatchEvent::getGoalkeeper, HmPlayerMatchEvent::setPlayer));
        }
    }


    @Override
    protected HmPlayerMatchEvent customizeMapToDto(HmPlayerMatchEvent hm, SrLiveMatchEvent sr, Map<String, Object> ctx) {
        if (sr == null) {
            return null;
        }
        if (hm.getMatch() == null || hm.getPlayer() == null) {
            log.warn("Unmappable event with unknown match or player. Skipping: " + sr);
            return null;
        }

        // we ignore some cases:
        // - fouled player who gained a penalty (161/P1)
        // - player receiving a block (172/P1)
        // if (sr.getType() == 161 && scope == EventScope.PLAYER_1 || sr.getType() == 172 && scope == EventScope.PLAYER_1) {
        /*if (sr.getType() == 172 && scope == EventScope.PLAYER_1) {
            return null;
        }*/

        List<HmHpiCalculation> calculationsFilteredByTypeThenExtra = hpiCalculations.stream()
                .filter(hpi -> Objects.equals(hpi.getSrType(), sr.getType()))
                .filter(hpi -> {
                    if (sr.getType() == 43) {
                        // type = 43 (time_penalty) extra could be any value
                        return isNull(hpi.getSrExtra()) || Objects.equals(hpi.getSrExtra(), sr.getExtra());
                    } else {
                        return nonNull(sr.getExtra()) ? Objects.equals(hpi.getSrExtra(), sr.getExtra())
                                : isNull(hpi.getSrExtra());
                    }
                }).toList();
        HmHpiCalculation hpiCalculation;
        // first find a rule with exactly the same position
        hpiCalculation = calculationsFilteredByTypeThenExtra.stream()
                .filter(hpi -> Objects.equals(hpi.getSrPosition(), sr.getPosition()))
                .filter(hpi -> Objects.equals(hpi.getScope(), this.scope))
                .findFirst().orElse(null);
        // if not found try to find a rule with unknown position (position = null)
        if (isNull(hpiCalculation)) {
            hpiCalculation = calculationsFilteredByTypeThenExtra.stream()
                    .filter(hpi -> isNull(hpi.getSrPosition()))
                    .filter(hpi -> Objects.equals(hpi.getScope(), this.scope))
                    .findFirst().orElse(null);

        }

        if (hpiCalculation == null) {
            log.warn("SR event cannot be translated to HPI. Skipping: " + sr);
            return null;
        }


        hm.setEvent(hpiCalculation.getEvent());
        hm.setEventCharacteristic(hpiCalculation.getEventCharacteristic());
        hm.setScore(hpiCalculation.getScore());

        return hm;
    }
}
