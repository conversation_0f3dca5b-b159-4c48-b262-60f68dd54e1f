package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "Name")
@NoArgsConstructor
@Data
public class NameXml {

    @JacksonXmlProperty(localName = "language", isAttribute = true)
    private String language;

    @JacksonXmlText
    private String value;
}
