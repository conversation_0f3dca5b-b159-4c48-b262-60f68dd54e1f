package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.MatchStatus;
import com.pass.hbl.manager.backend.persistence.dto.hm.Winner;
import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Table(name = "match", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@Entity
@ToString
@SQLDelete(sql = "UPDATE hm.match SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmMatch extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @Column(name = "hbl_round")
    private Integer hblRound;

    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "round_id", referencedColumnName = "id")
    private HmRound round;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "season_id", referencedColumnName = "id", updatable = false)
    private HmSeason season;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "match_status")
    private MatchStatus matchStatus;

    @NotNull
    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Column(name = "match_time")
    private String matchTime;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "home_club_id", referencedColumnName = "id")
    private HmClub home;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "away_club_id", referencedColumnName = "id")
    private HmClub away;

    @Min(0)
    @Column(name = "home_score")
    private Integer homeScore;

    @Min(0)
    @Column(name = "away_score")
    private Integer awayScore;

    @Min(0)
    @Column(name = "half_time_home_score")
    private Integer halfTimeHomeScore;

    @Min(0)
    @Column(name = "half_time_away_score")
    private Integer halfTimeAwayScore;

    @Enumerated(EnumType.STRING)
    @Column(name = "winner")
    private Winner winner;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "match")
    private List<HmPlayerRoundStatistics> statistics;
}
