package com.pass.hbl.manager.backend.persistence.mapper.admin;

import com.pass.hbl.manager.backend.persistence.dto.admin.ParameterDto;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminParameter;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.springframework.stereotype.Component;

@Component
public class AdminParameterMapper extends AbstractMapper<AdminParameter, ParameterDto> {

    public AdminParameterMapper() {
        super(AdminParameter.class, ParameterDto.class);
    }
}
