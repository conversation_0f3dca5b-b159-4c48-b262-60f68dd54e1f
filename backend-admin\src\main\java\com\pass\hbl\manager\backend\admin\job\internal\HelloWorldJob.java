package com.pass.hbl.manager.backend.admin.job.internal;

import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.SchedulingException;
import com.pass.hbl.manager.backend.persistence.job.admin.AdminAbstractJob;
import com.pass.hbl.manager.backend.persistence.job.ScheduledJob;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ScheduledJob(description = "HelloWorld job for testing. Running for 10secs doing nothing", parameters = "name=HBL")
public class HelloWorldJob extends AdminAbstractJob {

    public HelloWorldJob(ParameterService parameterService) {
        super(parameterService);
    }

    @Override
    protected void work() throws SchedulingException, FormatException {
        log.info("Start HelloWorld job with two steps to have to option to test cancellation.");
        log.info("Start 1st step for 5secs");
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            // nothing to do
        }
        log.info("Hello " + getParameter("name"));
        if (isTerminate()) {
            log.info("Step out of HelloWorld job after 1st step.");
        }
        log.info("Start 2nd step for 5secs");
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            // nothing to do
        }
        log.info("Finished HelloWorld job regularly.");
    }

    @Override
    protected void init() {
        log.info("Init HelloWorld job.");
    }

    @Override
    protected void terminate() {
        log.info("Terminate HelloWorld job.");
    }

    @Override
    protected void tearDown() {
        log.info("Tear down HelloWorld job.");
    }
}
