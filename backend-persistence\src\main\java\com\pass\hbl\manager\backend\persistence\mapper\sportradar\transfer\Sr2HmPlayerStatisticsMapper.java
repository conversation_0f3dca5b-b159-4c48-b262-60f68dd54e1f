package com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmClub;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerInClub;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerRoundScore;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerRoundStatistics;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrAbstractPlayerStatistics;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrMatchPlayerStatistics;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.TransientSrLiveMatchPlayerStatistics;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.HmMatchConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.HmPlayerConverter;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.IdConverter;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerMatchEventRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerRoundScoreRepository;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class Sr2HmPlayerStatisticsMapper extends AbstractSr2HmMapper<SrAbstractPlayerStatistics, HmPlayerRoundStatistics> {

    @Getter
    private final HmMatchConverter matchConverter;
    @Getter
    private final HmPlayerConverter playerConverter;

    private final HmPlayerRoundScoreRepository roundScoreRepository;
    private final HmPlayerMatchEventRepository playerMatchEventRepository;

    public Sr2HmPlayerStatisticsMapper(IdConverter idConverter, HmMatchConverter matchConverter, HmPlayerConverter playerConverter, HmPlayerRoundScoreRepository roundScoreRepository, HmPlayerMatchEventRepository playerMatchEventRepository) {
        super(SrAbstractPlayerStatistics.class, HmPlayerRoundStatistics.class, idConverter, false);
        this.matchConverter = matchConverter;
        this.playerConverter = playerConverter;
        this.roundScoreRepository = roundScoreRepository;
        this.playerMatchEventRepository = playerMatchEventRepository;
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected void customizeInit() {
        registerDateConverters();
        registerPrimitiveConverters();
        fillTypeMap(SrMatchPlayerStatistics.class);
        fillTypeMap(TransientSrLiveMatchPlayerStatistics.class);
    }

    public void resetConverterCaches() {
        matchConverter.resetCache();
        playerConverter.resetCache();
    }

    @SuppressWarnings("DuplicatedCode")
    private void fillTypeMap(Class<? extends SrAbstractPlayerStatistics> clazz) {
        TypeMap<? extends SrAbstractPlayerStatistics, HmPlayerRoundStatistics> typeMap = getOrCreateTypeMap(clazz, HmPlayerRoundStatistics.class);
        typeMap.addMappings(mapper -> mapper.using(playerConverter).map(SrAbstractPlayerStatistics::getPlayerSrId, HmPlayerRoundStatistics::setPlayer));
        typeMap.addMappings(mapper -> mapper.using(matchConverter).map(SrAbstractPlayerStatistics::getMatchSrId, HmPlayerRoundStatistics::setMatch));

        typeMap.addMappings(mapper -> mapper.skip(HmPlayerRoundStatistics::setTotalScore));
        typeMap.addMappings(mapper -> mapper.skip(HmPlayerRoundStatistics::setId));
        typeMap.addMappings(mapper -> mapper.skip(HmPlayerRoundStatistics::setCreatedAt));
        typeMap.addMappings(mapper -> mapper.skip(HmPlayerRoundStatistics::setDeleted));
        typeMap.addMappings(mapper -> mapper.skip(HmPlayerRoundStatistics::setDeletedAt));
        typeMap.addMappings(mapper -> mapper.skip(HmPlayerRoundStatistics::setModified));
        typeMap.addMappings(mapper -> mapper.skip(HmPlayerRoundStatistics::setModifiedAt));

        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getMatch().setCreatedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getMatch().setDeleted(false)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getMatch().setDeletedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getMatch().setId(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getMatch().setModifiedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getMatch().setModified(false)));

        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getPlayer().setCreatedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getPlayer().setDeleted(false)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getPlayer().setDeletedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getPlayer().setId(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getPlayer().setModifiedAt(null)));
        typeMap.addMappings(mapper -> mapper.skip((destination, value) -> destination.getPlayer().setModified(false)));
    }

    @Override
    protected HmPlayerRoundStatistics customizeMapToDto(HmPlayerRoundStatistics hm, SrAbstractPlayerStatistics sr, Map<String, Object> context) {
        if (hm.getPlayer() == null || hm.getMatch() == null) {
            log.warn("Cannot convert statistics, unknown match or player: " + sr);
            return null;
        }

        hm.setRound(hm.getMatch().getRound());
        //hm.setTotalScore(roundScoreRepository.findFirstByPlayerIdAndMatchId(hm.getPlayer().getId(), hm.getMatch().getId()).map(HmPlayerRoundScore::getScore).orElse(null));

        // Important: The total score is calculated based on HmPlayerMatchEvent instead of HmPlayerRoundScore entities.
        // When trying to retrieve the total score using EntityManager#find(), we will get back the result from the 1st Level Cache
        // that is storing our uncommitted player match events (transfer events); otherwise it will query HmPlayerRoundScore which
        // retrieves only committed changes.
        hm.setTotalScore(playerMatchEventRepository.findTotalScoreByPlayerIdAndMatchId(hm.getPlayer().getId(), hm.getMatch().getId()));
        if (Objects.equals(hm.getPlayer().getCurrentClub().stream().map(HmPlayerInClub::getClub).map(HmClub::getId).findFirst().orElse(null), hm.getMatch().getAway().getId())) {
            hm.setChallenger(hm.getMatch().getHome());
        } else {
            hm.setChallenger(hm.getMatch().getAway());
        }
        return hm;
    }
}
