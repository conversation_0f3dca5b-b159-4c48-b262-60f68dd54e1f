package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerRoundStatisticsDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.*;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.HmEntityToStringConverter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

@Component
public class HmPlayerRoundStatisticsMapper extends AbstractMapper<HmPlayerRoundStatistics, PlayerRoundStatisticsDto> {

    public HmPlayerRoundStatisticsMapper() { super(HmPlayerRoundStatistics.class, PlayerRoundStatisticsDto.class);   }

    @Override
    protected void customizeInit() {
        TypeMap<HmPlayerRoundStatistics, PlayerRoundStatisticsDto> e2d = getOrCreateTypeMap(HmPlayerRoundStatistics.class, PlayerRoundStatisticsDto.class);
        e2d.addMappings(mapper -> mapper.using(new HmEntityToStringConverter<HmPlayer>()).map(HmPlayerRoundStatistics::getPlayer, PlayerRoundStatisticsDto::setPlayerId));
        e2d.addMappings(mapper -> mapper.using(new HmEntityToStringConverter<HmClub>()).map(HmPlayerRoundStatistics::getChallenger, PlayerRoundStatisticsDto::setChallengerId));
        e2d.addMappings(mapper -> mapper.using(new HmEntityToStringConverter<HmRound>()).map(HmPlayerRoundStatistics::getRound, PlayerRoundStatisticsDto::setRoundId));
        e2d.addMappings(mapper -> mapper.using(new HmEntityToStringConverter<HmMatch>()).map(HmPlayerRoundStatistics::getMatch, PlayerRoundStatisticsDto::setMatchId));
    }
}
