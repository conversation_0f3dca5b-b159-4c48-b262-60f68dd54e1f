package com.pass.hbl.manager.backend.persistence.mapper.sportradar.live;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrLiveMatchEvent;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.xml.EventXml;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.live.converter.IdConverter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class XmlToSrLiveMatchEventMapper extends AbstractMapper<EventXml, SrLiveMatchEvent> {

    public static final String KEY_MATCH_ID = "matchId";

    private final IdConverter.EventIdConverter eventIdConverter;
    private final IdConverter.PlayerIdConverter playerIdConverter;

    private final IdConverter.MatchIdConverter matchIdConverter;

    public XmlToSrLiveMatchEventMapper(IdConverter.EventIdConverter eventIdConverter,
                                       IdConverter.PlayerIdConverter playerIdConverter,
                                       IdConverter.MatchIdConverter matchIdConverter) {
        super(EventXml.class, SrLiveMatchEvent.class);
        this.eventIdConverter = eventIdConverter;
        this.playerIdConverter = playerIdConverter;
        this.matchIdConverter = matchIdConverter;
    }

    @Override
    protected void customizeInit() {
        TypeMap<EventXml, SrLiveMatchEvent> typeMap = getOrCreateTypeMap(EventXml.class, SrLiveMatchEvent.class);
        typeMap.addMappings(mapper -> mapper.using(eventIdConverter).map(EventXml::getId, SrLiveMatchEvent::setId));
        typeMap.addMappings(mapper -> mapper.map(EventXml::getType, SrLiveMatchEvent::setType));
        typeMap.addMappings(mapper -> mapper.map(EventXml::getExtra, SrLiveMatchEvent::setExtra));
        typeMap.addMappings(mapper -> mapper.map(EventXml::getOutcome, SrLiveMatchEvent::setOutcome));
        typeMap.addMappings(mapper -> mapper.map(EventXml::getDisabled, SrLiveMatchEvent::setDisabled));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getTime().getValue(), SrLiveMatchEvent::setTime));
        typeMap.addMappings(mapper -> mapper.using(playerIdConverter).map(source -> source.getPlayer1().getId(), SrLiveMatchEvent::setPlayerId1));
        typeMap.addMappings(mapper -> mapper.using(playerIdConverter).map(source -> source.getPlayer2().getId(), SrLiveMatchEvent::setPlayerId2));
        typeMap.addMappings(mapper -> mapper.using(playerIdConverter).map(source -> source.getGoalkeeper().getId(), SrLiveMatchEvent::setGoalkeeper));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getScore().getTeam1().getValue(), SrLiveMatchEvent::setScoreHome));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getScore().getTeam2().getValue(), SrLiveMatchEvent::setScoreAway));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getPosition().getValue(), SrLiveMatchEvent::setPosition));
    }

    @Override
    protected SrLiveMatchEvent customizeMapToDto(SrLiveMatchEvent srLiveMatchEvent, EventXml eventXml, Map<String, Object> context) {
        srLiveMatchEvent.setMatchId(matchIdConverter.convert((Integer) context.get(KEY_MATCH_ID)));
        return super.customizeMapToDto(srLiveMatchEvent, eventXml, context);
    }
}
