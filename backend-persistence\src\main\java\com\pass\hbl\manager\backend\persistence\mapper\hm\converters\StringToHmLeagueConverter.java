package com.pass.hbl.manager.backend.persistence.mapper.hm.converters;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueRepository;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.SneakyThrows;
import org.modelmapper.AbstractConverter;

public class StringToHmLeagueConverter extends AbstractConverter<String, HmLeague> {

    private final HmLeagueRepository repository;

    public StringToHmLeagueConverter(HmLeagueRepository repository) {
        this.repository = repository;
    }

    @SneakyThrows
    @Override
    protected HmLeague convert(String source) {
        return repository.findById(Util.convertId(source)).orElseThrow(() -> new EntityNotExistException(HmLeague.class, source));
    }
}
