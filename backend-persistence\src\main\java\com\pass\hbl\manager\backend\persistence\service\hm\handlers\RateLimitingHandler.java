package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmClientRequest;
import com.pass.hbl.manager.backend.persistence.exception.RateLimitExceededException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmClientRequestRepository;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Handler for rate limiting API requests
 */
@Component
@RequiredArgsConstructor
public class RateLimitingHandler {

    private final HmClientRequestRepository clientRequestRepository;
    private final TransactionHandler transactionHandler;

    /**
     * Check rate limit for API requests
     * The rate limit is 1 request per specified minutes for the same parameters
     *
     * @param changedAfter The changed after date parameter
     * @param pageable The pagination parameters
     * @param requestUrl The request URL
     * @param externalClient The external client identifier
     * @param existingRequestOpt The existing client request if any
     * @param rateLimitMinutes The rate limit in minutes
     * @throws RateLimitExceededException If the rate limit is exceeded
     */
    public void checkRateLimit(LocalDateTime changedAfter, Pageable pageable, String requestUrl, 
                              String externalClient, Optional<HmClientRequest> existingRequestOpt,
                              int rateLimitMinutes) throws RateLimitExceededException {

        LocalDateTime now = LocalDateTime.now();

        Map<String, String> parameters = new HashMap<>();
        if (changedAfter != null) {
            parameters.put("changedAfter", changedAfter.toString());
        }
        if (pageable != null) {
            if (pageable.getPageNumber() != 0) {
                parameters.put("page", String.valueOf(pageable.getPageNumber()));
            }
            if (pageable.getPageSize() != 20) { // Default page size is 20
                parameters.put("size", String.valueOf(pageable.getPageSize()));
            }
        }

        if (existingRequestOpt.isPresent()) {
            HmClientRequest existingRequest = existingRequestOpt.get();

            // Check if the request was made within the rate limit period
            LocalDateTime requestTimePlusRateLimit = existingRequest.getLastRequestDate().plusMinutes(rateLimitMinutes);

            // If there's a request with the same parameters but different page/size, allow it
            boolean isPaginationRequest = false;
            if (pageable != null) {
                Map<String, String> existingParams = existingRequest.getParameterMap();

                // Check if only pagination parameters are different
                if ((existingParams.get("page") != null && parameters.get("page") != null && !existingParams.get("page").equals(parameters.get("page"))) ||
                    (existingParams.get("size") != null && parameters.get("size") != null && !existingParams.get("size").equals(parameters.get("size")))) {
                    isPaginationRequest = true;
                }
            }

            if (!isPaginationRequest && requestTimePlusRateLimit.isAfter(now)) {
                // Rate limit is based on configurable minutes for the same parameters

                // Update the last request date to prevent multiple attempts
                transactionHandler.runInNewTransaction(() -> {
                    // Update parameters with new page/size
                    Map<String, String> updatedParams = existingRequest.getParameterMap();
                    if (pageable != null) {
                        updatedParams.put("page", String.valueOf(pageable.getPageNumber()));
                        updatedParams.put("size", String.valueOf(pageable.getPageSize()));
                    }

                    clientRequestRepository.updateLastRequestDateAndParameters(now, Util.convertToString(updatedParams), existingRequest.getId());
                    return null;
                });

                throw new RateLimitExceededException("Rate limiting exceeded. Next request allowed after: " + requestTimePlusRateLimit + " UTC", 
                                                    requestTimePlusRateLimit.toEpochSecond(ZoneOffset.UTC));
            }

            // Update the parameters for the existing request
            transactionHandler.runInNewTransaction(() -> {
                Map<String, String> updatedParams = existingRequest.getParameterMap();
                updatedParams.putAll(parameters);

                clientRequestRepository.updateLastRequestDateAndParameters(now, Util.convertToString(updatedParams), existingRequest.getId());
                return null;
            });
        } else {
            // No existing request found, create a new one
            HmClientRequest clientRequest = new HmClientRequest(requestUrl, externalClient, now, parameters);

            transactionHandler.runInNewTransaction(() -> clientRequestRepository.save(clientRequest));
        }
    }
}