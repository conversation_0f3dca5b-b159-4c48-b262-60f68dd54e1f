package com.pass.hbl.manager.backend.persistence.entity.hm;


import com.pass.hbl.manager.backend.persistence.dto.hm.ManagerStatusCode;
import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.*;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.UUID;

@Table(name = "user_level", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@ToString
@SQLDelete(sql = "UPDATE hm.user_level SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmUserLevel extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @Enumerated(EnumType.STRING)
    @Column(name = "manager_status_code")
    private ManagerStatusCode managerStatusCode;

    @Column(name = "experience_points")
    private int experiencePoints;

    @Column(name = "level")
    private int level;
}
