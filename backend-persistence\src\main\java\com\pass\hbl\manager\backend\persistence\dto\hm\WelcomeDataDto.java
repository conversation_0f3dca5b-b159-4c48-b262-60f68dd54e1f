package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@JsonRootName("WelcomeData")
@Getter
@Setter
@Schema(description = "Data representing the header information of the app")
public class WelcomeDataDto extends AbstractPictureDto<WelcomeDataDto, String> {

    @JsonIgnore
    private String id;

    @Schema(description = "List of leagues a user is member of")
    @NotNull
    private List<LeagueInfoDto> leagues;

    @Schema(description = "List of leagues a user is waiting for getting membership")
    @NotNull
    private List<LeagueInfoDto> pendingLeagues;

    @Schema(description = "Optional currently displayed league")
    private Map<SessionAttribute, String> sessionAttributes;

    @Schema(description = "user object of the currently logged in user")
    private UserDto user;

    @Schema(description = "the score of the user in the current league, may be null if user has no current league")
    private UserScoreDto currentUserScore;

    @Schema(description = "the label of current season", example = "2021/22")
    private String currentSeason;

    @Schema(description = "the minimum required app version", example = "2, if the app version 1 is upgraded to 2")
    private int minVersionRequired;

    @Schema(description = "true if transfer market is active, otherwise false", example = "league winner award")
    @JsonProperty("isTransferMarketActive")
    private boolean isTransferMarketActive;

    @Schema(description = "The reason for which the transfer market is inactive", example = "season end")
    private TransferMarketInactiveReason transferMarketInactiveReason;

    @JsonCreator
    public WelcomeDataDto() {
    }
}
