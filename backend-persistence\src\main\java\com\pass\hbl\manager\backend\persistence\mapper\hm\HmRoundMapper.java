package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.RoundDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmRound;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmSeason;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.service.hm.SeasonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class HmRoundMapper extends AbstractMapper<HmRound, RoundDto> {

    private final SeasonService seasonService;

    public HmRoundMapper(@Lazy SeasonService seasonService) { super(HmRound.class, RoundDto.class);
        this.seasonService = seasonService;
    }

    @Override
    protected RoundDto customizeMapToDto(RoundDto roundDto, HmRound round) {
        try {
            HmSeason season = round.getSeason();
            List<HmRound> sortedRounds = seasonService.getSortedRounds(season);
            roundDto.setRoundOrder(sortedRounds.indexOf(round) + 1);
        } catch (Exception e) {
            log.info("Failed to set the display order for round id [" + round.getId() + "]");
        }
        return roundDto;
    }
}
