package com.pass.hbl.manager.backend.persistence.dto.datacore;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonRootName("get datacore streaming access response")
@Getter
@Setter
@Schema(description = "Get datacore streaming access response data")
public class DataCoreStreamingAccessResponseDto {

    private DataObject data;

    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonRootName("get datacore streaming access data")
    @Getter
    @Setter
    @NoArgsConstructor
    public static class DataObject {

        @NotNull
        @Schema(description = "token", required = true)
        private String url;

        @NotNull
        @Schema(description = "token", required = true)
        private String clientId;

        @NotNull
        @Schema(description = "token", required = true)
        private String expiry;

        @NotNull
        @Schema(description = "token", required = true)
        private List<StreamingTopicDto> topics;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StreamingTopicDto {

        private String topic;

        private String scope;

        private String permission;
    }

    @JsonCreator
    public DataCoreStreamingAccessResponseDto() {
    }
}
