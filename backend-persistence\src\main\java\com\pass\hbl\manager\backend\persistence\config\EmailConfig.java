package com.pass.hbl.manager.backend.persistence.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Component;

import java.util.Properties;

@Component
@Slf4j
public class EmailConfig {

    @Bean
    public JavaMailSender getJavaMailSender(Environment env) {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        String host = env.getProperty("spring.mail.host", "localhost");
        log.info("EmailConfig: host = " + host);
        mailSender.setHost(host);

        String port = env.getProperty("spring.mail.port", "25");
        log.info("EmailConfig: port = " + port);
        mailSender.setPort(Integer.parseInt(port));

        mailSender.setUsername(null);
        mailSender.setPassword(null);

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        String smtpAuth = env.getProperty("spring.mail.properties.mail.smtp.auth", "false");
        log.info("EmailConfig: smtpAuth = " + smtpAuth);
        props.put("mail.smtp.auth", smtpAuth);
        String starttlsEnable = env.getProperty("spring.mail.properties.mail.smtp.starttls.enable", "true");
        log.info("EmailConfig: starttlsEnabled = " + smtpAuth);
        props.put("mail.smtp.starttls.enable", starttlsEnable);

        return mailSender;
    }
}
