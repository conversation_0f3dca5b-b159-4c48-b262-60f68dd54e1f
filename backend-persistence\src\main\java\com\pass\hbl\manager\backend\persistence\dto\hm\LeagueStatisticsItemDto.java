package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * League statistics item data transfer object
 * Used for individual league items in the league statistics API endpoint
 * Contains league details without totalLeagueCount (which is at the parent level)
 */
@JsonRootName("LeagueStatisticsItem")
@Getter
@Setter
@Schema(description = "Individual league statistics item")
public class LeagueStatisticsItemDto {

    @Schema(description = "League id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "League name", example = "Elite Handball League", required = true)
    @NotBlank
    @Size(max = 256)
    private String name;

    @Schema(description = "Total number of members in the league", example = "10", required = true)
    @Min(0)
    private int totalMemberCount;

    @JsonCreator
    public LeagueStatisticsItemDto() {
    }

    public LeagueStatisticsItemDto(String id, String name, int totalMemberCount) {
        this.id = id;
        this.name = name;
        this.totalMemberCount = totalMemberCount;
    }
}
