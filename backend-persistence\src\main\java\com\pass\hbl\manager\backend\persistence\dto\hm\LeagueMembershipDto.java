package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;

@JsonRootName("LeagueMembership")
@Getter
@Setter
@Schema(description = "Relation between user and league")
public class LeagueMembershipDto extends AbstractDto<LeagueMembershipDto, String> {

    @Schema(description = "League Membership id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "User Id", required = true)
    @NotBlank
    @Size(max = 32)
    private String userId;

    @Schema(description = "League Id", required = true)
    @NotBlank
    @Size(max = 32)
    private String leagueId;

    @Schema(description = "Time when the user created or joined the league")
    private ZonedDateTime joined;

    @Schema(description = "Time when the user left the league")
    private ZonedDateTime left;

    @JsonCreator
    public LeagueMembershipDto() {
    }

    @JsonCreator
    public LeagueMembershipDto(String memberId, String leagueId, ZonedDateTime joined) {
        this.userId = memberId;
        this.leagueId = leagueId;
        this.joined = joined;
    }

    @JsonCreator
    public LeagueMembershipDto(String id, String userId, String leagueId, ZonedDateTime joined, ZonedDateTime left) {
        this.id = id;
        this.userId = userId;
        this.leagueId = leagueId;
        this.joined = joined;
        this.left = left;
    }
}
