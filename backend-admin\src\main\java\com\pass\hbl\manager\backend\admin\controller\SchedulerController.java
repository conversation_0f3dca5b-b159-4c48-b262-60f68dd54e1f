package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.persistence.exception.SchedulingException;
import com.pass.hbl.manager.backend.persistence.service.admin.AdminSchedulerService;
import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobDto;
import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobMode;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping(ApiConstants.SCHEDULER_API)
@Validated
@Tag(name = "scheduler")
@RolesAllowed({ApiConstants.ROLE_ADMIN_WRITE})
public class SchedulerController {

    private final AdminSchedulerService service;

    public SchedulerController(AdminSchedulerService service) {
        this.service = service;
    }

    @Operation(description = "list all scheduler jobs")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = SchedulerJobDto.class)))})
    })
    @GetMapping(value = "/jobs", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SchedulerJobDto> getAllSchedulerJobs() {
        return service.getAllSchedulerJobs();
    }

    @Operation(description = "Find a job by name")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = SchedulerJobDto.class))})
    })
    @GetMapping(value = "/job", produces = MediaType.APPLICATION_JSON_VALUE)
    public Optional<SchedulerJobDto> findJobByName(
            @Parameter(name = "name", description = "name of the job", required = true, example = "ImportPlayerJob")
            @RequestParam String name) {
        return service.findJobByName(name);
    }

    @Operation(description = "Find a job by id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = SchedulerJobDto.class))})
    })
    @GetMapping(value = "/job/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public SchedulerJobDto getJob(
            @Parameter(name = "id", description = "id of the job in UUID format", required = true, example = "69a29fcf-f58e-4c7a-956f-0fb41333841f")
            @PathVariable String id) throws SchedulingException, FormatException {
        return service.getJob(id);
    }

    @Operation(description = "list all running scheduler jobs")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = SchedulerJobDto.class)))})
    })
    @GetMapping(value = "/jobs/running", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SchedulerJobDto> getRunningJobs() {
        return service.getRunningJobs();
    }

    @Operation(description = "Refresh the schedule of the cron jobs from the database")
    @PutMapping("/jobs/refreshSchedule")
    public void refreshSchedule(@RequestParam boolean interruptIfRunning) throws SchedulingException {
        service.refreshSchedules(interruptIfRunning);
    }

    @Operation(description = "Terminate all schedules")
    @PutMapping("/jobs/terminateAllSchedules")
    public void terminateAllSchedules(@RequestParam boolean interruptIfRunning) {
        service.terminateAllSchedules(interruptIfRunning);
    }

    @Operation(description = "Run a job")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description ="ok", content = { @Content(mediaType = MediaType.TEXT_PLAIN_VALUE, schema = @Schema(implementation = Boolean.class))}),
            @ApiResponse(responseCode = "400", description ="job cannot be scheduled", content = { @Content(mediaType = MediaType.TEXT_PLAIN_VALUE, schema = @Schema(implementation = Boolean.class))})
    })
    @PutMapping(value = "/job/run/{id}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Boolean runJob(
            @Parameter(name = "id", description = "id of the job")
            @PathVariable String id,
            @Parameter(name = "params", description = "optional parameters")
            @RequestBody(required = false) Map<String, String> params) throws SchedulingException, FormatException {
        return service.runJob(id, params);
    }

    @Operation(description = "Run a job")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description ="ok", content = { @Content(mediaType = MediaType.TEXT_PLAIN_VALUE, schema = @Schema(implementation = Boolean.class))}),
            @ApiResponse(responseCode = "400", description ="job cannot be scheduled", content = { @Content(mediaType = MediaType.TEXT_PLAIN_VALUE, schema = @Schema(implementation = Boolean.class))})
    })
    @PutMapping(value = "test/job/run/{id}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Boolean runJobTest(
            @Parameter(name = "id", description = "id of the job")
            @PathVariable String id,
            @Parameter(name = "params", description = "optional parameters")
            @RequestBody(required = false) Map<String, String> params) throws SchedulingException, FormatException {
        return service.runJob(id, params);
    }

    @Operation(description = "Stop a running job")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description ="ok"),
            @ApiResponse(responseCode = "400", description ="job cannot be stopped")
    })
    @PutMapping(value = "/job/stop/{id}")
    public void stopJob(
            @Parameter(name = "id", description = "id of the job")
            @PathVariable String id,
            @Parameter(name = "mode", description = "the running mode")
            @RequestParam SchedulerJobMode mode,
            @Parameter(name = "interruptIfRunning", description = "if job should be terminated the hard way")
            @RequestParam boolean interruptIfRunning) throws FormatException {
        service.terminateJobSchedule(id, mode, interruptIfRunning);
    }

    @Operation(description = "Stop a running job")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description ="ok"),
            @ApiResponse(responseCode = "400", description ="job cannot be stopped")
    })
    @PutMapping(value = "test/job/stop/{id}")
    public void stopJobTest(
            @Parameter(name = "id", description = "id of the job")
            @PathVariable String id,
            @Parameter(name = "mode", description = "the running mode")
            @RequestParam SchedulerJobMode mode,
            @Parameter(name = "interruptIfRunning", description = "if job should be terminated the hard way")
            @RequestParam boolean interruptIfRunning) throws FormatException {
        service.terminateJobSchedule(id, mode, interruptIfRunning);
    }

    @Operation(description = "Save a job")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = SchedulerJobDto.class))}),
            @ApiResponse(responseCode = "400", description = "error")
    })
    @PostMapping(value = "/job/save", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public SchedulerJobDto save(
            @Parameter(name = "dto", description = "the scheduler job to save")
            @RequestBody SchedulerJobDto dto,
            @Parameter(name = "interruptIfRunning", description = "if job should be terminated the hard way")
            @RequestParam boolean interruptIfRunning) throws SchedulingException, FormatException {
        return service.save(dto, interruptIfRunning);
    }
}
