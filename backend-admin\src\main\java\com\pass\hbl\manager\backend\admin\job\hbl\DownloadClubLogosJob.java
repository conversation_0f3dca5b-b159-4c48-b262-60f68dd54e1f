package com.pass.hbl.manager.backend.admin.job.hbl;

import com.pass.hbl.manager.backend.persistence.service.hbl.DownloadPictureService;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.SchedulingException;
import com.pass.hbl.manager.backend.persistence.job.admin.AdminAbstractJob;
import com.pass.hbl.manager.backend.persistence.job.ScheduledJob;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ScheduledJob(description = "Download club logos", parameters = "url=https://images.dc.prod.cloud.atriumsports.com/%s/%s?size=raw")
public class DownloadClubLogosJob extends AdminAbstractJob {

    private final DownloadPictureService service;

    public DownloadClubLogosJob(ParameterService parameterService, DownloadPictureService service) {
        super(parameterService);
        this.service = service;
    }

    @Override
    protected void work() throws SchedulingException, FormatException {
        String url = getParameter("url");

        // Update DataCore-Api: add suffix to the url to indicate the file size
        url = url + "=raw";
        service.checkClubs(url, getParameterAsBoolean("overwrite"));
    }

    @Override
    protected void init() {

    }

    @Override
    protected void tearDown() {

    }

    @Override
    protected void terminate() {
        service.cancel();
    }
}
