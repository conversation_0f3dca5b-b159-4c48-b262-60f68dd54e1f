package com.pass.hbl.manager.backend.persistence.exception;

import lombok.Getter;

import java.util.UUID;

@Getter
public class InvalidOperationException extends CodedException {

    private final String operation;

    private final String user;

    private final String reason;

    public InvalidOperationException(String operation, String user, String reason) {
        super(ExceptionCode.INVALID_OPERATION, "Operation '" + operation + "' was is invalid for user '" + user + "'. Reason: " + reason, operation, user, reason);
        this.operation = operation;
        this.user = user;
        this.reason = reason;
    }

    public InvalidOperationException(String operation, UUID user, String reason) {
        this(operation, user.toString(), reason);
    }
}
