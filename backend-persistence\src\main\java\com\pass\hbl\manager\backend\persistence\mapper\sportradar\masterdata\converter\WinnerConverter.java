package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.dto.hm.Winner;
import com.pass.hbl.manager.backend.persistence.exception.MappingException;
import com.pass.hbl.manager.backend.persistence.util.Util;
import com.sportradar.handball.v2.model.Competitor;
import com.sportradar.handball.v2.model.EnumCompetitorQualifier;
import com.sportradar.handball.v2.model.Summary;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class WinnerConverter extends AbstractConverter<Summary, Winner> {
    @Override
    protected Winner convert(Summary source) {
        if (source == null) {
            return null;
        }
        if (Objects.nonNull(source.getSportEventStatus().getWinnerId())) {
            try {
                return Util.toStream(source.getSportEvent().getCompetitors())
                        .filter(c -> Objects.equals(c.getId(), source.getSportEventStatus().getWinnerId()))
                        .map(Competitor::getQualifier)
                        .filter(Objects::nonNull)
                        .map(EnumCompetitorQualifier::getValue)
                        .map(Winner::getByStringCaseIgnore)
                        .filter(Objects::nonNull)
                        .findFirst().orElseThrow(() -> new MappingException("Unknown winner qualifier", Datasource.SPORTRADAR, "", Winner.class));
            } catch (MappingException e) {
                Util.sneakyThrow(e);
            }
        }

        if (source.getSportEventStatus().getHomeScore() != null &&
                source.getSportEventStatus().getAwayScore() != null &&
                Objects.equals(source.getSportEventStatus().getHomeScore(), source.getSportEventStatus().getAwayScore())
        ) {
            return Winner.DRAW;
        }
        return Winner.N_A;
    }
}
