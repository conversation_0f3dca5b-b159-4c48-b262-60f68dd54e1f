package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrCompetitor;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.repository.sportradar.SrCompetitorRepository;
import com.pass.hbl.manager.backend.persistence.util.Util;
import com.sportradar.handball.v2.model.Competitor;
import com.sportradar.handball.v2.model.SportEvent;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.springframework.util.CollectionUtils;

import java.util.Objects;

public abstract class AbstractCompetitorConverter extends AbstractConverter<SportEvent, SrCompetitor> {

    private final SrCompetitorRepository repository;

    private final String qualifier;

    public AbstractCompetitorConverter(SrCompetitorRepository repository, String qualifier) {
        this.repository = repository;
        this.qualifier = qualifier;
    }

    @SneakyThrows
    @Override
    protected SrCompetitor convert(SportEvent source) {

        if (CollectionUtils.isEmpty(source.getCompetitors())) {
            return null;
        }
        String competitorId = Util.toStream(source.getCompetitors())
                .filter(c -> Objects.nonNull(c.getQualifier()))
                .filter(c -> Objects.equals(c.getQualifier().getValue(), qualifier))
                .map(Competitor::getId).findFirst().orElse(null);

        if (StringUtils.isEmpty(competitorId)) {
            throw new EntityNotExistException(SrCompetitor.class, competitorId);
        }
        return repository.findById(competitorId).orElseThrow(() -> new EntityNotExistException(SrCompetitor.class, competitorId));
    }
}
