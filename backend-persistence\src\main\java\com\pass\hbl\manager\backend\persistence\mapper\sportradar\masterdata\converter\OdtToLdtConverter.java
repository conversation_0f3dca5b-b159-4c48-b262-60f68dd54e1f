package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;
import org.threeten.bp.OffsetDateTime;

import java.time.LocalDateTime;

@Component
public class OdtToLdtConverter extends AbstractConverter<OffsetDateTime, LocalDateTime> {
    @Override
    protected LocalDateTime convert(OffsetDateTime source) {
        if (source == null) {
            return null;
        }
        return LocalDateTime.of(source.getYear(), source.getMonthValue(), source.getDayOfMonth(),
                source.getHour(), source.getMinute(), source.getSecond(), source.getNano());
    }
}
