package com.pass.hbl.manager.backend.persistence.mapper.datacore.live.converter;

import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminExternalDataMapping;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmMatch;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmMatchRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ExternalDataMappingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import static com.pass.hbl.manager.backend.persistence.dto.admin.Datasource.DATACORE;

@Component
@Slf4j
public class DcMatchConverter extends AbstractConverter<String, HmMatch> {

    private final ExternalDataMappingService externalDataMappingService;

    private final HmMatchRepository matchRepository;

    private final Map<String, HmMatch> cache;

    public DcMatchConverter(ExternalDataMappingService externalDataMappingService, HmMatchRepository matchRepository) {
        this.externalDataMappingService = externalDataMappingService;
        this.matchRepository = matchRepository;
        this.cache = new ConcurrentHashMap<>();
    }

    public void resetCache() {
        this.cache.clear();
    }

    @Override
    protected HmMatch convert(String source) {
        //example source = fixtureId = 71d24683-9e83-11ee-9084-b5570c7f34b9
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        if (cache.containsKey(source)) {
            return cache.get(source);
        }


        try {
            Optional<HmMatch> match = externalDataMappingService.get(DATACORE, ExternalEntity.MATCH, source)
                    .map(AdminExternalDataMapping::getHmId)
                    .map(matchRepository::findById)
                    .filter(Optional::isPresent)
                    .map(Optional::get);
            if (match.isEmpty()) {
                log.warn("Unknown match with dcId=" + source);
                return null;
            }
            cache.put(source, match.get());
            return match.get();
        } catch (Exception e) {
            log.error("Converting match with dcId=" + source + "to HmMatch failed", e);
            return null;
        }
    }
}
