package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@JsonRootName("UserScore")
@Getter
@Setter
@Schema(description = "List of leagues a user is member of")
public class UserScoreDto extends AbstractDto<UserScoreDto, String> {

    @JsonIgnore
    private String id;

    @Schema(description = "Level of the player in the given league", example = "7", required = true)
    @NotNull
    @Min(0)
    private int level;

    @Schema(description = "Score of the player in the given league", example = "846", required = true)
    @NotNull
    @Min(0)
    private int score;

    @Schema(description = "Account balance of the player for the given league", example = "120813", required = true)
    @NotNull
    @Min(0)
    private int accountBalance;

    @JsonCreator
    public UserScoreDto() {
    }

    @JsonCreator
    public UserScoreDto(String id, int level, int score, int accountBalance) {
        this.id = id;
        this.level = level;
        this.score = score;
        this.accountBalance = accountBalance;
    }
}
