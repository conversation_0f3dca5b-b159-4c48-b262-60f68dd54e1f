package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Table(name = "player_round_statistics", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@Entity
@ToString
@SQLDelete(sql = "UPDATE hm.player_round_statistics SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmPlayerRoundStatistics extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @ToString.Exclude
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "player_id", referencedColumnName = "id", updatable = false)
    private HmPlayer player;

    @ToString.Exclude
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "round_id", referencedColumnName = "id", updatable = false)
    private HmRound round;

    @ManyToOne(fetch = FetchType.LAZY)
    @ToString.Exclude
    @JoinColumn(name = "challenger_id", referencedColumnName = "id", updatable = false)
    private HmClub challenger;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @ToString.Exclude
    @JoinColumn(name = "match_id", referencedColumnName = "id", updatable = false)
    private HmMatch match;

    @ToString.Exclude
    @Column(name = "total_score")
    private Integer totalScore;

    @ToString.Exclude
    @Min(0)
    @Column(name = "assists_number")
    private Integer assistsNumber;

    @ToString.Exclude
    @Min(0)
    @Column(name = "seconds_played")
    private Integer secondsPlayed;

    @ToString.Exclude
    @Min(0)
    @Column(name = "goals_thrown")
    private Integer goalsThrown;

    @ToString.Exclude
    @Min(0)
    @Column(name = "goals_saved")
    private Integer goalsSaved;

    @ToString.Exclude
    @Min(0)
    @Column(name = "throw_rate")
    private Float throwRate;

    @ToString.Exclude
    @Min(0)
    @Column(name = "goals_conceded")
    private Integer goalsConceded;

    @ToString.Exclude
    @Min(0)
    @Column(name = "throws_number")
    private Integer throwsNumber;
}
