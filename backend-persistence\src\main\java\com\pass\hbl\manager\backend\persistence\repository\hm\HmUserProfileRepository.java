package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmUserSessionAttributesDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.UserSsoIdsDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.UserStatisticsBasicDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.SessionAttribute;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.*;

public interface HmUserProfileRepository extends PagingAndSortingRepository<HmUserProfile, UUID> {

    Optional<HmUserProfile> findByEmailAddressIgnoreCase(String email);

    List<HmUserProfile> findAllByIdIn(Set<UUID> ids);

    boolean existsByEmailAddressIgnoreCase(String email);

    boolean existsBySsoIdIgnoreCase(String ssoId);

    Optional<HmUserProfile> findBySsoIdIgnoreCase(String ssoId);

    List<HmUserProfile> findByUsernameIgnoreCase(String name);

    List<HmUserProfile> findByUsernameIgnoreCaseOrEmailAddressIgnoreCase(String name, String emailAddress);

    @Query(value = "select Cast(id as varchar) from hm.user_profile where id in :ids and deleted = true", nativeQuery = true)
    List<String> findNotExistingByIdIn(@Param("ids") Set<UUID> ids);

    @Query("select u.id, u.experiencePointsBackup from HmUserProfile u where u.deleted = false and not u.username = :systemUsername")
    List<Object[]> findAllIdsAndExperiencePointsBackupsNotEqualUser(@Param("systemUsername") String systemUsername);

    @Query(value = "SELECT Cast(u.id as varchar) AS id, Cast(u.sso_id as varchar) AS ssoId FROM hm.user_profile u WHERE u.deleted = false AND u.sso_id <> 'NOT_ASSIGNED'", nativeQuery = true)
    List<UserSsoIdsDO> findAllIdsAndSsoIds();

    @Query("select u.id, u.experiencePoints from HmUserProfile u where u.deleted = false and not u.username = :systemUsername")
    List<Object[]> findAllIdsAndExperiencePointsNotEqualUser(@Param("systemUsername") String systemUsername);

    @Modifying
    @Query("update HmUserProfile p set p.experiencePoints = :experiencePoints, p.modifiedAt = CURRENT_TIMESTAMP where p.id = :id")
    int updateExperiencePointsById(@Param("id") UUID id, @Param("experiencePoints") int experiencePoints);

    @Modifying
    @Query("update HmUserProfile p set p.experiencePoints = p.experiencePoints + :experiencePoints, p.modifiedAt = CURRENT_TIMESTAMP where p.id = :id")
    int addExperiencePointsById(@Param("id") UUID id, @Param("experiencePoints") int experiencePoints);


    @Modifying
    @Query("update HmUserProfile p set p.experiencePoints = :experiencePoints, p.level = :level, p.modifiedAt = CURRENT_TIMESTAMP where p.id = :id")
    int updateExperiencePointsAndLevelById(@Param("id") UUID id, @Param("experiencePoints") int experiencePoints, @Param("level") int level);

    @Modifying
    @Query("update HmUserProfile p set p.experiencePoints = p.experiencePoints - :exPointsToSubtract, p.modifiedAt = CURRENT_TIMESTAMP where p.id = :id")
    int resetExperiencePointsById(@Param("id") UUID id, @Param("exPointsToSubtract") int exPointsToSubtract);

    @Modifying
    @Query("update HmUserProfile p set p.experiencePointsBackup = p.experiencePoints, p.levelBackup = p.level, p.modifiedAt = CURRENT_TIMESTAMP")
    int createExperiencePointsAndLevelBackup();

    @Modifying
    @Query("update HmUserProfile  p set p.sessionAttributes = :s, p.modifiedAt = CURRENT_TIMESTAMP where p.id = :id")
    void updateSessionAttributes(@Param("id") UUID id, @Param("s") Map<SessionAttribute, String> s);

    @Modifying
    @Query("update HmUserProfile p set p.premium = :premium, p.premiumExpiration = :premiumExpiration, p.modifiedAt = CURRENT_TIMESTAMP where p.id = :id")
    int updatePremiumAndPremiumExpirationById(@Param("id") UUID id, @Param("premium") boolean premium, @Param("premiumExpiration") LocalDateTime premiumExpiration);

    @Modifying
    @Query("update HmUserProfile p set p.premiumExpiration = :premiumExpiration, p.modifiedAt = CURRENT_TIMESTAMP where p.id = :id")
    int updatePremiumExpirationById(@Param("id") UUID id, @Param("premiumExpiration") LocalDateTime premiumExpiration);

    @Query("select p.premium, p.premiumExpiration from HmUserProfile p where p.id = :id")
    List<Object[]> findPremiumInfoById(@Param("id") UUID id);

    @Modifying
    @Query("update HmUserProfile u set u.appVersion = :appVersion, u.modifiedAt = CURRENT_TIMESTAMP where u.id = :id and u.deleted = false")
    int setAppVersionById(@Param("id") UUID id, @Param("appVersion") String appVersion);

    @Modifying
    @Query("update HmUserProfile u set u.premium = false, u.premiumExpiration = null, u.isManualSubscription = false, u.modifiedAt = CURRENT_TIMESTAMP where u.id = :id and u.deleted = false")
    int downgradeManualSubscriptionById(@Param("id") UUID id);

    @Modifying
    @Query("update HmUserProfile u set u.appLanguage = :appLanguage, u.modifiedAt = CURRENT_TIMESTAMP where u.id = :id and u.deleted = false")
    int setAppLanguageById(@Param("id") UUID id, @Param("appLanguage") String appLanguage);

    @Query("select u.id as id, u.sessionAttributes as sessionAttributes from HmUserProfile u where u.deleted = false")
    List<HmUserSessionAttributesDO> findAllUserSessionAttributes();

    @Query(value = "select count(*) from hm.user_profile where deleted = true", nativeQuery = true)
    Long countDeletedUsers();

    @Query("select p.appLanguage from HmUserProfile p where p.id = :id and p.deleted = false")
    String findAppLanguageById(UUID id);

    Page<HmUserProfile> findByUsernameContainingIgnoreCaseOrEmailAddressContainingIgnoreCase(String username, String emailAddress, Pageable pageable);

    @Query("""
           SELECT p.id FROM HmUserProfile p
           LEFT JOIN HmLeagueMembership m ON p.id = m.userProfile.id
           WHERE m.league.id IN :leagueIds AND
              ( p.modifiedAt > :changedAfter
              OR p.createdAt > :changedAfter
              OR p.deletedAt > :changedAfter
              OR m.modifiedAt > :changedAfter
              OR m.createdAt > :changedAfter
              OR m.deletedAt > :changedAfter)
           """)
    List<UUID> findIdsByModifiedAtAfter(@Param("changedAfter") LocalDateTime changedAfter, @Param("leagueIds") List<UUID> leagueIds);

    @Query(value = "SELECT  Cast(u.id as varchar) from hm.user_profile u where u.deleted = true and u.deleted_at > :changedAfter", nativeQuery = true)
    List<String> findAllDeletedUserAccountsAfter(@Param("changedAfter") LocalDateTime changedAfter);

    @Query(value = "SELECT CAST(u.id AS varchar) FROM hm.user_profile u WHERE u.deleted = true", nativeQuery = true)
    List<String> findAllDeletedUserAccounts();

    @Query("""
           SELECT p.id FROM HmUserProfile p where p.deleted = false
           """)
    List<UUID> findAllIds();

    @Query(value = """
       SELECT cast(u.id as varchar ) AS id,
              u.sso_id AS ssoId,
              u.username AS username,
              u.created_at AS createdAt,
              u.app_language AS appLanguage,
              u.level AS level,
              u.app_version AS appVersion,
              u.premium AS premium,
              u.premium_expiration AS premiumExpiration,
              u.deleted AS deleted
       FROM hm.user_profile u
       WHERE u.id IN :ids
       """, nativeQuery = true)
    List<UserStatisticsBasicDO> findBasicStatisticsDataByIdIn(@Param("ids") List<UUID> ids);

    @Query(value = "SELECT  u.sso_id AS ssoId FROM hm.user_profile u WHERE u.id IN :ids ", nativeQuery = true)
    List<String> findSSoIdUserByIdIn(@Param("ids") List<UUID> ids);

    @Query(value = "SELECT cast(u.id as varchar) FROM hm.user_profile u WHERE u.username = :username", nativeQuery = true)
    String findIdByUsername(@Param("username") String username);;

}
