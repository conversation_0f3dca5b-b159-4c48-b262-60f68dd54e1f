databaseChangeLog:
  - changeSet:
      author: system
      id: 177
      labels: refactor
      comment: Refactor user_subscription table to support multiple subscriptions per user
      changes:
        # Add purchase_date column to track when subscription was purchased
        - addColumn:
            catalogName: handball_manager
            schemaName: hm
            tableName: user_subscription
            columns:
              - column:
                  name: purchase_date
                  type: timestamp
                  constraints:
                    nullable: false
                  defaultValueComputed: CURRENT_TIMESTAMP
        
        # Rename last_payment_amount to payment_amount
        - renameColumn:
            catalogName: handball_manager
            schemaName: hm
            tableName: user_subscription
            oldColumnName: last_payment_amount
            newColumnName: payment_amount
            columnDataType: decimal(10,2)
        
        # Rename last_payment_date to payment_date
        - renameColumn:
            catalogName: handball_manager
            schemaName: hm
            tableName: user_subscription
            oldColumnName: last_payment_date
            newColumnName: payment_date
            columnDataType: timestamp
        
        # Rename last_payment_id to payment_id
        - renameColumn:
            catalogName: handball_manager
            schemaName: hm
            tableName: user_subscription
            oldColumnName: last_payment_id
            newColumnName: payment_id
            columnDataType: varchar(255)
        
        # Remove subscription_period column as it's not used in the entity
        - dropColumn:
            catalogName: handball_manager
            schemaName: hm
            tableName: user_subscription
            columnName: subscription_period
        
        # Update existing records to set purchase_date to created_at for historical data
        - sql:
            sql: UPDATE hm.user_subscription SET purchase_date = created_at WHERE purchase_date IS NULL;
