package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.UUID;

/**
 * Entity for storing user login information
 * Tracks the last login date for each user
 */
@Table(name = "user_login", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@Entity
@ToString
@NoArgsConstructor
@SQLDelete(sql = "UPDATE hm.user_login SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmUserLogin extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", referencedColumnName = "id", nullable = false, updatable = false)
    private HmUserProfile user;

    @NotNull
    @Column(name = "last_login")
    private LocalDate lastLogin;

    /**
     * Constructor with required fields
     *
     * @param user User profile
     * @param lastLogin Last login date
     */
    public HmUserLogin(HmUserProfile user, LocalDate lastLogin) {
        this.user = user;
        this.lastLogin = lastLogin;
    }
}
