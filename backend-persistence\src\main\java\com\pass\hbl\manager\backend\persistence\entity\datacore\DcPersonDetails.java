package com.pass.hbl.manager.backend.persistence.entity.datacore;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DcPersonDetails {

    private String personId;

    private String dob;

    private String nationality;

    private String gender;

    private String nameGivenLatin;

    private String nameGivenLocal;

    private String nameFamilyLatin;

    private String nameFamilyLocal;

    private String nameFullLocal;

    private List<DcImages> images;


    private Map<String, Object> additionalDetails;
}
