package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserPurchaseNotification;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public interface HmUserPurchaseNotificationRepository extends PagingAndSortingRepository<HmUserPurchaseNotification, UUID> {

    boolean existsByUserId(UUID uuid);

    @Modifying
    @Query("update HmUserPurchaseNotification p set p.token = :token, p.notificationType = :notificationType, p.expiryDate = :expiryDate, p.modifiedAt = CURRENT_TIMESTAMP where p.user.id = :userId")
    int updatePurchaseInfoByUserId(@Param("token") String token, @Param("notificationType") int notificationType,
                        @Param("expiryDate") LocalDateTime expiryDate, @Param("userId") UUID userId);

    @Query("select p.token from HmUserPurchaseNotification p where p.user.id = :userId and p.deleted = false")
    List<String> findTokenByUserId(@Param("userId") UUID userId);

    void deleteByUserId(@Param("userId") UUID userId);
}
