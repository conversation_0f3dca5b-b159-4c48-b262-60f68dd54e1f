package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.dto.admin.LogMessageDto;
import com.pass.hbl.manager.backend.persistence.dto.admin.LogMessageLevel;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.core.converters.models.PageableAsQueryParam;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.security.RolesAllowed;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping(ApiConstants.LOG_API)
@Validated
@Tag(name = "log")
@RolesAllowed({ApiConstants.ROLE_ADMIN})
public class LogMessageController extends AbstractController {

    private final LogMessageService service;

    public LogMessageController(ApplicationEventPublisher eventPublisher, LogMessageService service) {
        super(eventPublisher);
        this.service = service;
    }

    @Operation(description = "Get filtered log entries.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = LogMessageDto.class)))})
    })
    @GetMapping(value = "{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public LogMessageDto get(
            @Parameter(name = "id", description = "Log message id")
            @PathVariable(name = "id")
                    String id) throws EntityNotExistException, FormatException {
        return service.getByIdAsDto(id);
    }


    @Operation(description = "Get filtered log entries.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = LogMessageDto.class)))})
    })
    @GetMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    @PageableAsQueryParam
    public List<LogMessageDto> get(
                    @Parameter(name = "processName", description = "Process name")
                    @RequestParam(name = "processName", required = false)
                    String processName,
                    @Parameter(name = "level", description = "Log level")
                    @RequestParam(name = "level", required = false)
                    LogMessageLevel level,
                    @Parameter(name = "start", description = "Starting timestamp")
                    @RequestParam(name = "start", required = false)
                    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
                    LocalDateTime start,
                    @Parameter(name = "end", description = "End timestamp")
                    @RequestParam(name = "end", required = false)
                    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
                    LocalDateTime end,
                    @Parameter(hidden=true) Pageable pageable,
                    UriComponentsBuilder uriBuilder,
                    HttpServletRequest request,
                    HttpServletResponse response) {
        return getListFromPageableResult(this, uriBuilder, request, response, service.get(processName, level, start, end, pageable));
    }



    @Operation(description = "Get list of available log levels")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "ok", content = { @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, array = @ArraySchema(schema = @Schema(implementation = String.class)))})
    })
    @GetMapping(value = "/levels", produces = MediaType.APPLICATION_JSON_VALUE)
    public String[] getLogLevelValues() throws InvalidOperationException {
        return getEnumValues(LogMessageLevel.class);
    }
}
