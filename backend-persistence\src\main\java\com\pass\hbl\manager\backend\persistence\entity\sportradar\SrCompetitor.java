package com.pass.hbl.manager.backend.persistence.entity.sportradar;

import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Table(name = "competitor", schema = "sportradar", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class SrCompetitor extends AbstractSportradarEntity {

    @NotNull
    @Column(name = "name")
    private String name;

    @NotNull
    @Column(name = "short_name")
    private String shortName;

    @NotNull
    @Column(name = "abbreviation")
    private String abbreviation;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "season_id")
    private SrSeason season;

    public String getHblImageId() {
        return getId() == null ? null : getId().toLowerCase().replaceAll("[a-z:]*:", "");
    }

    @Override
    public ExternalEntity getExternalEntity() {
        return ExternalEntity.COMPETITOR;
    }
}
