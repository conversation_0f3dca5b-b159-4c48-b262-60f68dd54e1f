package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.SessionAttribute;
import com.pass.hbl.manager.backend.persistence.entity.AbstractPictureEntity;
import com.pass.hbl.manager.backend.persistence.entity.hm.converter.MapConverter;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.pass.hbl.manager.backend.persistence.util.Constants.*;

@Table(name = "user_profile", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@ToString(callSuper = true)
@Entity
@SQLDelete(sql = "UPDATE hm.user_profile SET deleted = true, deleted_at = now() WHERE id=? and version=?")
@Where(clause = "deleted=false")
public class HmUserProfile extends AbstractPictureEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @Size(max = 42)
    @Column(name = "sso_id")
    private String ssoId;

    @NotBlank
    @Size(max = 256)
    @Column(name = "username")
    private String username;

    @Size(max = 256)
    @Column(name = "last_name")
    private String lastName;

    @Size(max = 256)
    @Column(name = "first_name")
    private String firstName;

    @NotBlank
    @Size(max = 256)
    @Column(name = "email_address")
    private String emailAddress;

    @ToString.Exclude
    @Column(name = "session_attributes")
    @Convert(converter = MapConverter.class)
    private Map<SessionAttribute, String> sessionAttributes;

    @ToString.Exclude
    @Column(name = "email_validated")
    private boolean emailValidated;

    @ToString.Exclude
    @NotNull
    @Column(name = "registered_since")
    private LocalDateTime registeredSince;

    @ToString.Exclude
    @Column(name = "premium")
    private boolean premium;

    @ToString.Exclude
    @NotNull
    @Min(0)
    @Column(name = "level")
    private int level;

    @ToString.Exclude
    @Column(name = "experience_points")
    private Integer experiencePoints;

    @ToString.Exclude
    @Column(name = "premium_expiration")
    private LocalDateTime premiumExpiration;

    @ToString.Exclude
    @OneToMany(mappedBy = "userProfile", fetch = FetchType.LAZY)
    List<HmLeagueMembership> leagueMemberships;

    @ToString.Exclude
    @OneToMany(mappedBy = "owner", fetch = FetchType.LAZY)
    List<HmTeam> teams;

    @ToString.Exclude
    @OneToMany(mappedBy = "owner", fetch = FetchType.LAZY)
    List<HmLeague> ownedLeagues;

    @ToString.Exclude
    @OneToMany(mappedBy = "owner", fetch = FetchType.LAZY)
    List<HmTransferMarket> transferMarketAppearances;

    @ToString.Exclude
    @OneToMany(mappedBy = "bidder", fetch = FetchType.LAZY)
    List<HmTransferMarketBid> transferMarketBids;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    List<HmUserAward> allAwards;

    /**
     * level at closing of the current round
     */
    @ToString.Exclude
    @Column(name = "level_backup")
    private Integer levelBackup;

    /**
     * experience points at closing of the current round
     */
    @ToString.Exclude
    @Column(name = "experience_points_backup")
    private Integer experiencePointsBackup;

    //Only the persistence provider can increment or update
    @Version
    @Column(name = "version")
    private Integer version;

    @ToString.Exclude
    @Column(name = "app_version")
    private String appVersion;

    @ToString.Exclude
    @Column(name = "manual_subscription")
    private boolean isManualSubscription;

    @ToString.Exclude
    @Column(name = "app_language")
    private String appLanguage;


    public HmUserProfile(String username, String emailAddress) {
        this.id = UUID.randomUUID();
        this.username = username;
        this.emailAddress = emailAddress;
        this.registeredSince = LocalDateTime.now();
        this.experiencePoints = 0;
        this.level = 1;
        this.ssoId = NOT_ASSIGNED;
        this.levelBackup = null;
        this.experiencePointsBackup = null;
        this.appVersion = DEFAULT_APP_VERSION;
        this.isManualSubscription = false;
        this.appLanguage = DEFAULT_APP_LANGUAGE;
    }

    public HmUserProfile(String ssoId, String username, String firstName, String lastName, String emailAddress, boolean emailValidated, UUID picture, String appVersion) {
        this.ssoId = ssoId;
        this.username = username;
        this.lastName = lastName;
        this.firstName = firstName;
        this.emailAddress = emailAddress;
        this.emailValidated = emailValidated;
        this.registeredSince = LocalDateTime.now();
        this.experiencePoints = 0;
        this.level = 1;
        this.premium = false;
        this.setPicture(picture);
        this.levelBackup = null;
        this.experiencePointsBackup = null;
        this.appVersion = appVersion;
        this.isManualSubscription = false;
        this.appLanguage = DEFAULT_APP_LANGUAGE;
    }

    public String getSessionAttribute(SessionAttribute key) {
        return getSessionAttribute(key, null);
    }

    public String getSessionAttribute(SessionAttribute key, String defaultValue) {
        if (sessionAttributes == null) {
            sessionAttributes = new HashMap<>();
        }
        if (!sessionAttributes.containsKey(key)) {
            setSessionAttribute(key, defaultValue);
        }
        return sessionAttributes.get(key);
    }

    public void setSessionAttribute(SessionAttribute key, Object value) {
        if (sessionAttributes == null) {
            sessionAttributes = new HashMap<>();
        }
        sessionAttributes.put(key, value == null ? null : String.valueOf(value));
    }

    public boolean isPremium() {
        LocalDateTime registeredSince = this.registeredSince;
        LocalDate trialSubscriptionUntil;
        if (registeredSince.toLocalDate().isAfter(TRIAL_SUBSCRIPTION_REFERENCE_DATE)) {
            trialSubscriptionUntil = registeredSince.toLocalDate().plusDays(TRIAL_SUBSCRIPTION_30_DAYS);
        } else {
            trialSubscriptionUntil = registeredSince.toLocalDate().plusDays(TRIAL_SUBSCRIPTION_60_DAYS);
        }
        LocalDate today = LocalDateTime.now().toLocalDate();
        if (trialSubscriptionUntil.isBefore(today)) {
            return premium;
        } else {
            return true;
        }
    }
}
