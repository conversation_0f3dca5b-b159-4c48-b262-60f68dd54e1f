package com.pass.hbl.manager.backend.persistence.entity.datacore;

import com.pass.hbl.manager.backend.persistence.domain.datacore.DataSourceStatus;
import com.pass.hbl.manager.backend.persistence.domain.datacore.FixtureStatus;
import com.pass.hbl.manager.backend.persistence.dto.hm.LastGoal;
import com.pass.hbl.manager.backend.persistence.dto.hm.MatchStatus;
import com.pass.hbl.manager.backend.persistence.dto.hm.Winner;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrAbstractMatch;
import com.pass.hbl.manager.backend.persistence.exception.MappingException;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static javax.persistence.EnumType.STRING;

@Table(name = "live_match", schema = "datacore", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class DcLiveMatch extends SrAbstractMatch {

    /*@Column(name = "match_date")
    private LocalDateTime matchDate;
    */
    @Column(name = "team1_id")
    private String team1Id;

    @Column(name = "team2_id")
    private String team2Id;

    @Enumerated(STRING)
    @Column(name = "status")
    private FixtureStatus status;

    @Enumerated(EnumType.STRING)
    @NotNull
    @Column(name = "winner")
    private Winner winner;

    @Column(name = "team1_current_score")
    private Integer team1CurrentScore;

    @Column(name = "team2_current_score")
    private Integer team2CurrentScore;

    @Column(name = "event_source_status")
    private DataSourceStatus eventSourceStatus;

    @Column(name = "scoreboard_source_status")
    private DataSourceStatus scoreboardSourceStatus;

    /*@Column(name = "team1_halftime_score")
    private Integer team1HalftimeScore;

    @Column(name = "team2_halftime_score")
    private Integer team2HalftimeScore;

    @Column(name = "team1_final_score")
    private Integer team1FinalScore;

    @Column(name = "team2_final_score")
    private Integer team2FinalScore;

    @Column(name = "current_period_start")
    private LocalDateTime currentPeriodStart;
     */

    @Column(name = "clock")
    private String clock;

    @Column(name = "running")
    private boolean running;

    @Column(name = "time")
    private LocalDateTime time;

    /*@Column(name = "last_goal_time")
    private LocalDateTime lastGoalTime;

    @Column(name = "last_goal_match_time")
    private String lastGoalMatchTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "last_goal_team")
    private LastGoal lastGoalTeam;

    @Column(name = "venue")
    private String venue;*/

    private transient Integer round;

    private transient String seasonId;


    /*@Override
    public LocalDateTime getStartTime() {
        return matchDate;
    }*/

    @Override
    public String getHomeClubId() {
        return team1Id;
    }

    @Override
    public String getAwayClubId() {
        return team2Id;
    }

    @Override
    public Integer getHomeScore() {
        return team1CurrentScore;
    }

    @Override
    public Integer getAwayScore() {
        return team2CurrentScore;
    }

    /*@Override
    public Integer getHalfTimeHomeScore() {
        return team1HalftimeScore;
    }

    @Override
    public Integer getHalfTimeAwayScore() {
        return team2HalftimeScore;
    }*/

    @Override
    public MatchStatus getMatchStatus() throws MappingException {
        return MatchStatus.getByDataCoreFixtureStatus(status);
    }

    @Override
    public String getMatchTime() {
        return time.toString();
    }

    @Override
    public Integer getHblRound() {
        return round;
    }
}
