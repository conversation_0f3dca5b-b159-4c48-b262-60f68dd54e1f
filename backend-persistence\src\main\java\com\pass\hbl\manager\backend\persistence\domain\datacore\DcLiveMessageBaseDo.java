package com.pass.hbl.manager.backend.persistence.domain.datacore;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/*
 Represents a default live message received from MQTT topic including compressed data payload
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class DcLiveMessageBaseDo {

    private String type;

    private String fixtureId;

    private LocalDateTime timestamp;

    @NotNull
    private int periodId;

    @NotNull
    private String compressedData;

    // Constructors, getters, and setters

    @JsonCreator
    public DcLiveMessageBaseDo() {
        // Default constructor
    }

    @Override
    public String toString() {
        return "DcLiveMessageBaseDo{" +
                "type='" + type + '\'' +
                ", fixtureId='" + fixtureId + '\'' +
                ", timestamp=" + timestamp +
                ", periodId=" + periodId +
                ", compressedData='" + compressedData + '\'' +
                '}';
    }
}

