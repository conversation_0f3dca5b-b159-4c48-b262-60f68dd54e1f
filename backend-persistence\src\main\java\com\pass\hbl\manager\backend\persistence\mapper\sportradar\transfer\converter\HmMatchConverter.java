package com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminExternalDataMapping;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmMatch;
import com.pass.hbl.manager.backend.persistence.repository.admin.AdminExternalDataMappingRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmMatchRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ExternalDataMappingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Component
@Slf4j
public class HmMatchConverter extends AbstractConverter<String, HmMatch> {

    private final ExternalDataMappingService externalDataMappingService;

    private final HmMatchRepository matchRepository;

    private final Map<String, HmMatch> cache;

    public HmMatchConverter(ExternalDataMappingService externalDataMappingService, HmMatchRepository matchRepository) {
        this.externalDataMappingService = externalDataMappingService;
        this.matchRepository = matchRepository;
        this.cache = new HashMap<>();
    }

    public void resetCache() {
        this.cache.clear();
    }

    @Override
    protected HmMatch convert(String source) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        if (cache.containsKey(source)) {
            return cache.get(source);
        }

        Optional<HmMatch> match = externalDataMappingService.get(Datasource.SPORTRADAR, ExternalEntity.MATCH, source)
                .map(AdminExternalDataMapping::getHmId)
                .map(matchRepository::findById)
                .filter(Optional::isPresent)
                .map(Optional::get);
        if (match.isEmpty()) {
            log.warn("Unknown match with SrId=" + source);
            return null;
        }
        cache.put(source, match.get());
        return match.get();
    }
}
