package com.pass.hbl.manager.backend.persistence.dto.admin;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Data transfer object for reduced transfer market object used for admin api
 */
public record TransferMarketAdminDto(UUID id, UUID leagueId, UUID ownerId, UUID playerId, int price, LocalDateTime auctionEnd, LocalDateTime createdAt, LocalDateTime deletedAt, List<TransferMarketBidAdminDto> bids) {
}
