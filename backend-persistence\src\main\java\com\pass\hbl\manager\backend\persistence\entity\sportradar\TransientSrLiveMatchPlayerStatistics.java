package com.pass.hbl.manager.backend.persistence.entity.sportradar;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.tuple.Pair;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class TransientSrLiveMatchPlayerStatistics extends SrAbstractPlayerStatistics {

    private String matchSrId;
    private String playerSrId;

    // sr type 5
    private Integer secondsPlayed;

    // sr type 22
    private Integer assistsNumber;

    // sr type 6
    private Integer throwsNumber;

    // sr type 7
    private Integer goalsThrown;

    // sr type 52
    private Integer goalsSaved;

    // sr type 9
    private Float throwRate;

    // sr type 51
    private Integer goalsConceded;

    private TransientSrLiveMatchPlayerStatistics(String matchSrId, String playerSrId) {
        this.matchSrId = matchSrId;
        this.playerSrId = playerSrId;
    }

    public static TransientSrLiveMatchPlayerStatistics of(@NotNull @NotEmpty List<SrLiveMatchPlayerStatistics> stats) {
        List<Pair<String, String>> ids = stats.stream().map(s -> Pair.of(s.getMatchId(), s.getPlayerId())).distinct().toList();
        if (ids.size() != 1) {
            throw new IllegalArgumentException("Cannot create instance with mixed stats. List must be unique regarding matchId and playerId");
        }

        TransientSrLiveMatchPlayerStatistics stat = new TransientSrLiveMatchPlayerStatistics(ids.get(0).getKey(), ids.get(0).getValue());

        stats.forEach(s -> {
            switch (s.getType()) {
                case 5 -> stat.secondsPlayed = s.getValue().intValue();
                case 22 -> stat.assistsNumber = s.getValue().intValue();
                case 6 -> stat.throwsNumber = s.getValue().intValue();
                case 7 -> stat.goalsThrown = s.getValue().intValue();
                case 9 -> stat.throwRate = s.getValue().floatValue();
                case 52 -> stat.goalsSaved = s.getValue().intValue();
                case 51 -> stat.goalsConceded = s.getValue().intValue();
            }
        });

        return stat;
    }
}
