package com.pass.hbl.manager.backend.persistence.domain.datacore;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@NoArgsConstructor
public class DataObject {

    @NotNull
    private Sources sources;

    @NotNull
    private Status status;

    @NotNull
    private Map<String, Entity> entities;

    @NotNull
    private Clock clock;

    @NotNull
    private LocalDateTime timestamp;

    // Getters and setters
}
