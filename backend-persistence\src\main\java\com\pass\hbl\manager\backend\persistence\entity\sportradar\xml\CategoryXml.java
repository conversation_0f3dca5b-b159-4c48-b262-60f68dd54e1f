package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "Category")
@NoArgsConstructor
@Data
public class CategoryXml {

    @JacksonXmlProperty(localName = "BetradarCategoryId", isAttribute = true)
    private Integer betradarCategoryId;

    @JacksonXmlProperty(localName = "Name")
    private List<NameXml> names;

    @JacksonXmlProperty(localName = "Tournament")
    private List<TournamentXml> tournaments;
}
