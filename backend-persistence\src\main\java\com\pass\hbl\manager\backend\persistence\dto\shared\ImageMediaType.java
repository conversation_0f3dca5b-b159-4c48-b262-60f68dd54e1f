package com.pass.hbl.manager.backend.persistence.dto.shared;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;

import javax.validation.constraints.NotNull;
import java.util.Arrays;

@Schema(description = "Media type of the image")
public enum ImageMediaType {

    GIF, JPEG, PNG;

    public String getContentType() {
        return "image/" + this.name().toLowerCase();
    }

    public String getFormat() {
        return this.name().toUpperCase();
    }

    public static ImageMediaType findByString(@NotNull String s, ImageMediaType elseValue) {
        if (StringUtils.isEmpty(s)) {
            throw new NullPointerException();
        }

        return Arrays.stream(values())
                .filter(type -> StringUtils.equalsIgnoreCase(type.toString(), s))
                .findAny().orElse(elseValue);
    }

    public static ImageMediaType getByContentType(@NotNull String contentType) {

        if (StringUtils.isEmpty(contentType)) {
            throw new NullPointerException();
        }

        return Arrays.stream(values())
                .filter(type -> StringUtils.equalsIgnoreCase(type.getContentType(), contentType))
                .findAny().orElse(null);
    }

    public @NotNull MediaType getMediaType() {
        switch (this) {
            case GIF -> {
                return MediaType.IMAGE_GIF;
            }
            case JPEG -> {
                return MediaType.IMAGE_JPEG;
            }
            case PNG -> {
                return MediaType.IMAGE_PNG;
            }
        }
        throw new NullPointerException("image type is not set");
    }
}
