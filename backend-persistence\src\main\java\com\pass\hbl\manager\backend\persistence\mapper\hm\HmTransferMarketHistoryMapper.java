package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.BidDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerLobbyDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.TransferMarketHistoryDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.UserDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmTransferMarket;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmTransferMarketBid;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.modelmapper.AbstractConverter;
import org.modelmapper.Condition;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


@Component
@Slf4j
public class HmTransferMarketHistoryMapper extends AbstractMapper<HmTransferMarket, TransferMarketHistoryDto> {

    private final HmUserMapper userMapper;
    private final HmBidMapper bidMapper;

    public HmTransferMarketHistoryMapper(HmUserMapper userMapper, HmBidMapper bidMapper) {
        super(HmTransferMarket.class, TransferMarketHistoryDto.class);
        this.userMapper = userMapper;
        this.bidMapper = bidMapper;
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected void customizeInit() {

        getModelMapper().getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);

        TypeMap<HmTransferMarket, TransferMarketHistoryDto> e2d = getModelMapper().createTypeMap(HmTransferMarket.class, TransferMarketHistoryDto.class);

        Condition<HmTransferMarket, TransferMarketHistoryDto> conditionOwnerIsNotDeleted = context -> {
            try {
                //noinspection ResultOfMethodCallIgnored
                context.getSource().getOwner().getId().toString();
                return true;
            } catch (Exception e) {
                return false;
            }
        };

        Converter<HmPlayer, PlayerLobbyDto> playerConverter = new AbstractConverter<>() {
            @Override
            protected PlayerLobbyDto convert(HmPlayer source) {
                String picture = Objects.nonNull(source.getPicture()) ? source.getPicture().toString() : null;
                return new PlayerLobbyDto(source.getId().toString(), source.getFirstName(), source.getLastName(), source.getPosition(), source.getMarketValue(), picture);
            }
        };

        Converter<HmTransferMarket, UserDto> userConverter = new AbstractConverter<>() {
            @Override
            protected UserDto convert(HmTransferMarket source) {
                try {
                    return source == null ? null : userMapper.mapToDto(source.getOwner());
                } catch (Exception e) {
                    return UserDto.getAnonymous();
                }
            }
        };
        Converter<List<HmTransferMarketBid>, List<BidDto>> bidConverter = new AbstractConverter<>() {
            @Override
            protected List<BidDto> convert(List<HmTransferMarketBid> source) {
                if (CollectionUtils.isEmpty(source)) {
                    return Collections.emptyList();
                }
                return source.stream().map(bidMapper::mapToDto).toList();
            }
        };

        e2d.addMappings(mapper -> mapper.map(HmTransferMarket::getId, TransferMarketHistoryDto::setId));
        e2d.addMappings(mapper -> mapper.when(conditionOwnerIsNotDeleted).using(userConverter).map(HmTransferMarket::self, TransferMarketHistoryDto::setOwner));
        e2d.addMappings(mapper -> mapper.using(playerConverter).map(HmTransferMarket::getPlayer, TransferMarketHistoryDto::setPlayer));
        e2d.addMappings(mapper -> mapper.map(HmTransferMarket::getPrice, TransferMarketHistoryDto::setPrice));
        e2d.addMappings(mapper -> mapper.using(bidConverter).map(HmTransferMarket::getBids, TransferMarketHistoryDto::setBids));
    }

    @Override
    protected TransferMarketHistoryDto customizeMapToDto(TransferMarketHistoryDto transferMarketHistoryDto, HmTransferMarket transferMarket) {
        if (transferMarketHistoryDto.getOwner() == null) {
            transferMarketHistoryDto.setOwner(UserDto.getAnonymous());
        }
        return super.customizeMapToDto(transferMarketHistoryDto, transferMarket);
    }
}
