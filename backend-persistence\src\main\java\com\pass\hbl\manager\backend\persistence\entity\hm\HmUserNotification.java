package com.pass.hbl.manager.backend.persistence.entity.hm;


import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.UUID;

@Table(name = "user_notification", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@Entity
@NoArgsConstructor
@ToString
@SQLDelete(sql = "UPDATE hm.user_notification SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmUserNotification extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", referencedColumnName = "id", updatable = false)
    private HmUserProfile user;

    @NotBlank
    @Size(max = 1024)
    @Column(name = "title")
    private String title;

    @NotBlank
    @Size(max = 1024)
    @Column(name = "body")
    private String body;

    @Size(max = 4096)
    @Column(name = "data")
    private String data;

    @Column(name = "is_unread")
    private boolean isUnread;

    @Column(name = "language")
    private String language;

    public HmUserNotification(HmUserProfile user, String title, String body, String data) {
        this.user = user;
        this.title = title;
        this.body = body;
        this.data = data;
        this.isUnread = true;
        this.language = null;
    }

    public HmUserNotification(HmUserProfile user, String title, String body, String data, String language) {
        this.user = user;
        this.title = title;
        this.body = body;
        this.data = data;
        this.isUnread = true;
        this.language = language;
    }
}
