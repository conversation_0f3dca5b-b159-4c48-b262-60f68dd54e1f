package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractPictureEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.UUID;

@Table(name = "club", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@Entity
@ToString
@SQLDelete(sql = "UPDATE hm.club SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmClub extends AbstractPictureEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;
    @Column(name = "hbl_image_id")
    private String hblImageId;

    @Size(max = 64)
    @NotBlank
    @Column(name = "name")
    private String name;

    @Size(max = 8)
    @NotBlank
    @Column(name = "abbreviation")
    private String abbreviation;

    @Column(name = "active")
    private boolean active;

    @ToString.Exclude
    @OneToMany(mappedBy = "club", fetch = FetchType.LAZY)
    @Where(clause = "joined < now() and (`left` is null or `left` > now())")
    private List<HmPlayerInClub> currentPlayers;

    @ToString.Exclude
    @OneToMany(mappedBy = "club", fetch = FetchType.LAZY)
    private List<HmPlayerInClub> historicalPlayers;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "challenger")
    private List<HmPlayerRoundStatistics> statistics;
}
