package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.sportradar.handball.v2.model.SportEvent;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

@Component
public class SportEventRoundConverter extends AbstractConverter<SportEvent, Integer> {
    @Override
    protected Integer convert(SportEvent source) {
        if (source.getSportEventContext() == null || source.getSportEventContext().getRound() == null) {
            return null;
        }
        return source.getSportEventContext().getRound().getNumber();
    }
}
