package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;

@JsonRootName("Round")
@NoArgsConstructor
@Getter
@Setter
@Schema(description = "Reduced round info object")
public class RoundDto extends AbstractDto<RoundDto, String> {

    @Schema(description = "Round id is registered with", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "name round is registered with", example = "john-doe", required = true)
    @NotBlank
    @Size(max = 16)
    private String name;

    @Schema(description = "start date of the round", example = "2021-09-08T00:00:00", required = true)
    @NotNull
    private ZonedDateTime from;

    @Schema(description = "end date of the round", example = "2022-06-12T23:59:59", required = true)
    @NotNull
    private ZonedDateTime to;

    @Schema(description = "closing timestamp of the round", example = "2022-06-11T23:59:59", required = true)
    @NotNull
    private ZonedDateTime closing;

    @Schema(description = "number of the round in season", required = true)
    private int roundNumber;

    @Schema(description = "indicates whether the round is special round", required = true)
    private boolean specialRound;

    @Schema(description = "display order of the special or regular round in season ordered by start date", required = true)
    private int roundOrder;

    public RoundDto(int roundNumber, boolean specialRound) {
        this.roundNumber = roundNumber;
        this.specialRound = specialRound;
    }
}
