package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.SubscriptionStatisticsBasicDO;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserSubscription;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public interface HmUserSubscriptionRepository extends PagingAndSortingRepository<HmUserSubscription, UUID> {

    @Modifying
    @Query("UPDATE HmUserSubscription s SET s.paymentAmount = :amount, s.paymentDate = :date, s.paymentId = :paymentId, s.modifiedAt = CURRENT_TIMESTAMP WHERE s.id = :id")
    int updatePaymentInfo(@Param("id") UUID id, @Param("amount") BigDecimal amount, @Param("date") LocalDateTime date, @Param("paymentId") String paymentId);

    @Query(value = """
       SELECT CAST(s.id AS VARCHAR)
       FROM hm.user_subscription s
       LEFT OUTER JOIN hm.user_profile u ON s.user_id = u.id
       WHERE s.deleted = false
       AND u.deleted = false
       AND s.modified_at >= CAST(:changedAfter AS TIMESTAMP)
       ORDER BY s.modified_at DESC
       """, nativeQuery = true)
    List<String> findSubscriptionStatisticsIds(@Param("changedAfter") LocalDateTime changedAfter);

    @Query(value = """
       SELECT CAST(s.id AS VARCHAR)
       FROM hm.user_subscription s
       LEFT OUTER JOIN hm.user_profile u ON s.user_id = u.id
       WHERE s.deleted = false
       AND u.deleted = false
       ORDER BY s.modified_at DESC
       """, nativeQuery = true)
    List<String> findAllSubscriptionStatisticsIds();

    @Query(value = """
       SELECT cast(s.id as varchar) AS id,
              u.sso_id AS userSsoId,
              s.subscription_id AS subscriptionId,
              s.start_date AS startDate,
              s.end_date AS endDate,
              s.subscription_type AS subscriptionType,
              s.platform AS platform,
              s.payment_id AS lastPaymentId,
              s.payment_amount AS lastPaymentAmount,
              s.payment_date AS lastPaymentDate
       FROM hm.user_subscription s
       LEFT OUTER JOIN hm.user_profile u ON s.user_id = u.id
       WHERE s.deleted = false
       AND u.deleted = false
       AND s.id IN :ids
       ORDER BY s.modified_at DESC
       """, nativeQuery = true)
    List<SubscriptionStatisticsBasicDO> findSubscriptionStatisticsByIdIn(@Param("ids") List<UUID> ids);

    @Modifying
    @Query("UPDATE HmUserSubscription s SET s.endDate = :endDate, s.modifiedAt = CURRENT_TIMESTAMP WHERE s.id = :id")
    int updateEndDate(@Param("id") UUID id, @Param("endDate") LocalDateTime endDate);
}