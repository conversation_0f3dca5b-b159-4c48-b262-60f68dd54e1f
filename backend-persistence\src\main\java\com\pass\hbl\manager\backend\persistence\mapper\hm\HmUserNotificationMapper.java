package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pass.hbl.manager.backend.persistence.dto.hm.UserAwardDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.UserNotificationDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserAward;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserNotification;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.HmEntityToStringConverter;
import com.pass.hbl.manager.backend.persistence.util.Util;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.util.Map;


@Component
public class HmUserNotificationMapper extends AbstractMapper<HmUserNotification, UserNotificationDto> {

    public HmUserNotificationMapper() {
        super(HmUserNotification.class, UserNotificationDto.class);
    }

    @Override
    protected void customizeInit() {
        TypeMap<HmUserNotification, UserNotificationDto> e2d
                = getOrCreateTypeMap(HmUserNotification.class, UserNotificationDto.class);
        Converter<String, Map<String, String>> jsonToMapConverter = ctx -> {
            try {
                ObjectMapper mapper = new ObjectMapper();
                Map<String, String> map = mapper.readValue(ctx.getSource(), Map.class);
                return ctx.getSource() == null ? null : map;
            } catch (Exception e) {
                return null;
            }
        };
        e2d.addMappings(mapper -> mapper.using(new HmEntityToStringConverter<HmUserNotification>())
                .map(HmUserNotification::getUser, UserNotificationDto::setUserId));
        e2d.addMappings(mapper -> mapper.map(HmUserNotification::getCreatedAt, UserNotificationDto::setReceivedAt));
        e2d.addMappings(mapper -> mapper.using(jsonToMapConverter).map(HmUserNotification::getData, UserNotificationDto::setData));
    }
}