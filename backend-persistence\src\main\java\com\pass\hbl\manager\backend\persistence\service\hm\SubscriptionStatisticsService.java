package com.pass.hbl.manager.backend.persistence.service.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.SubscriptionStatisticsDto;
import com.pass.hbl.manager.backend.persistence.exception.RateLimitExceededException;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.SubscriptionStatisticsHandler;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for subscription statistics operations
 * Delegates to SubscriptionStatisticsHandler for business logic
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class SubscriptionStatisticsService {

    private final SubscriptionStatisticsHandler subscriptionStatisticsHandler;

    @SneakyThrows
    @Transactional(readOnly = true)
    public List<SubscriptionStatisticsDto> getAllSubscriptionStatistics(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws RateLimitExceededException {
        return subscriptionStatisticsHandler.getAllSubscriptionStatistics(changedAfter, pageable, requestUrl, externalClient);
    }
}
