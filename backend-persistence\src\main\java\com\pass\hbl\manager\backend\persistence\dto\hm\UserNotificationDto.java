package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;
import java.util.Map;

@JsonRootName("UserNotification")
@Getter
@Setter
@Schema(description = "User notification")
public class UserNotificationDto extends AbstractDto<UserNotificationDto, String> {

    @Schema(description = "user notification id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "user to which the notification is assigned")
    private String userId;

    @Schema(description = "title of the notification")
    @Size(max = 1024)
    private String title;

    @Schema(description = "message of the notification")
    @Size(max = 1024)
    private String body;

    @Schema(description = "data payload of the notification")
    private Map<String, String> data;

    @Schema(description = "user notification receipt time")
    private ZonedDateTime receivedAt;

    @Schema(description = "true, is the user notification is not read")
    @JsonProperty(value = "isUnread")
    private boolean isUnread;
}
