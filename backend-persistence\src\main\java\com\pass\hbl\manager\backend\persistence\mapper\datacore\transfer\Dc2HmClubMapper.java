package com.pass.hbl.manager.backend.persistence.mapper.datacore.transfer;

import com.pass.hbl.manager.backend.persistence.entity.datacore.DcCompetitor;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmClub;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.transfer.converter.CustomDataCoreIdConverter;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.transfer.converter.DcCompetitorIdConverter;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class Dc2HmClubMapper extends AbstractDc2HmMapper<DcCompetitor, HmClub> {


    public Dc2HmClubMapper(DcCompetitorIdConverter customIdConverter) {
        super(DcCompetitor.class, HmClub.class, null, customIdConverter);
    }

    @Override
    protected HmClub customizeMapToDto(HmClub hmClub, DcCompetitor dcCompetitor, Map<String, Object> context) {
        if (hmClub == null) {
            return null;
        }
        hmClub.setActive(true);
        return hmClub;
    }
}
