package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import jdk.jfr.BooleanFlag;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

@JsonRootName("LeagueInfo")
@Getter
@Setter
@Schema(description = "Overview info of a league")
public class LeagueDto extends AbstractPictureDto<LeagueDto, String> {

    @Schema(description = "League id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "Name of the league", example = "AB-Town Heroes", required = true)
    @NotBlank
    @Size(max = 32)
    private String name;

    @Schema(description = "Owner of the league")
    @NotBlank
    private String ownerId;

    @Schema(description = "Optional description of the league")
    private String description;

    @Schema(description = "Public access")
    @BooleanFlag
    private Boolean publicAccess;

    @Schema(description = "Season id")
    @NotBlank
    private String seasonId;

    @Schema(description = "Amount of members in the league")
    @Min(0)
    private int memberCount;

    @Schema(description = "Maximum allowed members in the league")
    @Min(0)
    private int maxSize;

    @Schema(description = "Members of the league")
    private List<UserDto> members;

    @Schema(description = "Applicants of the league")
    private List<UserDto> applicants;

    @Schema(description = "User Awards related to the league", example = "league winner award")
    private List<UserAwardInfoDto> leagueAwards;

    @Schema(description = "active state of the league")
    @JsonProperty(value = "isActive")
    private boolean isActive;

    @JsonCreator
    public LeagueDto() {
    }

    public LeagueDto(String id, String name, String ownerId, String description, Boolean publicAccess, String seasonId, int memberCount, int maxSize, List<UserDto> members, List<UserDto> applicants) {
        this.id = id;
        this.name = name;
        this.ownerId = ownerId;
        this.description = description;
        this.publicAccess = publicAccess;
        this.seasonId = seasonId;
        this.memberCount = memberCount;
        this.maxSize = maxSize;
        this.members = members;
        this.applicants = applicants;
    }
}
