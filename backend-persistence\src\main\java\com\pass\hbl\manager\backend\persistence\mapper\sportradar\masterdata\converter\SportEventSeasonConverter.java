package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrSeason;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.SrSeasonMapper;
import com.sportradar.handball.v2.model.SportEvent;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

@Component
public class SportEventSeasonConverter extends AbstractConverter<SportEvent, SrSeason> {

    private final SrSeasonMapper seasonMapper;

    public SportEventSeasonConverter(SrSeasonMapper seasonMapper) {
        this.seasonMapper = seasonMapper;
    }

    @Override
    protected SrSeason convert(SportEvent source) {
        return source.getSportEventContext() == null ? null : seasonMapper.mapToEntity(source.getSportEventContext().getSeason());
    }
}
