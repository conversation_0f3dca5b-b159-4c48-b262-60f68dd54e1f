package com.pass.hbl.manager.backend.persistence.dto.admin;

import com.pass.hbl.manager.backend.persistence.dto.hm.BidStatus;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Data transfer object for reduced transfer market bid object used for admin api
 */
public record TransferMarketBidAdminDto(UUID id, String bidder, int bid, BidStatus bidStatus, LocalDateTime createdAt, LocalDateTime deletedAt) {
}
