package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@JsonRootName("ScoreDto")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Schema(description = "Player market value score by round info object")
public class ScoreDto {

    @Schema(description = "Round number", example = "1", required = true)
    private Integer roundNumber;

    @Schema(description = "Indicates if the round is a special round", example = "false")
    private Boolean specialRound;

    @Schema(description = "Score in the round", example = "5")
    private Integer score;

}