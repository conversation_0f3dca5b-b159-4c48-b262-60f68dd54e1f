package com.pass.hbl.manager.backend.persistence.exception;

import lombok.Getter;

public class TechnicalException extends CodedException {

    public enum Type {FCM, RESOURCE_NOT_FOUND, SSO_AUTHENTICATION, INVALID_RESOURCE_FILE, APPLE_IN_APP_PURCHASES, DAT<PERSON>ASE_UPDATE_FAILED, GOOGLE_IN_APP_PURCHASES}

    @Getter
    private final Type type;


    public TechnicalException(Type type, String message, String... args) {
        this(type, message, null, args);
    }

    public TechnicalException(Type type, String message, Throwable cause, String... args) {
        super(ExceptionCode.TECHNICAL, message, cause, args);
        this.type = type;
    }
}
