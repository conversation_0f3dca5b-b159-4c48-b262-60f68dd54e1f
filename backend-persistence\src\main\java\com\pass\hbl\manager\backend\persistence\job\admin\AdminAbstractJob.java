package com.pass.hbl.manager.backend.persistence.job.admin;

import com.pass.hbl.manager.backend.persistence.job.AbstractJob;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AdminAbstractJob extends AbstractJob {

    public AdminAbstractJob(ParameterService parameterService) {
        super(parameterService);
    }
}
