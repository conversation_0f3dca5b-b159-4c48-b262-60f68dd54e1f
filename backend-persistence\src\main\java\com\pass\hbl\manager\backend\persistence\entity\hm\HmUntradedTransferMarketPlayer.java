package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;


@Table(name = "untraded_transfer_market_player", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@Entity
@ToString
@SQLDelete(sql = "UPDATE hm.untraded_transfer_market_player SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmUntradedTransferMarketPlayer extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "league_id", referencedColumnName = "id")
    private HmLeague league;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "player_id", referencedColumnName = "id")
    private HmPlayer player;

    @NotNull
    @ToString.Exclude
    @Column(name = "price")
    private int price;

    public HmUntradedTransferMarketPlayer(HmLeague league, HmPlayer player, int price) {
        this.league = league;
        this.player = player;
        this.price = price;
    }
}