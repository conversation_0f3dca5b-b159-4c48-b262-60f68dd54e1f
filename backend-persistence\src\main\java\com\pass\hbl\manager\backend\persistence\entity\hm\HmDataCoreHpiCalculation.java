package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.*;
import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Table(name = "datacore_hpi_calculation", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@SQLDelete(sql = "UPDATE hm.datacore_hpi_calculation SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmDataCoreHpiCalculation extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @Column(name = "dc_event_type")
    private String dcEventType;

    @Column(name = "dc_subtype")
    private String dcSubtype;

    @Column(name = "dc_attack_type")
    private String dcAttackType;

    @Column(name = "dc_failure_reason")
    private String dcFailureReason;

    @Column(name = "dc_location")
    private String dcLocation;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "event")
    private Event event;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_characteristic")
    private EventCharacteristic eventCharacteristic;

    @Column(name = "score")
    private int score;

    @Column(name = "goalkeeper")
    private Boolean goalkeeper;

    @Column(name = "empty_net")
    private Boolean emptyNet;

    @Transient
    private String description;
}
