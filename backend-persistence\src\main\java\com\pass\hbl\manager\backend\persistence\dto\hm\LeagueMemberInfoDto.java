package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.UUID;

/**
 * Current league data transfer object with statistics information
 * Used for current leagues in user statistics
 */
@JsonRootName("CurrentLeague")
@Getter
@Setter
@Schema(description = "Current league object with user statistics")
public class LeagueMemberInfoDto {

    @Schema(description = "League id", example = "8a7b6c5d-4e3f-2a1b-0c9d-8e7f6a5b4c3d", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private UUID leagueId;

    @Schema(description = "User is the owner of leauge or not")
    private boolean isAdmin;

    @Schema(description = "User rank in the league (calculated by ordering users by score)", example = "3")
    private int rank;

    @Schema(description = "User balance in the league", example = "75000")
    private int balance;

    @Schema(description = "User score in the league", example = "1250")
    private int score;

    @JsonCreator
    public LeagueMemberInfoDto() {
    }

    public LeagueMemberInfoDto(UUID leagueId, boolean isAdmin, int rank, int balance, int score) {
        this.leagueId = leagueId;
        this.isAdmin = isAdmin;
        this.rank = rank;
        this.balance = balance;
        this.score = score;
    }
}
