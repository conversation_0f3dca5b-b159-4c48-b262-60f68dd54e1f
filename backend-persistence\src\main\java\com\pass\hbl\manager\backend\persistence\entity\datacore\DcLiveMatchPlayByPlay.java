package com.pass.hbl.manager.backend.persistence.entity.datacore;

import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.AbstractSportradarEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Table(name = "live_match_play_by_play", schema = "datacore", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class DcLiveMatchPlayByPlay extends AbstractSportradarEntity {

    @Column(name = "match_id")
    private String matchId;

    @Column(name = "player_id")
    private String playerId;

    @Column(name = "event_id")
    private String eventId;

    @Column(name = "clock")
    private String clock;

    @NotNull
    @Column(name = "event_type", nullable = false)
    private String eventType;

    @Column(name = "subtype")
    private String subtype;

    @Column(name = "attack_type")
    private String attackType;

    @Column(name = "failure_reason")
    private String failureReason;

    @Column(name = "location")
    private String location;

    @Column(name = "event_time")
    private LocalDateTime eventTime;

    @Column(name = "score_home")
    private Integer scoreHome;

    @Column(name = "score_away")
    private Integer scoreAway;

    @Column(name = "play_id")
    private String playId;

    @Column(name = "sequence")
    private Integer sequence;

    @Column(name = "status")
    private String status;

    @Column(name = "success")
    private boolean success;

    @Column(name = "time")
    private LocalDateTime time;

    @Column(name = "period_id")
    private Integer periodId;

    @Column(name = "goalkeeper_id")
    private String goalKeeperId;


    @Column(name = "empty_net")
    private Boolean emptyNet;

    public DcLiveMatchPlayByPlay(DcLiveMatchPlayByPlay original) {
        this.matchId = original.matchId;
        this.playerId = original.playerId;
        this.eventId = original.eventId;
        this.clock = original.clock;
        this.eventType = original.eventType;
        this.subtype = original.subtype;
        this.attackType = original.attackType;
        this.failureReason = original.failureReason;
        this.location = original.location;
        this.eventTime = original.eventTime;
        this.scoreHome = original.scoreHome;
        this.scoreAway = original.scoreAway;
        this.playId = original.playId;
        this.sequence = original.sequence;
        this.status = original.status;
        this.success = original.success;
        this.time = original.time;
        this.periodId = original.periodId;
        this.goalKeeperId = original.goalKeeperId;
        this.emptyNet = original.emptyNet;
    }

    @Override
    public ExternalEntity getExternalEntity() {
        return ExternalEntity.EVENT;
    }
}
