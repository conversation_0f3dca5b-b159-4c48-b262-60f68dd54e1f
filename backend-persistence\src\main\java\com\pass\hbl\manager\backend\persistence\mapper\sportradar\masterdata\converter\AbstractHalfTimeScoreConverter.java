package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.util.SportradarConstants;
import com.pass.hbl.manager.backend.persistence.util.Util;
import com.sportradar.handball.v2.model.PeriodScore;
import com.sportradar.handball.v2.model.SportEventStatus;
import org.modelmapper.AbstractConverter;

import java.util.Objects;
import java.util.stream.Stream;

public abstract class AbstractHalfTimeScoreConverter extends AbstractConverter<SportEventStatus, Integer> {

    private final String qualifier;

    protected AbstractHalfTimeScoreConverter(String qualifier) {
        this.qualifier = qualifier;
    }

    @Override
    protected Integer convert(SportEventStatus source) {

        Stream<PeriodScore> periodScoreStream = Util.toStream(source.getPeriodScores())
                .filter(p -> Objects.equals(p.getNumber(), 1));

        if (Objects.equals(qualifier, SportradarConstants.HOME)) {
            return periodScoreStream
                    .map(PeriodScore::getHomeScore)
                    .findFirst().orElse(null);
        } else {
            return periodScoreStream
                    .map(PeriodScore::getAwayScore)
                    .findFirst().orElse(null);
        }
    }
}
