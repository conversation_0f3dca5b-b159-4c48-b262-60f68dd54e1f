package com.pass.hbl.manager.backend.persistence.mapper.hm.converters;

import com.pass.hbl.manager.backend.persistence.dto.hm.AwardDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.AwardLocalizationDto;
import com.pass.hbl.manager.backend.persistence.dto.shared.EntityType;
import com.pass.hbl.manager.backend.persistence.entity.shared.SharedLocalization;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.repository.shared.SharedLocalizationRepository;
import com.pass.hbl.manager.backend.persistence.util.LocalizationConstants;
import com.pass.hbl.manager.backend.persistence.util.Util;
import org.modelmapper.Converter;
import org.modelmapper.spi.MappingContext;

import java.util.*;
import java.util.stream.Collectors;

public class UserAwardLocalizationToEntityConverter implements Converter<Map<String, AwardLocalizationDto>, List<SharedLocalization>> {

    private final SharedLocalizationRepository descriptionRepository;

    public UserAwardLocalizationToEntityConverter(SharedLocalizationRepository descriptionRepository) {
        this.descriptionRepository = descriptionRepository;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<SharedLocalization> convert(MappingContext<Map<String, AwardLocalizationDto>, List<SharedLocalization>> context) {
        AwardDto source = (AwardDto) context.getParent().getSource();
        final UUID entityId;
        try {
            entityId = source.getId() == null ? null : Util.convertId(source.getId());
        } catch (FormatException e) {
            Util.sneakyThrow(e);
            return null;
        }

        return (List<SharedLocalization>) Util.toStream(context.getSource().entrySet())
                .map(entry -> {
                    try {
                        Locale locale = Util.getLocaleByLanguageTag(entry.getKey());
                        SharedLocalization name = descriptionRepository
                                .findFirstByEntityTypeAndEntityIdAndLocaleAndKey(EntityType.AWARD, entityId, locale, LocalizationConstants.NAME)
                                .orElse(new SharedLocalization());
                        name.setValue(entry.getValue().getName());
                        SharedLocalization description = descriptionRepository
                                .findFirstByEntityTypeAndEntityIdAndLocaleAndKey(EntityType.AWARD, entityId, locale, LocalizationConstants.DESCRIPTION)
                                .orElse(new SharedLocalization());
                        description.setValue(entry.getValue().getDescription());
                        return Arrays.asList(name, description);
                    } catch (FormatException e) {
                        Util.sneakyThrow(e);
                        return Collections.emptyList();
                    }
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }
}
