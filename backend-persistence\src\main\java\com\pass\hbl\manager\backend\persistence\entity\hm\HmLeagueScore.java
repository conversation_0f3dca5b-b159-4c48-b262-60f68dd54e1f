package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Table(name = "league_score", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@Entity
@ToString
@SQLDelete(sql = "UPDATE hm.league_score SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmLeagueScore extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @ToString.Exclude
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "league_id", referencedColumnName = "id")
    private HmLeague league;

    @ManyToOne(fetch = FetchType.LAZY)
    @ToString.Exclude
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    private HmUserProfile user;

    @NotNull
    @ToString.Exclude
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "round_id", referencedColumnName = "id")
    private HmRound round;

    @Min(0)
    @Column(name = "live_score")
    private Integer liveScore;

    @Min(0)
    @Column(name = "end_of_day_score")
    private Integer endOfDayScore;
}
