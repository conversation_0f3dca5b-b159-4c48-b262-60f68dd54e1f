package com.pass.hbl.manager.backend.persistence.mapper.admin;

import com.pass.hbl.manager.backend.persistence.dto.admin.LogMessageDto;
import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminLogMessage;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

@Component
public class AdminLogMessageMapper extends AbstractMapper<AdminLogMessage, LogMessageDto> {

    public AdminLogMessageMapper() {
        super(AdminLogMessage.class, LogMessageDto.class);
    }

    @Override
    protected void customizeInit() {
        TypeMap<AdminLogMessage, LogMessageDto> e2d = getOrCreateTypeMap(AdminLogMessage.class, LogMessageDto.class);
        e2d.addMappings(mapper -> mapper.map(AbstractEntity::getCreatedAt, LogMessageDto::setTimestamp));
    }
}
