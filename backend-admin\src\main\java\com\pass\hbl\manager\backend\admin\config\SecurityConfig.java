package com.pass.hbl.manager.backend.admin.config;

import com.pass.hbl.manager.backend.admin.config.security.ApiKeyAuthenticationFilter;
import com.pass.hbl.manager.backend.admin.config.security.CustomFilter;
import com.pass.hbl.manager.backend.admin.config.security.MyBasicAuthenticationEntryPoint;
import com.pass.hbl.manager.backend.admin.config.security.SimpleCorsFilter;
import com.pass.hbl.manager.backend.admin.service.internal.UserDetailsInfoService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter;
import org.springframework.security.web.header.writers.StaticHeadersWriter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, jsr250Enabled = true)
public class SecurityConfig {

    private final UserDetailsInfoService userDetailsInfoService;

    private final MyBasicAuthenticationEntryPoint authenticationEntryPoint;

    private final HandballManagerAdminConfigurationProperties properties;

    public SecurityConfig(UserDetailsInfoService userDetailsInfoService, MyBasicAuthenticationEntryPoint authenticationEntryPoint, HandballManagerAdminConfigurationProperties properties) {
        this.userDetailsInfoService = userDetailsInfoService;
        this.authenticationEntryPoint = authenticationEntryPoint;
        this.properties = properties;
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        /*http.cors(httpSecurityCorsConfigurer -> {
            CorsConfiguration configuration = new CorsConfiguration();
            //configuration.setAllowedOrigins(List.of("https://pass-consulting.com", "http://localhost:8181"));
            configuration.setAllowedOrigins(List.of("*"));
            configuration.setAllowedMethods(List.of("*"));
            configuration.setAllowedHeaders(List.of("*"));
            configuration.setAllowCredentials(true);
            configuration.setExposedHeaders(List.of("Authorization"));
            UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
            source.registerCorsConfiguration("/**", configuration);
            httpSecurityCorsConfigurer.configurationSource(source);
        });*/
        http.cors().and().csrf().disable();
        http.addFilterBefore(new SimpleCorsFilter(), WebAsyncManagerIntegrationFilter.class);
        http.addFilterBefore(new ApiKeyAuthenticationFilter(properties), WebAsyncManagerIntegrationFilter.class);
        http.authorizeRequests()
                .anyRequest()
                .authenticated()
                .and()
                .httpBasic()
                .authenticationEntryPoint(authenticationEntryPoint);
        http.addFilterAfter(new CustomFilter(), BasicAuthenticationFilter.class);
        http.sessionManagement(sees -> sees.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
        return http.build();
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }

    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authenticationProvider = new DaoAuthenticationProvider();
        authenticationProvider.setUserDetailsService(userDetailsInfoService);
        authenticationProvider.setPasswordEncoder(passwordEncoder());
        return authenticationProvider;
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
