package com.pass.hbl.manager.backend.persistence.domain.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

/**
 * Domain object for apple notification payload v2
 */

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApplePayloadDataDo {

    @Schema(description = "The unique identifier of an app in the App Store")
    private String appAppleId;

    @Schema(description = "The bundle identifier of an app")
    private String bundleId;

    @Schema(description = "The version of the build that identifies an iteration of the bundle")
    private String bundleVersion;

    @Schema(description = "The server environment, either sandbox or production.")
    private String environment;

    @Schema(description = "Subscription renewal information signed by the App Store, in JSON Web Signature (JWS) format")
    private String signedRenewalInfo;

    @Schema(description = "Transaction information, signed by the App Store, in JSON Web Signature (JWS) format.")
    private String signedTransactionInfo;

    @JsonCreator
    public ApplePayloadDataDo() {
    }
}
