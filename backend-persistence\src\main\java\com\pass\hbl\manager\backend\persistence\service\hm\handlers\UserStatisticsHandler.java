package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.domain.hm.LeagueBasicInfoDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.LeagueMembershipStatsDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.UserStatisticsBasicDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.LeagueMemberInfoDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.LeagueNameDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.UserStatisticsDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmClientRequest;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmSeason;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.RateLimitExceededException;
import com.pass.hbl.manager.backend.persistence.mapper.hm.UserStatisticsBasicMapper;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmClientRequestRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueMembershipRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserProfileRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.hm.SeasonService;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.util.Constants.SYSTEM_USERNAME;
import static com.pass.hbl.manager.backend.persistence.util.ParameterDefaults.DEFAULT_PARAM_STAT_RATE_LIMITING_ACTIVE;
import static com.pass.hbl.manager.backend.persistence.util.ParameterDefaults.PARAM_STAT_RATE_LIMITING_ACTIVE;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
@Service
@Transactional
/*
  Handles user profile operations performed by the administrator (system)
 */
public class UserStatisticsHandler {

    private final HmUserProfileRepository userProfileRepository;
    private final HmLeagueMembershipRepository leagueMembershipRepository;
    private final HmLeagueRepository leagueRepository;
    private final UserStatisticsBasicMapper userStatisticsBasicMapper;
    private final ParameterService parameterService;
    private final SeasonService seasonService;
    private final HmClientRequestRepository clientRequestRepository;
    private final RateLimitingHandler rateLimitHandler;

    @Getter
    private int maxStatPageSize = ParameterDefaults.DEFAULT_MAX_STAT_PAGE_SIZE;
    @Getter
    private int rateLimitMinutes = ParameterDefaults.DEFAULT_PARAM_STAT_RATE_LIMIT_MINUTES;
    @Getter
    private int forceRefreshCacheAfterMinutes = ParameterDefaults.DEFAULT_PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES;
    /**
     * Cache for user IDs to support pagination = Triple <left,Middle,right> represents the user ids who have
     * performed any change (related to the requested data structure e.g. joined leagues) in this time frame.
     * right -> IDs
     * middle -> changeStart e.g. 12:00 (requested date)
     * left -> changeEnd e.g 12h30 (cache update date, client request sent at)
     */
    private Triple<LocalDateTime, LocalDateTime, List<UUID>> cacheData = Triple.of(null, null, new ArrayList<>());

    public UserStatisticsHandler(HmUserProfileRepository userProfileRepository,
                                 HmLeagueMembershipRepository leagueMembershipRepository, HmLeagueRepository leagueRepository,
                                 UserStatisticsBasicMapper userStatisticsBasicMapper, ParameterService parameterService,
                                 SeasonService seasonService,
                                 HmClientRequestRepository clientRequestRepository,
                                 RateLimitingHandler rateLimitHandler) {
        this.userProfileRepository = userProfileRepository;
        this.leagueMembershipRepository = leagueMembershipRepository;
        this.leagueRepository = leagueRepository;
        this.userStatisticsBasicMapper = userStatisticsBasicMapper;
        this.parameterService = parameterService;
        this.seasonService = seasonService;
        this.clientRequestRepository = clientRequestRepository;
        this.rateLimitHandler = rateLimitHandler;
    }

    @SneakyThrows
    @Transactional(readOnly = true)
    public List<UserStatisticsDto> getAllUserStatistics(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws RateLimitExceededException {

        log.info("getAllUserStatistics: changedAfter: " + changedAfter + " pageable: " + pageable + " client: " + externalClient);

        boolean isRateLimitingActive = parameterService.getAsBoolean(PARAM_STAT_RATE_LIMITING_ACTIVE, DEFAULT_PARAM_STAT_RATE_LIMITING_ACTIVE, SYSTEM_USERNAME);

        rateLimitMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_STAT_RATE_LIMIT_MINUTES, ParameterDefaults.DEFAULT_PARAM_STAT_RATE_LIMIT_MINUTES, SYSTEM_USERNAME);

        forceRefreshCacheAfterMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, ParameterDefaults.DEFAULT_PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, SYSTEM_USERNAME);

        maxStatPageSize = parameterService.getAsInteger(ParameterDefaults.PARAM_MAX_STAT_PAGE_SIZE, ParameterDefaults.DEFAULT_MAX_STAT_PAGE_SIZE, SYSTEM_USERNAME);

        // Find the existing request for this client and URL
        Optional<HmClientRequest> existingRequestOpt = clientRequestRepository.findByRequestAndExternalClient(requestUrl, externalClient);

        if (isRateLimitingActive) {
            rateLimitHandler.checkRateLimit(changedAfter, pageable, requestUrl, externalClient, existingRequestOpt, rateLimitMinutes);
        }

        HmSeason currentSeason = seasonService.getCurrentSeason();
        UUID currentSeasonId = currentSeason.getId();

        LocalDateTime now = LocalDateTime.now();
        boolean forceRefreshCache = !isNull(cacheData.getLeft()) && now.isAfter(cacheData.getLeft().plusMinutes(forceRefreshCacheAfterMinutes));

        // Check if we need to refresh the cache based on multiple conditions:
        // 1. Cache hasn't been initialized yet (cacheData.getLeft() == null) -> first time refreshing cache
        // 2. Cache hasn't been initialized yet (cacheData.getRight() == null) -> User Ids list is empty
        // 3. Filter parameter changed (cacheData.getMiddle() != changedAfter) -> changeStart-date was changed
        // 4. forceRefreshCache if "forceRefreshCacheAfterMinutes" minutes passed -> Cache expiration time (in minutes)
        boolean refreshCache = cacheData.getLeft() == null || cacheData.getRight() == null || !Objects.equals(cacheData.getMiddle(), changedAfter) || forceRefreshCache;

        // Refresh the cache if needed
        if (refreshCache) {
            log.info("Refreshing user statistics cache with changedAfter: " + changedAfter);
            List<UUID> userIds;
            if (changedAfter == null) {
                userIds = userProfileRepository.findAllIds();
            } else {
                List<UUID> leagueIds = leagueRepository.findBySeasonIdAndChangedAfter(changedAfter, currentSeasonId);
                int batchSize = 1000; // Size of each batch

                // Split the leagueIds list into batches of 1000
                List<List<UUID>> batches = new ArrayList<>();
                for (int i = 0; i < leagueIds.size(); i += batchSize) {
                    batches.add(leagueIds.subList(i, Math.min(i + batchSize, leagueIds.size())));
                }

                // Use parallel streams to process the batches in parallel
                userIds = batches.parallelStream()
                        .flatMap(batch -> userProfileRepository.findIdsByModifiedAtAfter(changedAfter, batch).stream())
                        .toList();

                // userIds = userProfileRepository.findIdsByModifiedAtAfter(changedAfter, leagueIds);
            }
            cacheData = Triple.of(LocalDateTime.now(), changedAfter, userIds);
        }

        // If pageable size is > maxStatPageSize, use maxStatPageSize instead
        int pageSize = pageable.getPageSize() > maxStatPageSize ? maxStatPageSize : pageable.getPageSize();
        int pageNumber = pageable.getPageNumber();
        // Calculate pagination
        int startIndex = pageNumber * pageSize;
        int endIndex = Math.min(startIndex + pageSize, cacheData.getRight().size());

        // Check if the requested page is valid
        if (startIndex >= cacheData.getRight().size()) {
            return Collections.emptyList(); // Return empty list if page is out of bounds
        }

        List<UUID> pageUserIds = cacheData.getRight().subList(startIndex, endIndex);

        List<UserStatisticsBasicDO> usersBasicData = userProfileRepository.findBasicStatisticsDataByIdIn(pageUserIds);

        return usersBasicData.stream()
                .map(userData -> {
                    try {
                        UserStatisticsDto dto = mapToUserStatistics(userData, currentSeasonId);
                        // Set the changedAfter value in the response
                        dto.setChangedAfter(changedAfter);
                        return dto;
                    } catch (EntityNotExistException e) {
                        log.error("Failed to map user statistics: {}", e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private @NotNull UserStatisticsDto mapToUserStatistics(UserStatisticsBasicDO userData, UUID currentSeasonId) throws EntityNotExistException {


        UserStatisticsDto dto = userStatisticsBasicMapper.mapToDto(userData);

        // TODO souahm Change agreed on 01-07 with CRM Consults in order to reduce the payload. Remove the code once CRM is successfully tested and released

        /*List<LeagueMembershipStatsDO> membershipStats = leagueMembershipRepository.findByLeagueSeasonIdAndUserProfileId(currentSeasonId, UUID.fromString(userData.getId()));

        List<LeagueMemberInfoDto> currentLeagues = processCurrentLeaguesFromStats(membershipStats);
        dto.setCurrentLeagues(currentLeagues);

        List<LeagueNameDto> createdLeagues = processCreatedLeagues(currentSeasonId, UUID.fromString(userData.getId()));
        dto.setCreatedLeagues(createdLeagues);

        List<LeagueNameDto> joinedLeagues = processJoinedLeaguesFromStats(membershipStats);
        dto.setJoinedLeagues(joinedLeagues);

        List<LeagueNameDto> leftLeagues = processLeftLeaguesFromStats(membershipStats);
        dto.setLeftLeagues(leftLeagues);*/

        return dto;
    }

    private List<LeagueMemberInfoDto> processCurrentLeaguesFromStats(@NotNull List<LeagueMembershipStatsDO> membershipStats) {
        return membershipStats.stream()
                .filter(m -> nonNull(m.getJoined()) && !m.getDeleted())
                .map(membership -> {
                    try {
                        return mapToCurrentLeagueDtoFromStats(membership);
                    } catch (Exception e) {
                        log.error("Failed to process league membership stats: {}", e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private @NotNull LeagueMemberInfoDto mapToCurrentLeagueDtoFromStats(@NotNull LeagueMembershipStatsDO membership) {
        int rank = leagueMembershipRepository.calculateUserRankInLeague(membership.getLeagueId(), membership.getId());
        boolean isAdmin = membership.getLeagueOwnerId().equals(membership.getUserId());

        return new LeagueMemberInfoDto(membership.getLeagueId(), isAdmin, rank, membership.getBalance(), membership.getScore());
    }

    private List<LeagueNameDto> processCreatedLeagues(UUID seasonId, UUID userId) {

        List<LeagueBasicInfoDO> createdLeagues = leagueRepository.findBasicInfoBySeasonIdAndOwnerId(seasonId, userId);

        return createdLeagues.stream()
                .map(league -> new LeagueNameDto(
                        league.getId().toString(),
                        league.getName()
                ))
                .collect(Collectors.toList());
    }

    private List<LeagueNameDto> processJoinedLeaguesFromStats(@NotNull List<LeagueMembershipStatsDO> membershipStats) {
        return membershipStats.stream()
                .filter(m -> nonNull(m.getJoined()) && !m.getDeleted())
                .map(m -> new LeagueNameDto(
                        m.getLeagueId().toString(),
                        m.getLeagueName()
                ))
                .collect(Collectors.toList());
    }

    private List<LeagueNameDto> processLeftLeaguesFromStats(@NotNull List<LeagueMembershipStatsDO> membershipStats) {
        return membershipStats.stream()
                .filter(m -> nonNull(m.getJoined()) && m.getDeleted())
                .map(m -> new LeagueNameDto(
                        m.getLeagueId().toString(),
                        m.getLeagueName()
                ))
                .collect(Collectors.toList());
    }
}
