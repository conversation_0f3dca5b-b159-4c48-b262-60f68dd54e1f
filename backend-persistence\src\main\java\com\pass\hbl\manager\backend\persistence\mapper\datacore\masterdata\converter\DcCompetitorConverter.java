package com.pass.hbl.manager.backend.persistence.mapper.datacore.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.entity.datacore.DcCompetitor;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.repository.datacore.masterdata.DcCompetitorRepository;
import com.pass.hbl.manager.backend.persistence.util.Util;
import com.sportradar.datacore.rest.model.MatchCompetitor;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;


public abstract class DcCompetitorConverter extends AbstractConverter<List<MatchCompetitor>, DcCompetitor> {

    private final DcCompetitorRepository dcCompetitorRepository;
    private final boolean isHome;

    public DcCompetitorConverter(DcCompetitorRepository dcCompetitorRepository, boolean isHome) {
        this.dcCompetitorRepository = dcCompetitorRepository;
        this.isHome = isHome;
    }

    @Override
    protected DcCompetitor convert(List<MatchCompetitor> matchCompetitors) {

        if (CollectionUtils.isEmpty(matchCompetitors)) {
            return null;
        }
        String competitorId = Util.toStream(matchCompetitors)
                .filter(competitor -> Objects.equals(competitor.getIsHome(), isHome))
                .map(MatchCompetitor::getEntityId)
                .findFirst()
                .orElse(null);
        try {
            if (Objects.nonNull(competitorId)) {
                return dcCompetitorRepository.findById(competitorId).orElseThrow(() -> new EntityNotExistException(DcCompetitor.class, competitorId));
            }
        } catch (EntityNotExistException e) {
            throw new RuntimeException(e);
        }
        return null;

    }

    @Component
    public static class AwayDcCompetitorConverter extends DcCompetitorConverter {
        public AwayDcCompetitorConverter(DcCompetitorRepository dcCompetitorRepository) {
            super(dcCompetitorRepository, false);
        }
    }

    @Component
    public static class HomeDcCompetitorConverter extends DcCompetitorConverter {
        public HomeDcCompetitorConverter(DcCompetitorRepository dcCompetitorRepository) {
            super(dcCompetitorRepository, true);
        }
    }
}
