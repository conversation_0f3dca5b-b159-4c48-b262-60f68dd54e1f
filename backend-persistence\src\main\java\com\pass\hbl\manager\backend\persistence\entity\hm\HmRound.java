package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Table(name = "round", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity( name = "Round")
@SQLDelete( sql = "UPDATE hm.round SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmRound extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotBlank
    @Size(max = 16)
    @Column( name = "name")
    private String name;

    @NotNull
    @ToString.Exclude
    @Column(name = "`from`")
    private LocalDateTime from;

    @NotNull
    @ToString.Exclude
    @Column(name = "`to`")
    private LocalDateTime to;

    @NotNull
    @ToString.Exclude
    @Column(name = "`closing`")
    private LocalDateTime closing;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch =  FetchType.LAZY)
    @JoinColumn(name = "season_id", referencedColumnName = "id", updatable = false)
    private HmSeason season;

    @Column(name = "round_number")
    private Integer roundNumber;

    @Column(name = "special_round")
    private boolean specialRound;

    @ToString.Exclude
    @OneToMany(mappedBy = "round", fetch = FetchType.LAZY)
    private List<HmPlayerRoundStatistics> statistics;


    public HmRound(String name, LocalDateTime from, LocalDateTime to, LocalDateTime closing, HmSeason season, Integer roundNumber) {
        this.name = name;
        this.from = from;
        this.to = to;
        this.closing = closing;
        this.season = season;
        this.roundNumber = roundNumber;
    }

    public HmRound(String name, LocalDateTime from, LocalDateTime to, LocalDateTime closing, HmSeason season, Integer roundNumber, boolean specialRound) {
        this.name = name;
        this.from = from;
        this.to = to;
        this.closing = closing;
        this.season = season;
        this.roundNumber = roundNumber;
        this.specialRound = specialRound;
    }

    public HmRound(String name, LocalDateTime from, LocalDateTime to, LocalDateTime closing, HmSeason season) {
        this.name = name;
        this.from = from;
        this.to = to;
        this.closing = closing;
        this.season = season;
    }
}
