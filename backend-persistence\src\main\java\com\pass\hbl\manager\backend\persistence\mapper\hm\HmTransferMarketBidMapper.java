package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.TransferMarketBidDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.UserDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmTransferMarket;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmTransferMarketBid;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.AbstractConverter;
import org.modelmapper.Condition;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class HmTransferMarketBidMapper extends AbstractMapper<HmTransferMarketBid, TransferMarketBidDto> {

    private final HmUserMapper userMapper;

    public HmTransferMarketBidMapper(HmUserMapper userMapper) {
        super(HmTransferMarketBid.class, TransferMarketBidDto.class);
        this.userMapper = userMapper;
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected void customizeInit() {

        getModelMapper().getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);

        TypeMap<HmTransferMarketBid, TransferMarketBidDto> e2d = getModelMapper().createTypeMap(HmTransferMarketBid.class, TransferMarketBidDto.class);

        Condition<HmTransferMarketBid, TransferMarketBidDto> conditionBidderIsNotDeleted = context -> {
            try {
                //noinspection ResultOfMethodCallIgnored
                context.getSource().getBidder().getId().toString();
                return true;
            } catch (Exception e) {
                return false;
            }
        };
        Condition<HmTransferMarketBid, TransferMarketBidDto> conditionOwnerIsNotDeleted = context -> {
            try {
                //noinspection ResultOfMethodCallIgnored
                context.getSource().getOffer().getOwner().getId().toString();
                return true;
            } catch (Exception e) {
                return false;
            }
        };

        Converter<HmTransferMarketBid, UserDto> userConverter = new AbstractConverter<>() {
            @Override
            protected UserDto convert(HmTransferMarketBid source) {
                try {
                    return source == null ? null : userMapper.mapToDto(source.getBidder());
                } catch (Exception e) {
                    return UserDto.getAnonymous();
                }
            }
        };
        Converter<HmTransferMarketBid, String> ownerConverter = new AbstractConverter<>() {
            @Override
            protected String convert(HmTransferMarketBid source) {
                try {
                    return source == null ? null : source.getOffer().getOwner().getId().toString();
                } catch (Exception e) {
                    return UserDto.getAnonymous().getId();
                }
            }
        };
        Converter<HmTransferMarket, String> leagueConverter = new AbstractConverter<>() {
            @Override
            protected String convert(HmTransferMarket source) {
                return source == null ? null : source.getLeague().getId().toString();
            }
        };
        Converter<HmTransferMarket, String> playerConverter = new AbstractConverter<>() {
            @Override
            protected String convert(HmTransferMarket source) {
                return source == null ? null : source.getPlayer().getId().toString();
            }
        };

        e2d.addMappings(mapper -> mapper.map(HmTransferMarketBid::getId, TransferMarketBidDto::setId));
        e2d.addMappings((mapper -> mapper.when(conditionBidderIsNotDeleted).using(userConverter).map(HmTransferMarketBid::self, TransferMarketBidDto::setBidder)));
        e2d.addMappings((mapper -> mapper.when(conditionOwnerIsNotDeleted).using(ownerConverter).map(HmTransferMarketBid::self, TransferMarketBidDto::setOwnerId)));
        e2d.addMappings((mapper -> mapper.using(leagueConverter).map(HmTransferMarketBid::getOffer, TransferMarketBidDto::setLeagueId)));
        e2d.addMappings((mapper -> mapper.using(playerConverter).map(HmTransferMarketBid::getOffer, TransferMarketBidDto::setPlayerId)));
        e2d.addMappings(mapper -> mapper.map(HmTransferMarketBid::getBid, TransferMarketBidDto::setBid));
        e2d.addMappings(mapper -> mapper.map(HmTransferMarketBid::getStatus, TransferMarketBidDto::setStatus));
    }

    @Override
    protected TransferMarketBidDto customizeMapToDto(TransferMarketBidDto transferMarketBidDto, HmTransferMarketBid hmTransferMarketBid) {
        if (transferMarketBidDto.getBidder() == null) {
            transferMarketBidDto.setBidder(UserDto.getAnonymous());
        }
        return super.customizeMapToDto(transferMarketBidDto, hmTransferMarketBid);
    }
}
