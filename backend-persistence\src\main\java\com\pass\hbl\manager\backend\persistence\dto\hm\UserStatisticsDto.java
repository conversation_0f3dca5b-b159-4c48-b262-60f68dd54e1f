package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * User statistics data transfer object
 * Contains comprehensive user data for statistics API endpoint
 */
@JsonRootName("UserStatistics")
@Getter
@Setter
@Schema(description = "User statistics with comprehensive data")
public class UserStatisticsDto {

    @Schema(description = "User SSO id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    private String sso_id;

    @Schema(description = "Username", example = "john_doe", required = true)
    @NotBlank
    @Size(max = 256)
    private String username;

    @Schema(description = "Registration date and time", required = true)
    private LocalDateTime registeredSince;

    @Schema(description = "Last login date and time")
    private LocalDateTime lastLogin;

    @Schema(description = "Premium status", example = "true")
    private boolean premium;

    @Schema(description = "Premium expiration date and time")
    private LocalDateTime premiumExpiration;

    @Schema(description = "User's preferred language", example = "en")
    private String language;

    @Schema(description = "Account deleted status", example = "false")
    private boolean accountDeleted;

    @Schema(description = "Manager level", example = "6")
    private int managerLevel;

    // TODO souahm Change agreed on 01-07 with CRM Consults in order to reduce the payload. Remove the code once CRM is successfully tested and released
    /*@Schema(description = "Current leagues with user statistics")
    private List<LeagueMemberInfoDto> currentLeagues = new ArrayList<>();

    @Schema(description = "Leagues the user has left")
    private List<LeagueNameDto> leftLeagues = new ArrayList<>();

    @Schema(description = "Leagues created by the user")
    private List<LeagueNameDto> createdLeagues = new ArrayList<>();

    @Schema(description = "Leagues the user has joined")
    private List<LeagueNameDto> joinedLeagues = new ArrayList<>();*/

    @Schema(description = "App version", example = "2.5.1")
    private String appVersion;

    @Schema(description = "Filter date for changed after query", example = "2024-02-20T15:30:00")
    private LocalDateTime changedAfter;

    @JsonCreator
    public UserStatisticsDto() {
    }

    /**
     * Constructor with essential fields
     */
    public UserStatisticsDto(String sso_id, String username, LocalDateTime registeredSince) {
        this.sso_id = sso_id;
        this.username = username;
        this.registeredSince = registeredSince;
        this.premium = false;
        this.accountDeleted = false;
        this.managerLevel = 1;
        /*this.currentLeagues = new ArrayList<>();
        this.leftLeagues = new ArrayList<>();
        this.createdLeagues = new ArrayList<>();
        this.joinedLeagues = new ArrayList<>();*/
        this.changedAfter = null;
    }
}
