package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserNotification;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public interface HmUserNotificationRepository extends PagingAndSortingRepository<HmUserNotification, UUID> {

    List<HmUserNotification> findByUserIdInAndCreatedAtAfterOrderByCreatedAtDesc(List<UUID> userIds, LocalDateTime earliestDate);

    void deleteByUserId(UUID userId);
}
