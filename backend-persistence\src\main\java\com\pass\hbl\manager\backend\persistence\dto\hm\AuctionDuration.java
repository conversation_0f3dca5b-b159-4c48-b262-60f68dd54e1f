package com.pass.hbl.manager.backend.persistence.dto.hm;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Arrays;
import java.util.Comparator;

@Schema(implementation = AuctionDuration.class)
public enum AuctionDuration {

    SIX_HOURS(6), HALF_DAY(12), ONE_DAY(24), TWO_DAYS(48), THREE_DAYS(72);

    public int getHours() {
        return hours;
    }

    private final int hours;

    AuctionDuration(int hours) {
        this.hours = hours;
    }

    public static AuctionDuration getMax() {
        return Arrays.stream(values()).max(Comparator.comparing(AuctionDuration::getHours)).orElseThrow(() -> new RuntimeException("No AuctionDuration defined"));
    }
}
