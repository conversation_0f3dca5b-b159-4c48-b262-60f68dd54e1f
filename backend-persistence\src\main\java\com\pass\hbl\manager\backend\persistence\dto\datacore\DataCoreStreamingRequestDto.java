package com.pass.hbl.manager.backend.persistence.dto.datacore;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonRootName("get datacore streaming access request")
@Getter
@Setter
@NoArgsConstructor
@Schema(description = "Get datacore streaming access request data")
public class DataCoreStreamingRequestDto {

    @NotNull
    @Schema(description = "credentialId", required = true)
    private String credentialId;

    @NotNull
    @Schema(description = "credentialSecret", required = true)
    private String credentialSecret;

    @NotNull
    @Schema(description = "Id of the fixture (match)", required = true)
    private String fixtureId;

    @NotNull
    @Schema(description = "sport", example = "handball", required = true)
    private String sport;

    @NotNull
    @Schema(description = "requested scopes", required = true)
    private List<String> scopes;

    @NotNull
    @Schema(description = "If true, the returned signed WebSocket endpoint URL will include the port (default: false). Some websocket/mqtt libraries require this.")
    private boolean includePort;

    public DataCoreStreamingRequestDto(String credentialId, String credentialSecret, String fixtureId, String sport, List<String> scopes) {
        this.credentialId = credentialId;
        this.credentialSecret = credentialSecret;
        this.fixtureId = fixtureId;
        this.sport = sport;
        this.scopes = scopes;
        this.includePort = false;
    }
}
