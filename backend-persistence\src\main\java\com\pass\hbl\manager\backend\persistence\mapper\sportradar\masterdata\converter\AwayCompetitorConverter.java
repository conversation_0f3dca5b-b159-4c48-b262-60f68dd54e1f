package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.repository.sportradar.SrCompetitorRepository;
import com.pass.hbl.manager.backend.persistence.util.SportradarConstants;
import org.springframework.stereotype.Component;

@Component
public class AwayCompetitorConverter extends AbstractCompetitorConverter {
    public AwayCompetitorConverter(SrCompetitorRepository repository) {
        super(repository, SportradarConstants.AWAY);
    }
}
