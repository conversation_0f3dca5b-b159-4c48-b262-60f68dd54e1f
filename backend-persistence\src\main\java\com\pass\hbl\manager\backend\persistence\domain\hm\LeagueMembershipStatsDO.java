package com.pass.hbl.manager.backend.persistence.domain.hm;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Domain object for league membership statistics
 * Contains only the fields needed for user statistics
 */
public interface LeagueMembershipStatsDO {
    /**
     * Get the membership ID
     * @return Membership ID
     */
    UUID getId();
    
    /**
     * Get the league ID
     * @return League ID
     */
    UUID getLeagueId();
    
    /**
     * Get the league name
     * @return League name
     */
    String getLeagueName();
    
    /**
     * Get the league owner ID
     * @return League owner ID
     */
    UUID getLeagueOwnerId();
    
    /**
     * Get the user ID
     * @return User ID
     */
    UUID getUserId();
    
    /**
     * Get the joined date
     * @return Joined date
     */
    LocalDateTime getJoined();
    
    /**
     * Get the user score
     * @return User score
     */
    Integer getScore();
    
    /**
     * Get the user balance
     * @return User balance
     */
    Integer getBalance();
    
    /**
     * Get the deleted status
     * @return Deleted status
     */
    Boolean getDeleted();
}
