package com.pass.hbl.manager.backend.persistence.mapper.datacore.transfer;

import com.pass.hbl.manager.backend.persistence.entity.datacore.DcSeason;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmSeason;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.transfer.converter.DataCoreIdConverter;
import org.springframework.stereotype.Component;

@Component
public class Dc2HmSeasonMapper extends AbstractDc2HmMapper<DcSeason, HmSeason> {


    public Dc2HmSeasonMapper(DataCoreIdConverter idConverter) {
        super(DcSeason.class, HmSeason.class, idConverter, null);
    }
}
