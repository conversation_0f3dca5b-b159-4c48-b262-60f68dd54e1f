package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

@Table(name = "player_market_value", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@SQLDelete(sql = "UPDATE hm.player_market_value SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmPlayerMarketValue extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "player_id", referencedColumnName = "id", updatable = false)
    private HmPlayer player;

    @NotNull
    @Column(name = "valid_from")
    private LocalDateTime validFrom;

    @Column(name = "market_value")
    private Integer marketValue;

    @NotNull
    @Column(name = "valid_to")
    private LocalDateTime validTo;

    @Column(name = "current_value", columnDefinition = "boolean not null default false", nullable = false)
    private boolean currentValue;

    public HmPlayerMarketValue(HmPlayer player, LocalDateTime validFrom, Integer marketValue, LocalDateTime validTo, boolean currentValue) {
        this.player = player;
        this.validFrom = validFrom;
        this.marketValue = marketValue;
        this.validTo = validTo;
        this.currentValue = currentValue;
    }

    @Override
    protected void checkCustomDates() {
        if (this.validTo == null) {
            this.validTo = ZERO_DATE;
        }
    }

    @Override
    protected void loadCustomDates() {
        if (Objects.equals(this.validTo, ZERO_DATE)) {
            this.validTo = null;
        }
    }
}
