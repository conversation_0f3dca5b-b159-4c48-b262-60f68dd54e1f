package com.pass.hbl.manager.backend.persistence.domain.datacore;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
public class DataCoreIdMappingDo {

    @JsonProperty(value = "DataCore ID")
    private String dataCoreId;

    @JsonProperty(value = "Sportradar ID")
    private String sportRadarId;

    @JsonCreator
    public DataCoreIdMappingDo() {
    }
}
