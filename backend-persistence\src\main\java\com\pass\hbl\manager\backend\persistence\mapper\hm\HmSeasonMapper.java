package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.RoundDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.SeasonDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmRound;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmSeason;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static java.util.Objects.nonNull;

@Component
public class HmSeasonMapper extends AbstractMapper<HmSeason, SeasonDto> {

    private final HmRoundMapper roundMapper;

    public HmSeasonMapper(HmRoundMapper roundMapper) { super(HmSeason.class, SeasonDto.class);
        this.roundMapper = roundMapper;
    }

    @Override
    protected SeasonDto customizeMapToDto(SeasonDto seasonDto, HmSeason season) {
        List<HmRound> rounds = season.getRounds();
        if (nonNull(rounds)) {
            AtomicInteger i = new AtomicInteger();
            List<RoundDto> startedRounds = rounds.stream().filter(r -> r.getFrom().isBefore(LocalDateTime.now()))
                    .map(roundMapper::mapToDto).toList();
            List<RoundDto> roundDtos = new ArrayList<>(startedRounds);
            roundDtos.sort(Comparator.comparing(RoundDto::getFrom));
            roundDtos.forEach(roundDto -> roundDto.setRoundOrder(i.getAndIncrement()));
            seasonDto.setStartedRounds(roundDtos);
        }
        return seasonDto;
    }
}
