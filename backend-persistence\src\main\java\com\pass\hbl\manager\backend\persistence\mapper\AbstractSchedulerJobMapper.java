package com.pass.hbl.manager.backend.persistence.mapper;

import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobDto;
import com.pass.hbl.manager.backend.persistence.entity.AbstractSchedulerJobEntity;
import org.modelmapper.TypeMap;

public class AbstractSchedulerJobMapper<T extends AbstractSchedulerJobEntity> extends AbstractMapper<T, SchedulerJobDto> {

    public AbstractSchedulerJobMapper(Class<T> tClass, Class<SchedulerJobDto> schedulerJobDtoClass) {
        super(tClass, schedulerJobDtoClass);

        // cannot do this in customInit() as tClass there is still null

        TypeMap<T, SchedulerJobDto> e2d = getModelMapper().createTypeMap(tClass, SchedulerJobDto.class);
        e2d.addMappings(mapper -> mapper.map(T::getParameterMap, SchedulerJobDto::setParameters));

        TypeMap<SchedulerJobDto, T> d2e = getModelMapper().createTypeMap(SchedulerJobDto.class, tClass);
        d2e.addMappings(mapper -> mapper.map(SchedulerJobDto::getParameters, T::setParameterMap));
        d2e.addMappings(mapper -> mapper.skip(T::setParameters));
    }
}
