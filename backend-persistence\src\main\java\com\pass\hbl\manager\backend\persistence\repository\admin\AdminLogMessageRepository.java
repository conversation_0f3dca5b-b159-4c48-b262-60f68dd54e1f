package com.pass.hbl.manager.backend.persistence.repository.admin;

import com.pass.hbl.manager.backend.persistence.dto.admin.LogMessageLevel;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminLogMessage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.time.LocalDateTime;
import java.util.UUID;

public interface AdminLogMessageRepository extends PagingAndSortingRepository<AdminLogMessage, UUID> {
    Page<AdminLogMessage> findByOrderByCreatedAtDesc(Pageable pageable);

    Page<AdminLogMessage> findByNameLikeOrderByCreatedAtDesc(String name, Pageable pageable);
    Page<AdminLogMessage> findByNameLikeAndCreatedAtAfterOrderByCreatedAtDesc(String name, LocalDateTime ts, Pageable pageable);
    Page<AdminLogMessage> findByNameLikeAndCreatedAtBeforeOrderByCreatedAtDesc(String name, LocalDateTime ts, Pageable pageable);
    Page<AdminLogMessage> findByNameLikeAndCreatedAtBetweenOrderByCreatedAtDesc(String name, LocalDateTime ts1, LocalDateTime ts2, Pageable pageable);

    Page<AdminLogMessage> findByLevelOrderByCreatedAtDesc(LogMessageLevel level, Pageable pageable);
    Page<AdminLogMessage> findByLevelAndCreatedAtAfterOrderByCreatedAtDesc(LogMessageLevel level, LocalDateTime ts, Pageable pageable);
    Page<AdminLogMessage> findByLevelAndCreatedAtBeforeOrderByCreatedAtDesc(LogMessageLevel level, LocalDateTime ts, Pageable pageable);
    Page<AdminLogMessage> findByLevelAndCreatedAtBetweenOrderByCreatedAtDesc(LogMessageLevel level, LocalDateTime ts1, LocalDateTime ts2, Pageable pageable);

    Page<AdminLogMessage> findByNameLikeAndLevelOrderByCreatedAtDesc(String name, LogMessageLevel level, Pageable pageable);
    Page<AdminLogMessage> findByNameLikeAndLevelAndCreatedAtAfterOrderByCreatedAtDesc(String name, LogMessageLevel level, LocalDateTime ts, Pageable pageable);
    Page<AdminLogMessage> findByNameLikeAndLevelAndCreatedAtBeforeOrderByCreatedAtDesc(String name, LogMessageLevel level, LocalDateTime ts, Pageable pageable);
    Page<AdminLogMessage> findByNameLikeAndLevelAndCreatedAtBetweenOrderByCreatedAtDesc(String name, LogMessageLevel level, LocalDateTime ts1, LocalDateTime ts2, Pageable pageable);

    Page<AdminLogMessage> findByCreatedAtAfterOrderByCreatedAtDesc(LocalDateTime ts, Pageable pageable);
    Page<AdminLogMessage> findByCreatedAtBeforeOrderByCreatedAtDesc(LocalDateTime ts, Pageable pageable);
    Page<AdminLogMessage> findByCreatedAtBetweenOrderByCreatedAtDesc(LocalDateTime ts1, LocalDateTime ts2, Pageable pageable);
}
