package com.pass.hbl.manager.backend.persistence.mapper.shared;

import com.pass.hbl.manager.backend.persistence.dto.shared.ImageDto;
import com.pass.hbl.manager.backend.persistence.entity.shared.SharedImage;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.springframework.stereotype.Component;

@Component
public class SharedImageMapper extends AbstractMapper<SharedImage, ImageDto> {

    public SharedImageMapper() {
        super(SharedImage.class, ImageDto.class);
    }
}
