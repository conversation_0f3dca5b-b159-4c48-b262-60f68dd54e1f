package com.pass.hbl.manager.backend.persistence.dto.admin;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@JsonRootName("ExternalDataMapping")
@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ExternalDataMappingDto extends AbstractDto<ExternalDataMappingDto, String> {

    @Schema(name = "id", description = "UUID of the mapping", required = true)
    @Size(min = 36, max = 36)
    @NotBlank
    private String id;

    @Schema(name = "datasource", description = "External data source", required = true)
    @NotNull
    private Datasource datasource;

    @Schema(name = "entity", description = "External entity", required = true)
    @NotNull
    private ExternalEntity entity;

    @Schema(name = "sourceId", description = "external ID", required = true)
    @NotEmpty
    private String sourceId;

    @Schema(name = "hmId", description = "internal ID", required = true)
    @NotEmpty
    private String hmId;
}
