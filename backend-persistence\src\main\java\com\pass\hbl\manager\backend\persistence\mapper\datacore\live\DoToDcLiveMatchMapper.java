package com.pass.hbl.manager.backend.persistence.mapper.datacore.live;

import com.pass.hbl.manager.backend.persistence.domain.datacore.DcLiveMatchStatusDo;
import com.pass.hbl.manager.backend.persistence.domain.datacore.FixtureStatus;
import com.pass.hbl.manager.backend.persistence.entity.datacore.DcLiveMatch;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.live.converter.DcLiveMatchEntityConverter;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.live.converter.DcLiveMatchScoreConverter;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.live.converter.DcLiveMatchWinnerConverter;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.modelmapper.spi.MappingContext;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;

@Slf4j
@Component
public class DoToDcLiveMatchMapper extends AbstractMapper<DcLiveMatchStatusDo, DcLiveMatch> {

    private final DcLiveMatchWinnerConverter dcLiveMatchWinnerConverter;

    private final DcLiveMatchEntityConverter.Team1EntityConverter team1EntityConverter;
    private final DcLiveMatchEntityConverter.Team2EntityConverter team2EntityConverter;

    private final DcLiveMatchScoreConverter.Team1ScoreConverter team1ScoreConverter;
    private final DcLiveMatchScoreConverter.Team2ScoreConverter team2ScoreConverter;

    public DoToDcLiveMatchMapper(DcLiveMatchWinnerConverter dcLiveMatchWinnerConverter, DcLiveMatchEntityConverter.Team1EntityConverter team1EntityConverter, DcLiveMatchEntityConverter.Team2EntityConverter team2EntityConverter, DcLiveMatchScoreConverter.Team1ScoreConverter team1ScoreConverter, DcLiveMatchScoreConverter.Team2ScoreConverter team2ScoreConverter) {
        super(DcLiveMatchStatusDo.class, DcLiveMatch.class);
        this.dcLiveMatchWinnerConverter = dcLiveMatchWinnerConverter;
        this.team1EntityConverter = team1EntityConverter;
        this.team2EntityConverter = team2EntityConverter;
        this.team1ScoreConverter = team1ScoreConverter;
        this.team2ScoreConverter = team2ScoreConverter;
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected void customizeInit() {
        Converter<String, FixtureStatus> toFixtureStatusConverter = ctx -> {
            try {
                return ctx.getSource() == null ? null : FixtureStatus.valueOf(ctx.getSource());
            } catch (Exception e) {
                log.error("failed to convert " + ctx.getSource() +" to FixtureStatus. Reason: " + e);
                return null;
            }
        };

        TypeMap<DcLiveMatchStatusDo, DcLiveMatch> typeMap = getOrCreateTypeMap(DcLiveMatchStatusDo.class, DcLiveMatch.class);
        typeMap.addMappings(mapper -> mapper.using(team1EntityConverter).map(DcLiveMatchStatusDo::getData, DcLiveMatch::setTeam1Id));
        typeMap.addMappings(mapper -> mapper.using(team2EntityConverter).map(DcLiveMatchStatusDo::getData, DcLiveMatch::setTeam2Id));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getData().getStatus().getFixture(), DcLiveMatch::setStatus));
        typeMap.addMappings(mapper -> mapper.using(dcLiveMatchWinnerConverter).map(DcLiveMatchStatusDo::getData, DcLiveMatch::setWinner));
        typeMap.addMappings(mapper -> mapper.using(team1ScoreConverter).map(DcLiveMatchStatusDo::getData, DcLiveMatch::setTeam1CurrentScore));
        typeMap.addMappings(mapper -> mapper.using(team2ScoreConverter).map(DcLiveMatchStatusDo::getData, DcLiveMatch::setTeam2CurrentScore));

        typeMap.addMappings(mapper -> mapper.map(source -> source.getData().getSources().getEvent(), DcLiveMatch::setEventSourceStatus));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getData().getSources().getScoreboard(), DcLiveMatch::setScoreboardSourceStatus));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getData().getClock().getMain(), DcLiveMatch::setClock));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getData().getClock().isRunning(), DcLiveMatch::setRunning));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getData().getTimestamp(), DcLiveMatch::setTime));
    }
}
