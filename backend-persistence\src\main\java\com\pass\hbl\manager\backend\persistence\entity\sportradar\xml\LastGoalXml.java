package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "LastGoal")
@NoArgsConstructor
@Data
public class LastGoalXml {

    @JacksonXmlProperty(localName = "Time")
    private LastGoalTimeXml time;

    @JacksonXmlProperty(localName = "MatchTime")
    private MatchTimeXml matchTime;

    @JacksonXmlProperty(localName = "Team")
    private TeamXml team;
}
