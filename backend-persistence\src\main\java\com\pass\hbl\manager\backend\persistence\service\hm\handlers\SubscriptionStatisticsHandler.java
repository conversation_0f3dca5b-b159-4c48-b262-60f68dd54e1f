package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.domain.hm.SubscriptionStatisticsBasicDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.SubscriptionStatisticsDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmClientRequest;
import com.pass.hbl.manager.backend.persistence.exception.RateLimitExceededException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmClientRequestRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserSubscriptionRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.util.Constants.SYSTEM_USERNAME;
import static com.pass.hbl.manager.backend.persistence.util.ParameterDefaults.*;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class SubscriptionStatisticsHandler {

    private final HmUserSubscriptionRepository userSubscriptionRepository;
    private final HmClientRequestRepository clientRequestRepository;
    private final ParameterService parameterService;
    private final RateLimitingHandler rateLimitHandler;

    @Getter
    private int maxStatPageSize = ParameterDefaults.DEFAULT_MAX_SUBSCRIPTION_STAT_PAGE_SIZE;
    @Getter
    private int rateLimitMinutes = ParameterDefaults.DEFAULT_PARAM_SUBSCRIPTION_STAT_RATE_LIMIT_MINUTES;
    @Getter
    private int forceRefreshCacheAfterMinutes = ParameterDefaults.DEFAULT_PARAM_SUBSCRIPTION_FORCE_REFRESH_CACHE_AFTER_MINUTES;

    /**
     * Cache for subscription IDs to support pagination = Triple <left,Middle,right> represents the subscription ids who have
     * performed any change (related to the requested data structure e.g. subscription updates) in this time frame.
     * right -> IDs
     * middle -> changeStart e.g. 12:00 (requested date)
     * left -> changeEnd e.g 12h30 (cache update date, client request sent at)
     */
    private Triple<LocalDateTime, LocalDateTime, List<UUID>> cacheData = Triple.of(null, null, new ArrayList<>());

    @SneakyThrows
    @Transactional(readOnly = true)
    public List<SubscriptionStatisticsDto> getAllSubscriptionStatistics(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws RateLimitExceededException {

        log.info("getAllSubscriptionStatistics: changedAfter: " + changedAfter + " pageable: " + pageable + " client: " + externalClient);

        boolean isRateLimitingActive = parameterService.getAsBoolean(PARAM_SUBSCRIPTION_STAT_RATE_LIMITING_ACTIVE, DEFAULT_PARAM_SUBSCRIPTION_STAT_RATE_LIMITING_ACTIVE, SYSTEM_USERNAME);

        rateLimitMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_SUBSCRIPTION_STAT_RATE_LIMIT_MINUTES, ParameterDefaults.DEFAULT_PARAM_SUBSCRIPTION_STAT_RATE_LIMIT_MINUTES, SYSTEM_USERNAME);

        forceRefreshCacheAfterMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_SUBSCRIPTION_FORCE_REFRESH_CACHE_AFTER_MINUTES, ParameterDefaults.DEFAULT_PARAM_SUBSCRIPTION_FORCE_REFRESH_CACHE_AFTER_MINUTES, SYSTEM_USERNAME);

        maxStatPageSize = parameterService.getAsInteger(ParameterDefaults.PARAM_MAX_SUBSCRIPTION_STAT_PAGE_SIZE, ParameterDefaults.DEFAULT_MAX_SUBSCRIPTION_STAT_PAGE_SIZE, SYSTEM_USERNAME);

        // Find the existing request for this client and URL
        Optional<HmClientRequest> existingRequestOpt = clientRequestRepository.findByRequestAndExternalClient(requestUrl, externalClient);

        if (isRateLimitingActive) {
            rateLimitHandler.checkRateLimit(changedAfter, pageable, requestUrl, externalClient, existingRequestOpt, rateLimitMinutes);
        }

        // Validate page size
        if (pageable.getPageSize() > maxStatPageSize) {
            throw new IllegalArgumentException("Page size cannot exceed " + maxStatPageSize);
        }

        LocalDateTime now = LocalDateTime.now();
        boolean forceRefreshCache = cacheData.getLeft() != null && now.isAfter(cacheData.getLeft().plusMinutes(forceRefreshCacheAfterMinutes));

        // Refresh cache if:
        // 1. Cache hasn't been initialized yet (cacheData.getLeft() == null)
        // 2. Filter parameter changed (cacheData.getMiddle() != changedAfter)
        // 3. forceRefreshCache if "forceRefreshCacheAfterMinutes" minutes passed
        boolean refreshCache = cacheData.getLeft() == null || cacheData.getRight() == null || !Objects.equals(cacheData.getMiddle(), changedAfter) || forceRefreshCache;

        if (refreshCache) {
            log.info("Refreshing subscription statistics cache with changedAfter: {}", changedAfter);
            List<String> subscriptionIdStrings;
            if (changedAfter == null) {
                subscriptionIdStrings = userSubscriptionRepository.findAllSubscriptionStatisticsIds();
            } else {
                subscriptionIdStrings = userSubscriptionRepository.findSubscriptionStatisticsIds(changedAfter);
            }
            // Convert String IDs to UUIDs
            List<UUID> subscriptionIds = subscriptionIdStrings.stream()
                    .map(UUID::fromString)
                    .collect(Collectors.toList());
            cacheData = Triple.of(LocalDateTime.now(), changedAfter, subscriptionIds);
            log.info("Found {} subscriptions modified after: {}", subscriptionIds.size(), changedAfter);
        }

        // Calculate pagination
        int totalSubscriptions = cacheData.getRight().size();
        int pageSize = pageable.getPageSize() > maxStatPageSize ? maxStatPageSize : pageable.getPageSize();
        int startIndex = pageable.getPageNumber() * pageSize;
        int endIndex = Math.min(startIndex + pageSize, totalSubscriptions);

        if (startIndex >= totalSubscriptions) {
            return new ArrayList<>();
        }

        List<UUID> pageSubscriptionIds = cacheData.getRight().subList(startIndex, endIndex);

        List<SubscriptionStatisticsBasicDO> subscriptionsBasicData = userSubscriptionRepository.findSubscriptionStatisticsByIdIn(pageSubscriptionIds);

        return subscriptionsBasicData.stream()
                .map(subscriptionData -> {
                    SubscriptionStatisticsDto dto = new SubscriptionStatisticsDto();
                    dto.setUserSsoId(subscriptionData.getUserSsoId());
                    dto.setSubscriptionId(subscriptionData.getSubscriptionId());
                    dto.setSubscriptionStartDate(subscriptionData.getStartDate());
                    dto.setSubscriptionEndDate(subscriptionData.getEndDate());
                    dto.setSubscriptionType(subscriptionData.getSubscriptionType());
                    dto.setPlatform(subscriptionData.getPlatform());
                    dto.setPaymentId(subscriptionData.getLastPaymentId());
                    dto.setPaymentAmount(subscriptionData.getLastPaymentAmount());
                    dto.setPaymentDate(subscriptionData.getLastPaymentDate());
                    return dto;
                })
                .collect(Collectors.toList());
    }

}

