package com.pass.hbl.manager.backend.persistence.mapper.hm.converters;

import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerHblDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerInClub;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmRound;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.modelmapper.AbstractConverter;
import org.modelmapper.Condition;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.pass.hbl.manager.backend.persistence.util.Constants.ROUNDS;
import static java.util.Objects.isNull;

@Component
public class HmPlayerHblDtoMapper extends AbstractMapper<HmPlayer, PlayerHblDto> {

    private final PlayerStatisticsAggregator playerStatisticsAggregator;

    public HmPlayerHblDtoMapper(PlayerStatisticsAggregator playerStatisticsAggregator) {
        super(HmPlayer.class, PlayerHblDto.class);
        this.playerStatisticsAggregator = playerStatisticsAggregator;
    }

    @Override
    protected void customizeInit() {
        TypeMap<HmPlayer, PlayerHblDto> e2d = getModelMapper().createTypeMap(HmPlayer.class, PlayerHblDto.class);

        Condition<HmPlayer, PlayerHblDto> conditionCurrentClubExists = context -> {
            try {
                if (isNull(context.getSource()) || isNull(context.getSource().getCurrentClub())) return false;
                return context.getSource().getCurrentClub().size() > 0;
            } catch (Exception e) {
                return false;
            }
        };

        Converter<HmPlayer, String> currentClubConverter = new AbstractConverter<>() {

            @Override
            protected String convert(HmPlayer source) {
                try {
                    if (isNull(source)) {
                        return null;
                    }
                    Optional<HmPlayerInClub> playerInClub = source.getCurrentClub().stream().findFirst();
                    return playerInClub.map(hmPlayerInClub -> hmPlayerInClub.getClub().getName()).orElse(null);
                } catch (Exception e) {
                    return null;
                }
            }
        };
        e2d.addMappings(mapper -> mapper.when(conditionCurrentClubExists).using(currentClubConverter).map(HmPlayer::self, PlayerHblDto::setCurrentClub));
    }

    @Override
    protected PlayerHblDto customizeMapToDto(PlayerHblDto playerHblDto, HmPlayer hmPlayer, Map<String, Object> context) {
        if (context != null && context.containsKey(ROUNDS)) {
            List<HmRound> alreadyPlayedRounds = List.of((HmRound) context.get(ROUNDS));
            if (!alreadyPlayedRounds.isEmpty()) {
                // Get the total number of games played by the player
                playerHblDto.setPlayedGames(playerStatisticsAggregator.getGamesPlayed(hmPlayer.getId(), alreadyPlayedRounds));
            }
        }
        playerHblDto.setHblClubImageId(hmPlayer.getClub().getHblImageId());
        return super.customizeMapToDto(playerHblDto, hmPlayer, context);
    }
}
