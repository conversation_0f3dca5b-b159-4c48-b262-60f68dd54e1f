package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.UserAwardDto;
import com.pass.hbl.manager.backend.persistence.dto.hm.UserAwardInfoDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserAward;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.HmEntityToStringConverter;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.UserAwardLocalizationToDtoConverter;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.UserAwardLocalizationToEntityConverter;
import com.pass.hbl.manager.backend.persistence.repository.shared.SharedLocalizationRepository;
import org.modelmapper.PropertyMap;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

@Component
public class HmUserAwardMapper extends AbstractMapper<HmUserAward, UserAwardDto> {

    public HmUserAwardMapper(SharedLocalizationRepository localizationRepository) {
        super(HmUserAward.class, UserAwardDto.class);

        // cannot do this in customInit() as localizationRepository there is still null
        getModelMapper().addMappings(new PropertyMap<UserAwardDto, HmUserAward>() {
            @Override
            protected void configure() {
                using(new UserAwardLocalizationToEntityConverter(localizationRepository)).map(source.getAward().getLocalizations(), destination.getAward().getDescriptions());
            }
        });

        getModelMapper().addMappings(new PropertyMap<HmUserAward, UserAwardDto>() {
            public void configure() {
                using(new UserAwardLocalizationToDtoConverter()).map(source.getAward().getDescriptions(), destination.getAward().getLocalizations());
            }
        });
    }

    @Override
    protected void customizeInit() {
        TypeMap<HmUserAward, UserAwardDto> e2d = getOrCreateTypeMap(HmUserAward.class, UserAwardDto.class);
        e2d.addMappings(mapper -> mapper.using(new HmEntityToStringConverter<HmUserProfile>()).map(HmUserAward::getUserProfile, UserAwardDto::setUserId));
        e2d.addMappings(mapper -> mapper.using(new HmEntityToStringConverter<HmLeague>()).map(HmUserAward::getLeague, UserAwardDto::setLeagueId));
    }
}
