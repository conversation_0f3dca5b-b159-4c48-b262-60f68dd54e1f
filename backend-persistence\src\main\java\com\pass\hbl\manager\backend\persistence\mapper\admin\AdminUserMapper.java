package com.pass.hbl.manager.backend.persistence.mapper.admin;

import com.pass.hbl.manager.backend.persistence.dto.admin.AdminUserDto;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminUser;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.springframework.stereotype.Component;

@Component
public class AdminUserMapper extends AbstractMapper<AdminUser, AdminUserDto> {

    public AdminUserMapper() {
        super(AdminUser.class, AdminUserDto.class);
    }
}
