package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.pass.hbl.manager.backend.persistence.util.Constants.*;

@Transactional
@Service
@Slf4j
public class SsoValidationHandler {

    public List<String> checkSsoIdsInBatches(List<String> ssoIds) {
        int batchSize = 10000;
        // Initialize list for invalid IDs
        List<String> invalidIds = IntStream.range(0, (ssoIds.size() + batchSize - 1) / batchSize)
                .mapToObj(i -> ssoIds.subList(i * batchSize, Math.min(ssoIds.size(), (i + 1) * batchSize)))
                .map(batch -> {
                    int start = ssoIds.indexOf(batch.get(0)) + 1;
                    int end = ssoIds.indexOf(batch.get(batch.size() - 1)) + 1;
                    try {
                        // Call method to get invalidated IDs in this batch
                        return getInvalidSsoIds(batch);
                    } catch (Exception e) {
                        // Handle errors that may occur during batch processing
                        log.error("checkSsoIdsInBatches : Error occurred while processing batch ["
                                + start + "-" + end + "] of [" + ssoIds.size() + "] IDs", e);
                        // Throw the same exception to report the error
                        throw new RuntimeException(e);
                    }
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        return invalidIds;
    }

    public List<String> getInvalidSsoIds(List<String> inputIds) {
        HttpClient httpClient = HttpClient.newHttpClient();
        ObjectMapper objectMapper = new ObjectMapper();
        String AUTHORIZATION_HEADER = "Basic " + encodeCredentials(SSO_VALIDATION_USERNAME, SSO_VALIDATION_PASSWORD);
        try {
            // Convert the list of IDs to a JSON string
            String jsonPayload = objectMapper.writeValueAsString(Map.of("ids", inputIds));
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(API_SSO_VALIDATE_URL))
                    .header("Content-Type", "application/json")
                    .header("Authorization", AUTHORIZATION_HEADER)
                    .POST(HttpRequest.BodyPublishers.ofString(jsonPayload))
                    .build();
            HttpResponse<String> httpResponse = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            // Parse the response body for debugging
            ObjectMapper responseMapper = new ObjectMapper();
            JsonNode rootNode = responseMapper.readTree(httpResponse.body());
            List<String> invalidIds = objectMapper.convertValue(rootNode, new TypeReference<>() {
            });
            log.info("getInvalidSsoIds : Invalid Ids successfully retrieved");
            return invalidIds;
        } catch (Exception e) {
            log.error("getInvalidSsoIds : Error occurred while retrieving invalidated IDs: " + e.getMessage());
            return Collections.emptyList(); // Return an empty list in case of failure
        }
    }

    private static String encodeCredentials(String username, String password) {
        String credentials = username + ":" + password;
        return Base64.getEncoder().encodeToString(credentials.getBytes(StandardCharsets.UTF_8));
    }
}
