package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@JsonRootName("Player match score response")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Player match score response used for player score history")
public class PlayerMatchScoreResponseDto {

    @Schema(description = "id of the match", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String matchId;

    @Schema(description = "number of the match in season", required = true)
    private int matchNumber;

    @NotNull
    @ToString.Exclude
    @Schema(description = "label of the season", required = true)
    private String seasonLabel;

    @Schema(description = "player match score", required = true)
    private  PlayerMatchScoreDto playerScore;
}
