package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.util.ApiConstants;
import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerDto;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.service.hm.TeamService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.RolesAllowed;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;

import static com.pass.hbl.manager.backend.admin.util.ApiConstants.ROLE_ADMIN;
import static com.pass.hbl.manager.backend.admin.util.ApiConstants.ROLE_ADMIN_WRITE;

@RestController
@RequestMapping(ApiConstants.TEAM_API)
@Validated
@Tag(name = "team", description = "API for admin team and lineup management")
@RolesAllowed({ROLE_ADMIN, ROLE_ADMIN_WRITE})
public class TeamController {

    private final TeamService service;

    public TeamController(TeamService service) {
        this.service = service;
    }

    @Operation(summary = "Get the lineup by league and round for the given user (username or email address)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "The lineup", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = List.class))}),
            @ApiResponse(responseCode = "404", description = "No Lineup found", content = {@Content})
    })
    @GetMapping(value = "/lineup/{user}", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Pair<UUID, String>> getLineupByUserAndLeagueAndRound(@Parameter(name = "user") @PathVariable(name = "user")
                                                                     @NotNull String usernameOrEmailAddress, @Parameter(name = "leagueName")
                                                                     @RequestParam(name = "leagueName") @NotNull String leagueName,
                                                                     @Parameter(name = "roundNumber", schema = @Schema(allowableValues = {"1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32","33","34"}))
                                                                     @RequestParam(name = "roundNumber")
                                                                     @NotNull String roundNumber, @Parameter(name = "specialRound")
                                                                         @RequestParam(name = "specialRound") @NotNull Boolean specialRound) throws EntityNotExistException, FormatException {
        return service.getLineupByUsernameAndLeagueNameAndRoundNumber(usernameOrEmailAddress, leagueName, roundNumber, specialRound);
    }

    @Operation(summary = "Get team by league and round for the given user (username or email address)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Found players", content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = Pair.class))}),
            @ApiResponse(responseCode = "404", description = "No players found", content = {@Content})
    })
    @GetMapping(value = "/{user}", produces = MediaType.APPLICATION_JSON_VALUE)
    public Pair<String, List<PlayerDto>> getTeamByUsernameAndLeagueNameAndRoundNumber(@Parameter(name = "user") @PathVariable(name = "user")
                                      @NotNull String usernameOrEmailAddress, @Parameter(name = "leagueName")
                                      @RequestParam(name = "leagueName") @NotNull String leagueName,
                                                        @Parameter(name = "roundNumber", schema = @Schema(allowableValues = {"1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32","33","34"}))
                                      @RequestParam(name = "roundNumber")
                                      @NotNull String roundNumber, @Parameter(name = "specialRound")
                                                                                          @RequestParam(name = "specialRound") @NotNull Boolean specialRound) throws EntityNotExistException {
        return service.getTeamByUsernameAndLeagueNameAndRoundNumber(usernameOrEmailAddress, leagueName, roundNumber, specialRound);
    }
}
