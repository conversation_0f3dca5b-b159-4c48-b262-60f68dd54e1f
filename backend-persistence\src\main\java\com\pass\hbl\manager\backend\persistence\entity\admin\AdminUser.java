package com.pass.hbl.manager.backend.persistence.entity.admin;

import com.pass.hbl.manager.backend.persistence.dto.admin.UserRole;
import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static java.util.Collections.emptyList;

@Table(name = "user", schema = "admin", catalog = "handball_manager")
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@ToString
@SQLDelete(sql = "UPDATE admin.user SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
@Slf4j
public class AdminUser extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @Column(name = "username")
    String username;

    @NotNull
    @Column(name = "password")
    String password;

    @NotNull
    @Column(name = "email_address")
    String emailAddress;

    /* one time password */
    @Column(name = "otp")
    Integer otp;

    @NotNull
    @Column(name = "roles")
    String roles;

    public AdminUser(String username, String password, String emailAddress, List<UserRole> userRoles) {
        this.username = username;
        this.password = password;
        this.emailAddress = emailAddress;
        this.roles = userRoles.toString();
    }

    public List<UserRole> getRoles() {
        try {
            String cleanedString = roles.replaceAll("[\\[\\] ]", "");
            String[] roleArray = cleanedString.split(",");

            // Trim role strings and create a List<String>
            return Arrays.stream(roleArray)
                    .map(String::trim).map(UserRole::valueOf)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("AdminUser:getRoles failed", e);
            return emptyList();
        }
    }
}
