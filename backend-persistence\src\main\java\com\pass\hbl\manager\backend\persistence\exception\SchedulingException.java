package com.pass.hbl.manager.backend.persistence.exception;

import lombok.Getter;

public class SchedulingException extends CodedException {

    public enum Mode { VALIDATE, START, DELETE, GET, STOP, CANCELLED, FINISHED }

    @Getter
    private final Mode mode;


    public SchedulingException(Mode mode, String message, String... args) {
        this(mode, message, null, args);
    }

    public SchedulingException(Mode mode, String message, Throwable cause, String... args) {
        super(ExceptionCode.SCHEDULING, message, cause, args);
        this.mode = mode;
    }

}
