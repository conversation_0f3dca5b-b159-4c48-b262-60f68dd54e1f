package com.pass.hbl.manager.backend.persistence.entity.sportradar;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Table(name = "live_match_player_statistics", schema = "sportradar", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class SrLiveMatchPlayerStatistics extends AbstractSportradarEntity {

    @NotNull
    @Column(name = "player_id", nullable = false)
    private String playerId;

    @NotNull
    @Column(name = "match_id", nullable = false)
    private String matchId;

    @NotNull
    @Column(name = "team_id", nullable = false)
    private String teamId;

    @Column(name = "substitute", nullable = false)
    private Boolean substitute;

    @NotNull
    @Column(name = "type", nullable = false)
    private Integer type;

    @NotNull
    @Column(name = "value", nullable = false, precision = 8, scale = 2)
    private Double value;
}
