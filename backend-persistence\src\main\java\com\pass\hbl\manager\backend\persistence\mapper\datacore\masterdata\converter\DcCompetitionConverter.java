package com.pass.hbl.manager.backend.persistence.mapper.datacore.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.entity.datacore.DcCompetition;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.repository.datacore.masterdata.DcCompetitionRepository;
import lombok.SneakyThrows;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class DcCompetitionConverter extends AbstractConverter<UUID, DcCompetition> {

    private final DcCompetitionRepository repository;

    public DcCompetitionConverter(DcCompetitionRepository repository) {
        this.repository = repository;
    }

    @SneakyThrows
    @Override
    protected DcCompetition convert(UUID source) {
        return source == null ? null : repository.findById(source.toString()).orElseThrow(() -> new EntityNotExistException(DcCompetition.class, source.toString()));
    }
}
