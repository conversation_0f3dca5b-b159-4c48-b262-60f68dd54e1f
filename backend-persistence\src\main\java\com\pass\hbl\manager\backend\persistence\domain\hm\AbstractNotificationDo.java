package com.pass.hbl.manager.backend.persistence.domain.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.NotificationEvent;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
public class AbstractNotificationDo {

    protected NotificationEvent notificationEvent;

    public NotificationEvent getNotificationEvent() {
        return this.notificationEvent;
    }

    public void setNotificationEvent(NotificationEvent notificationEvent) {
        this.notificationEvent = notificationEvent;
    }
}
