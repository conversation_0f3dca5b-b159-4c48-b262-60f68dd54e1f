package com.pass.hbl.manager.backend.persistence.mapper.datacore.masterdata.converter;



import com.pass.hbl.manager.backend.persistence.util.Util;
import org.modelmapper.Converter;
import org.modelmapper.spi.MappingContext;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;



@Component
public class LocalDateTimeConverter implements Converter<org.threeten.bp.LocalDateTime, LocalDateTime> {
    @Override
    public java.time.LocalDateTime convert(MappingContext<org.threeten.bp.LocalDateTime, java.time.LocalDateTime> context) {
        org.threeten.bp.LocalDateTime source = context.getSource();
        if (source == null) {
            return null;
        }
        return Util.toUtc( java.time.LocalDateTime.of(
                source.getYear(), source.getMonthValue(), source.getDayOfMonth(),
                source.getHour(), source.getMinute(), source.getSecond(), source.getNano()
        ));
    }
}
