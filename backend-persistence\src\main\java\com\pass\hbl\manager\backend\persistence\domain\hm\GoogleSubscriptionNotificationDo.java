package com.pass.hbl.manager.backend.persistence.domain.hm;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoogleSubscriptionNotificationDo {

    @Schema(description = "The version of this notification. Initially, this is 1.0", example = "1.0")
    private String version;

    @Schema(description = "The notificationType for a subscription", example = "(2) SUBSCRIPTION_RENEWED - An active subscription was renewed")
    private Integer notificationType;

    @Schema(description = "The token provided to the user's device when the subscription was purchased.")
    private String purchaseToken;

    @Schema(description = "The purchased subscription's product ID", example = "monthly001")
    private String subscriptionId;
}
