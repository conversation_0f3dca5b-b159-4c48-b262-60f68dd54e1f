package com.pass.hbl.manager.backend.persistence.mapper.admin;

import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalDataMappingDto;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminExternalDataMapping;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.springframework.stereotype.Component;

@Component
public class AdminExternalDataMappingMapper extends AbstractMapper<AdminExternalDataMapping, ExternalDataMappingDto> {

    public AdminExternalDataMappingMapper() {
        super(AdminExternalDataMapping.class, ExternalDataMappingDto.class);
    }
}
