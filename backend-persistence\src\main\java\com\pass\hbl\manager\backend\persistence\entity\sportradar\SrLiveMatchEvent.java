package com.pass.hbl.manager.backend.persistence.entity.sportradar;

import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Table(name = "live_match_event", schema = "sportradar", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class SrLiveMatchEvent extends AbstractSportradarEntity {

    @NotNull
    @Column(name = "type", nullable = false)
    private int type;

    @Column(name = "extra")
    private Integer extra;

    @Column(name = "outcome")
    private Integer outcome;

    @Column(name = "disabled")
    private boolean disabled;

    @Column(name = "match_id")
    private String matchId;

    @Column(name = "time")
    private String time;

    @Column(name = "player1_id")
    private String playerId1;

    @Column(name = "player2_id")
    private String playerId2;

    @Column(name = "goalkeeper")
    private String goalkeeper;

    @Column(name = "score_home")
    private Integer scoreHome;

    @Column(name = "score_away")
    private Integer scoreAway;

    @Column(name = "position")
    private Integer position;

    @Override
    public ExternalEntity getExternalEntity() {
        return ExternalEntity.EVENT;
    }
}
