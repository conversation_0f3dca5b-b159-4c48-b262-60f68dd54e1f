package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;
import java.util.List;

@JsonRootName("TransferMarket")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Transfer market history item")
public class TransferMarketHistoryDto extends AbstractDto<TransferMarketHistoryDto, String> {

    @Schema(description = "id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true, accessMode = Schema.AccessMode.READ_ONLY)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @NotNull
    @Schema(description = "User owning of the transfer item", required = true)
    private UserDto owner;

    @NotNull
    @Schema(description = "Player offered for a transfer", required = true)
    private PlayerLobbyDto player;

    @Schema(description = "Bids of the transfer item")
    private List<BidDto> bids;

    @NotNull
    @Schema(description = "Transfer price", required = true)
    private int price;

    @Schema(description = "Creation datetime of the transfer item")
    private ZonedDateTime createdAt;

}
