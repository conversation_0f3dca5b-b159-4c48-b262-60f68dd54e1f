package com.pass.hbl.manager.backend.persistence.entity.hm.converter;

import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.util.Util;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Locale;

@Converter
public class LocaleConverter implements AttributeConverter<Locale, String> {

    @Override
    public String convertToDatabaseColumn(Locale locale) {
        if (locale != null) {
            return Util.getLanguageTag(locale);
        }
        return null;
    }

    @Override
    public Locale convertToEntityAttribute(String languageTag) {
        try {
            return Util.getLocaleByLanguageTag(languageTag);
        } catch (FormatException e) {
            Util.sneakyThrow(e);
            return null;
        }
    }
}
