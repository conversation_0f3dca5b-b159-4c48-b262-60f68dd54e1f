package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.MachtRoundDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmMatch;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.springframework.stereotype.Component;

@Component
public class HmMatchRoundMapper extends AbstractMapper<HmMatch, MachtRoundDto> {

    public HmMatchRoundMapper() { super(HmMatch.class, MachtRoundDto.class);   }
}