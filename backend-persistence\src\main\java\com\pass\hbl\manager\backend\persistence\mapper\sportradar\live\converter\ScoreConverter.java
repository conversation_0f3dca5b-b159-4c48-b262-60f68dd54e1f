package com.pass.hbl.manager.backend.persistence.mapper.sportradar.live.converter;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.xml.ScoreXml;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.xml.ScoresXml;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.xml.SimpleTeamXml;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Objects;

public abstract class ScoreConverter extends AbstractConverter<ScoresXml, Integer> {

    private final String type;
    private final int teamNo;

    protected ScoreConverter(String type, int teamNo) {
        this.type = type;
        this.teamNo = teamNo;
    }

    @Override
    public Integer convert(ScoresXml source) {
        if (source == null || CollectionUtils.isEmpty(source.getScores())) {
            return null;
        }
        return source.getScores().stream()
                .filter(s -> Objects.equals(s.getType(), type))
                .map(s -> teamNo == 1 ? s.getTeam1() : s.getTeam2())
                .map(SimpleTeamXml::getValue).findFirst().orElse(null);
    }

    @Component
    public static class Team1CurrentScoreConverter extends ScoreConverter {
        public Team1CurrentScoreConverter() {
            super(ScoreXml.TYPE_CURRENT, 1);
        }
    }

    @Component
    public static class Team2CurrentScoreConverter extends ScoreConverter {
        public Team2CurrentScoreConverter() {
            super(ScoreXml.TYPE_CURRENT, 2);
        }
    }

    @Component
    public static class Team1HalftimeScoreConverter extends ScoreConverter {
        public Team1HalftimeScoreConverter() {
            super(ScoreXml.TYPE_CURRENT, 1);
        }
    }

    @Component
    public static class Team2HalftimeScoreConverter extends ScoreConverter {
        public Team2HalftimeScoreConverter() {
            super(ScoreXml.TYPE_CURRENT, 2);
        }
    }

    @Component
    public static class Team1FinalScoreConverter extends ScoreConverter {
        public Team1FinalScoreConverter() {
            super(ScoreXml.TYPE_CURRENT, 1);
        }
    }

    @Component
    public static class Team2FinalScoreConverter extends ScoreConverter {
        public Team2FinalScoreConverter() {
            super(ScoreXml.TYPE_CURRENT, 2);
        }
    }
}
