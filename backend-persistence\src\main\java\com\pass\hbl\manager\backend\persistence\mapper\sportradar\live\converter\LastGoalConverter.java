package com.pass.hbl.manager.backend.persistence.mapper.sportradar.live.converter;

import com.pass.hbl.manager.backend.persistence.dto.hm.LastGoal;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.xml.LastGoalXml;
import com.pass.hbl.manager.backend.persistence.exception.MappingException;
import com.pass.hbl.manager.backend.persistence.util.Util;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

@Component
public class LastGoalConverter extends AbstractConverter<LastGoalXml, LastGoal> {
    @Override
    protected LastGoal convert(LastGoalXml source) {
        if (source == null || source.getTeam() == null || source.getTeam().getValue() == null) {
            return null;
        }
        try {
            return LastGoal.getBySrLiveMatchValue(source.getTeam().getValue());
        } catch (MappingException e) {
            Util.sneakyThrow(e);
            return null;
        }
    }
}
