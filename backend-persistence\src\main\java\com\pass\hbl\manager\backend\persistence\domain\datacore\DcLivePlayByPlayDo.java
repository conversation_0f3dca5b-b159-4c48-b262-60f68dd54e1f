package com.pass.hbl.manager.backend.persistence.domain.datacore;

import com.fasterxml.jackson.annotation.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * Represents a live match play by play Object received by the DataCore Streaming Api (Mqtt)
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@ToString
public class DcLivePlayByPlayDo {

    private List<DcSportEvent> dcSportEvents;

    @JsonCreator
    public DcLivePlayByPlayDo(List<DcSportEvent> dcSportEvents) {
        this.dcSportEvents = dcSportEvents;
    }

}
