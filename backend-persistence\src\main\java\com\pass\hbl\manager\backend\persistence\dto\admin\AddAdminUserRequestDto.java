package com.pass.hbl.manager.backend.persistence.dto.admin;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonRootName("Add admin user request")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Add admin user request")
public class AddAdminUserRequestDto {

    @NotNull
    @Schema(description = "username", required = true)
    private String username;

    @Schema(description = "password of the admin user", required = true)
    private String password;

    @NotNull
    @Schema(description = "email address of the admin user", required = true)
    private String emailAddress;

    @NotNull
    @Schema(description = "roles", example = "ROLE_ADMIN", required = true)
    private List<UserRole> roles;
}
