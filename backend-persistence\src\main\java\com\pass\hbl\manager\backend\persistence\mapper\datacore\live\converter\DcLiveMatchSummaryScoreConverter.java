package com.pass.hbl.manager.backend.persistence.mapper.datacore.live.converter;

import com.pass.hbl.manager.backend.persistence.domain.datacore.DataObject;
import com.pass.hbl.manager.backend.persistence.domain.datacore.Entity;
import com.pass.hbl.manager.backend.persistence.domain.datacore.LiveMatchSummaryDo;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static java.util.Objects.isNull;

public abstract class DcLiveMatchSummaryScoreConverter extends AbstractConverter<LiveMatchSummaryDo, Integer> {

    // team number is 1 for the first team, 2 for the second team
    private final int teamNumber;

    protected DcLiveMatchSummaryScoreConverter(int teamNumber) {
        this.teamNumber = teamNumber;
    }

    @Override
    public Integer convert(LiveMatchSummaryDo source) {
        // Map of key = entityId (matchId), value = Entity data (score ..)
        Map<String, Entity> entities = source.getEntities();
        if (isNull(source.getStatus()) || isNull(entities) || entities.isEmpty()) {
            return null;
        }
        // TODO HBLMAN-565 check if the first item is home and second is away
        Set<String> keys = entities.keySet();
        List<String> keyList = new ArrayList<>(keys);
        if (keyList.size() == 2) {
            // Get the match
            String matchId = keyList.get(teamNumber - 1);
            return entities.get(matchId).getScore();
        } else {
            return null;
        }
    }

    @Component
    public static class Team1ScoreConverter extends DcLiveMatchSummaryScoreConverter {
        public Team1ScoreConverter() {
            super(1);
        }
    }

    @Component
    public static class Team2ScoreConverter extends DcLiveMatchSummaryScoreConverter {
        public Team2ScoreConverter() {
            super(2);
        }
    }

}
