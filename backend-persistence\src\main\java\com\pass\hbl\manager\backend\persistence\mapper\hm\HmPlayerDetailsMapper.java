package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerDetailsDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.hm.converters.PlayerStatisticsAggregator;
import com.pass.hbl.manager.backend.persistence.util.Util;
import org.modelmapper.Converter;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class HmPlayerDetailsMapper extends AbstractMapper<HmPlayer, PlayerDetailsDto> {

    private final PlayerStatisticsAggregator playerStatisticsAggregator;

    public HmPlayerDetailsMapper(PlayerStatisticsAggregator playerStatisticsAggregator) {
        super(HmPlayer.class, PlayerDetailsDto.class);
        this.playerStatisticsAggregator = playerStatisticsAggregator;
    }


    @Override
    protected void customizeInit() {
        TypeMap<HmPlayer, PlayerDetailsDto> e2d = getModelMapper().createTypeMap(HmPlayer.class, PlayerDetailsDto.class);

        Converter<String, String> toAlpha2LanguageCode = ctx -> {
            try {
                return ctx.getSource() == null ? null : Util.convertToIso2LanguageTag(ctx.getSource());
            } catch (FormatException e) {
                return ctx.getSource();
            }
        };

        e2d.addMappings(mapper -> mapper.using(toAlpha2LanguageCode).map(HmPlayer::getNationality, PlayerDetailsDto::setNationality));
    }


    @Override
    protected PlayerDetailsDto customizeMapToDto(PlayerDetailsDto playerDetailsDto, HmPlayer hmPlayer, Map<String, Object> context) {
        // Set player Statistics
        playerDetailsDto.setPlayedRoundsStatistics(this.playerStatisticsAggregator.getPlayerStatistics(hmPlayer.getId(), hmPlayer.getPosition()));
        return super.customizeMapToDto(playerDetailsDto, hmPlayer, context);
    }
}
