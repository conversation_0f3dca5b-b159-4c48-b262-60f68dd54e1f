package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;

@JsonRootName("UserLeagueTransaction")
@Getter
@Setter
@Schema(description = "Object representing a single user's transaction in league user for displaying the balance history")
public class UserLeagueTransactionDto extends AbstractPictureDto<UserLeagueTransactionDto, String> {

    @Schema(description = "transaction id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", accessMode = Schema.AccessMode.READ_ONLY)
    @NotBlank
    @Size(min = 36, max = 36)
    @JsonIgnore
    private String id;

    @Schema(description = "the credited or debited money trough the transaction", required = true)
    @NotNull
    @Min(0)
    private int money;

    @Schema(description = "description of the transaction", example = "€ for PURCHASE of player XYZ")
    private String description;

    @Schema(description = "true, if the money is credited", example = "true by player sale", required = true)
    @NotNull
    private boolean credit;

    @Schema(description = "date in which the transaction occurred", required = true)
    @NotNull
    private ZonedDateTime transactionDate;

    @Schema(description = "type of the transaction that lead to balance change", example = "PLAYER_PURCHASE",  required = true)
    @NotNull
    private BalanceTransactionType transactionType;

    @JsonCreator
    public UserLeagueTransactionDto() {
    }

    public UserLeagueTransactionDto(int money, String description, boolean credit, ZonedDateTime transactionDate, BalanceTransactionType transactionType, String picture) {
        this.money = money;
        this.description = description;
        this.credit = credit;
        this.transactionDate = transactionDate;
        this.transactionType = transactionType;
        this.setPicture(picture);
    }
}
