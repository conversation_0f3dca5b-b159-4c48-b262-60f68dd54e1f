package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.*;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserRoundScore;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

public interface HmUserRoundScoreRepository extends PagingAndSortingRepository<HmUserRoundScore, UUID> {

    @Modifying
    @Query(value = "update HmUserRoundScore s set s.deleted = true, s.deletedAt = CURRENT_TIMESTAMP where s.league.id = :leagueId and s.deleted = false")
    void deleteByLeague(@Param("leagueId") UUID leagueId);

    @Modifying
    @Query(value = "update HmUserRoundScore s set s.deleted = true, s.deletedAt = CURRENT_TIMESTAMP where (s.league.id = :leagueId and s.user.id = :userId) and s.deleted = false")
    void deleteByLeagueAndUser(@Param("leagueId") UUID leagueId, @Param("userId") UUID userId);

    @Query(value = """
            SELECT DISTINCT Cast(s.id as varchar) from hm.user_round_score s inner join hm.league_membership m
                        on s.user_id = m.member_id and s.league_id = m.league_id
                        where s.deleted = false and m.deleted = false and s.round_id = :roundId""", nativeQuery = true)
    Set<String> findUserRoundScoresWithExistingMemberships(@Param("roundId") UUID roundId);

    @Modifying
    @Query(value = "update hm.user_round_score set deleted = true, deleted_at = CURRENT_TIMESTAMP where id in :inValidIds and round_id = :roundId and deleted = false", nativeQuery = true)
    int deleteAllByIdInList(@Param("inValidIds") List<UUID> inValidIds, @Param("roundId") UUID roundId);

    Optional<HmUserRoundScore> findByUserIdAndRoundIdAndLeagueId(UUID userId, UUID roundId, UUID leagueId);

    @Query(value = "select distinct s.league.id from HmUserRoundScore s where s.round.id = :roundId and s.score is not null")
    Set<UUID> findAlreadyEvaluatedLeaguesByRoundId(@Param("roundId") UUID roundId);

    Iterable<HmUserRoundScore> findByUserIdAndLeagueId(UUID userId, UUID leagueId);

    List<HmUserRoundScore> findAllByUserIdAndRoundId(@Param("userId") UUID userId,@Param("roundId") UUID roundId);

    boolean existsByUserIdAndLeagueIdAndRoundId(UUID userId, UUID leagueId, UUID id);

    @Query(value = "select distinct s.id from HmUserRoundScore s where s.round.id = :roundId and s.deleted = false")
    Set<UUID> findAllIdsByRoundId(@Param("roundId") UUID roundId);

    @Query(value = "select Cast(id as varchar) as id, Cast(round_id as varchar) as roundId, deleted_at as deletedAt from hm.user_round_score where user_id = :userId and league_id = :leagueId and deleted = true", nativeQuery = true)
    List<HmUserRoundScoreDeletedDO> findDeletedUserRoundScoresByUserIdAndLeagueId(@Param("userId") UUID userId, @Param("leagueId") UUID leagueId);

    @Query(value = "select Cast(user_id as varchar) as userId, Cast(league_id as varchar) as leagueId, Cast(round_id as varchar) as roundId," +
            " score as score, start_balance as startBalance from hm.user_round_score where league_id in :leagueIds and round_id = :roundId and score is null and deleted = false", nativeQuery = true)
    List<HmUserRoundScoreDO> findStartUserRoundScoresByLeagueIdInAndRoundId(@Param("leagueIds") Set<UUID> leagueIds, @Param("roundId") UUID roundId);

    @Modifying
    @Query(value = "update hm.user_round_score set deleted = false, deleted_at = '1970-01-01 00:00:00' where id in :ids and deleted = true", nativeQuery = true)
    int activateUserRoundScoresByIds(@Param("ids") List<UUID> ids);

    /*@Query(value = "select Cast(user_id as varchar) as userId, sum(score) as totalScore from hm.user_round_score where deleted = false \n" +
            "and round_id = :roundId group by user_id having sum (score) > 0", nativeQuery = true)
    List<HmUserTotalScoreDO> findUsersWithPositiveTotalScoreInRound(@Param("roundId") UUID roundId);*/

    @Query(value = """
            with user_total_score as(
            \tselect user_id, sum(score) as totalScoreInRound from hm.user_round_score where deleted = false\s
            \tand round_id = :roundId group by user_id having sum (score) > 0
            )
            select Cast(uts.user_id as varchar) as userId, uts.totalScoreInRound as totalScoreInRound, u.experience_points as currentExperiencePoints
            from user_total_score uts inner join hm.user_profile u on uts.user_id = u.id where u.deleted = false""", nativeQuery = true)
    List<HmUserTotalScoreDO> findUsersWithPositiveTotalScoreInRound(@Param("roundId") UUID roundId);
/*
    @Query(value = "select Cast(user_id as varchar) as userId, sum(score) as totalScore from hm.user_round_score where deleted = false \n" +
            "and round_id = :roundId and league_id in (:leagueIds) group by user_id having sum (score) > 0", nativeQuery = true)
    List<HmUserTotalScoreDO> findUsersWithPositiveTotalScoreInRoundAndLeagueIdIn(@Param("roundId") UUID roundId, @Param("leagueIds") List<UUID> leagueIds);*/

    @Query(value = """
            with user_total_score as(
            \tselect user_id, sum(score) as totalScoreInRound from hm.user_round_score where deleted = false\s
            \tand round_id = :roundId and league_id in (:leagueIds) group by user_id having sum (score) > 0
            )
            select Cast(uts.user_id as varchar) as userId, uts.totalScoreInRound as totalScoreInRound, u.experience_points as currentExperiencePoints
            from user_total_score uts inner join hm.user_profile u on uts.user_id = u.id where u.deleted = false""", nativeQuery = true)
    List<HmUserTotalScoreDO> findUsersWithPositiveTotalScoreInRoundAndLeagueIdIn(@Param("roundId") UUID roundId, @Param("leagueIds") List<UUID> leagueIds);

    @Query(value = "select Cast(league_id as varchar) as leagueId, Cast(user_id as varchar) as userId, score from hm.user_round_score where round_id = :roundId \n" +
            "and (not score is null) and deleted = false", nativeQuery = true)
    List<HmUserLeagueScoreDO> findAllUserLeagueScoresByRoundId(UUID roundId);

    @Query(value = "select Cast(league_id as varchar) as leagueId, Cast(user_id as varchar) as userId, score from hm.user_round_score where round_id = :roundId \n" +
            "and (not score is null) and not (league_id in :excludedLeagues) and deleted = false", nativeQuery = true)
    List<HmUserLeagueScoreDO> findUserLeagueScoresByRoundIdExcludingLeagues(UUID roundId, Set<UUID> excludedLeagues);

    @Modifying
    @Query("update HmUserRoundScore urs set urs.score = urs.score + :scoreToAdd, urs.modifiedAt = CURRENT_TIMESTAMP where urs.id = :id and urs.score is not null and urs.deleted = false")
    int addScoreById(@Param("id") UUID id,@Param("scoreToAdd") int scoreToAdd);

    @Query(value = "select Cast(s.league_id as varchar) as leagueId, Cast(s.user_id as varchar) as userId, s.score, s.start_balance, r.round_number as roundNumber, " +
            "s.created_at as createdAt, s.modified_at as modifiedAt from hm.user_round_score s inner join hm.round r on s.round_id = r.id " +
            "where s.user_id = :userId and s.league_id = :leagueId " +
            "and s.deleted = false and r.deleted = false and (not s.score is null) and (s.modified_at > :sinceDate or s.created_at > :sinceDate)", nativeQuery = true)
    List<HmUserRoundScoreTransactionDO> findAllByUserIdAndLeagueIdSinceDate(@Param("userId") UUID userId, @Param("leagueId") UUID leagueId, @Param("sinceDate") LocalDateTime sinceDate);

}
