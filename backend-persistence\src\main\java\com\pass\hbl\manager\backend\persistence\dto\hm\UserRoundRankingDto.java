package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@JsonRootName("UserRanking")
@Getter
@Setter
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "Info object for manager ranking by league and round")
public class UserRoundRankingDto implements Serializable {

    // relevant for Redis cache
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "Round in the current season", required = true)
    @NotNull
    private RoundDto round;

    @Schema(description = "Round scores of the users in the given league", required = true)
    private List<UserRankingDto> scores;

}
