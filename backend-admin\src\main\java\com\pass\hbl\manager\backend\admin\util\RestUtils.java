package com.pass.hbl.manager.backend.admin.util;

import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;

import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;

public class RestUtils {

    /**
     * Helper for adding HATEOS self links with the pattern <code>http(s)://[host:port]/[base]/controllerMapping/{id}</code>.
     *
     * @param dto the dto to add the self link to
     * @param controller the according controller
     * @param <T> the dto class
     * @return the dto itself for fluent API programming
     */
    public static <T extends AbstractDto<T, ?>> T addSelfLink(T dto, Class<?> controller) {
        dto.add(linkTo(controller).slash(dto.getId()).withSelfRel());
        return dto;
    }
}
