package com.pass.hbl.manager.backend.persistence.service;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmJobCronExpressionDO;
import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobDto;
import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobMode;
import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobResult;
import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobStatus;
import com.pass.hbl.manager.backend.persistence.entity.AbstractSchedulerJobEntity;
import com.pass.hbl.manager.backend.persistence.entity.AbstractSchedulerJobTermination;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminSchedulerJob;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.SchedulingException;
import com.pass.hbl.manager.backend.persistence.job.AbstractJob;
import com.pass.hbl.manager.backend.persistence.job.ScheduledJob;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractSchedulerJobMapper;
import com.pass.hbl.manager.backend.persistence.repository.AbstractSchedulerJobRepository;
import com.pass.hbl.manager.backend.persistence.repository.AbstractSchedulerJobTerminationRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.reflections.Reflections;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.lang.reflect.InvocationTargetException;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicBoolean;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;

@Slf4j
@Transactional
public abstract class AbstractSchedulerService<
        ENTITY extends AbstractSchedulerJobEntity,
        REPO extends AbstractSchedulerJobRepository<ENTITY>,
        MAPPER extends AbstractSchedulerJobMapper<ENTITY>> {

    private final ApplicationContext context;

    private final REPO repository;

    private final AbstractSchedulerJobTerminationRepository terminationRepository;

    @Qualifier("HBLThreadPoolScheduler")
    private final ThreadPoolTaskScheduler executor;

    private final MAPPER mapper;
    @Getter
    private final LogMessageService logMessageService;

    private final Class<ENTITY> clazz;

    private final Map<String, AbstractJob> jobTemplates;

    @Autowired
    private TransactionHandler transactionHandler;

    /**
     * This map contains all currently running jobs. If the job finishes, it gets removed.
     */
    private final ConcurrentHashMap<Pair<UUID, SchedulerJobMode>, Pair<AbstractJob, Future<?>>> schedules;

    private final String activeProfile;

    public AbstractSchedulerService(REPO repository,
                                    AbstractSchedulerJobTerminationRepository terminationRepository,
                                    MAPPER mapper,
                                    ApplicationContext context,
                                    LogMessageService logMessageService,
                                    ThreadPoolTaskScheduler executor,
                                    Class<ENTITY> clazz,
                                    Environment environment) {
        this.repository = repository;
        this.terminationRepository = terminationRepository;
        this.mapper = mapper;
        this.context = context;
        this.executor = executor;
        this.logMessageService = logMessageService;
        this.clazz = clazz;
        activeProfile = Util.getActiveProfile(environment);
        this.schedules = new ConcurrentHashMap<>();
        this.jobTemplates = new HashMap<>();
    }

    /**
     * As also supporting clustered mode, it cannot be ensured that the termination call is triggered from the node where
     * the scheduled job is assigned to. Thus, we have a decoupling via the database using the entity {@link AbstractSchedulerJobTermination}.
     * The {@link #terminateJobSchedule(UUID, SchedulerJobMode, boolean)} stores the job to terminate in the database and this cron method
     * terminates the job on the correct cluster node.
     */
    public void terminateJobSchedules() {
        // terminate the jobs stored in the database
        List<AbstractSchedulerJobTermination> jobs = transactionHandler.runInNewTransactionReadOnly(() -> terminationRepository.findByActiveProfile(activeProfile));
        if (CollectionUtils.isEmpty(jobs)) {
            log.info("terminateJobSchedules: called, no jobs found in scheduler termination tables");
            return;
        }
        log.info("terminateJobSchedules: called, [" + jobs.size() + "] jobs found in scheduler termination tables");


        // List of terminated jobs that should be deleted
        List<AbstractSchedulerJobTermination> tobeDeleted = new ArrayList<>();

        jobs.forEach(job -> {

            // the id of the job in the scheduler job table
            UUID jobId = job.getJobId();
            if (isNull(jobId)) {
                log.info("terminateJobSchedules: jobId for job termination entry [" + job.getId() + "] not found. Skipping..");
            } else {
                SchedulerJobMode mode = job.getMode();
                boolean interruptIfRunning = job.isTerminateImmediate();

                // minimize the concurrency check by using copies of the schedules and futures map

                // remove job from the schedules
                Set<Map.Entry<Pair<UUID, SchedulerJobMode>, Pair<AbstractJob, Future<?>>>> schedulesCopy = new HashSet<>(schedules.entrySet());
                Optional<Map.Entry<Pair<UUID, SchedulerJobMode>, Pair<AbstractJob, Future<?>>>> scheduleEntryOpt = schedulesCopy.stream()
                        .filter(entry -> entry.getKey().equals(Pair.of(jobId, mode)))
                        .findFirst();

                scheduleEntryOpt.map(Map.Entry::getValue).ifPresent(entry -> {
                    AbstractJob abstractJob = entry.getLeft();
                    Future<?> future = entry.getRight();
                    log.info("terminateJobSchedules: schedule for job id [" + jobId + "] found in current profile. Start removing..");

                    if (nonNull(abstractJob) && !abstractJob.terminate(interruptIfRunning)) {
                        log.error("terminateJobSchedules: Job termination failed. Please check manually job status in database.");
                    }
                    if (nonNull(future)) {
                        boolean isFutureCanceled = future.cancel(interruptIfRunning);
                        if (isFutureCanceled) {
                            log.info("terminateJobSchedules: future schedule for job id [" + jobId + "] successfully cancelled");
                        } else {
                            log.error("terminateJobSchedules: Job process cancellation failed. Please check manually if process terminates");
                        }
                    }
                    schedules.remove(Pair.of(jobId, SchedulerJobMode.CRON));
                });

                // clean up database as we processed this job
                if (job.isOneTimeJob()) {
                    tobeDeleted.add(job);
                }
            }
        });

        // clean up database as we processed this job
        int size = tobeDeleted.size();
        log.info("terminateJobSchedules: starting deleting [" + size + "] job schedules from the database");
        Set<UUID> jobTerminationIdsToDelete = new HashSet<>();
        Set<UUID> schedulerJobIdsToDeleted = new HashSet<>();
        /*try {
            jobTerminationIdsToDelete = tobeDeleted.stream().map(AbstractSchedulerJobTermination::getId).collect(Collectors.toSet());
            terminationRepository.deleteAllById(jobTerminationIdsToDelete);
            schedulerJobIdsToDeleted = tobeDeleted.stream().map(AbstractSchedulerJobTermination::getJobId).collect(Collectors.toSet());
            repository.deleteAllById(schedulerJobIdsToDeleted);
        } catch (Exception e) {
            log.warn("terminateJobSchedules: could not delete job schedules as it was already deleted: " + e.getMessage());
        }*/
        tobeDeleted.stream().parallel().forEach(jobTerminationIdToDelete ->
        {
            try {
                transactionHandler.runInNewTransaction(() -> {
                    terminationRepository.deleteById(jobTerminationIdToDelete.getId());
                    repository.deleteById(jobTerminationIdToDelete.getJobId());
                    return null;
                });
                log.info("terminateJobSchedules: for jobId [" + jobTerminationIdToDelete.getJobId() + "] done");
            } catch (Exception e) {
                log.error("terminateJobSchedules: for jobId [" + jobTerminationIdToDelete.getJobId() + "] failed", e);
            }
        });
        log.info("terminateJobSchedules: [count=" + size + "] job termination ids and [count=" + size + "] scheduler job ids deleted successfully");

        /*log.info("terminateJobSchedules: [count=" + jobTerminationIdsToDelete.size()
                + "] job termination ids and [count=" + schedulerJobIdsToDeleted.size()
                + "] scheduler job ids deleted successfully");*/
    }

    /**
     * Main method for starting the job scheduler. It scans the classpath by the given reflection object and registers
     * and schedules (in case of cron jobs) the found jobs. This method should be called on start up of the application.
     *
     * @param reflections the reflections object providing the classpath to scan
     * @throws SchedulingException in case scheduling fails
     */
    public void initJobs(Reflections reflections) throws SchedulingException {
        log.info("Scanning for unknown jobs and registering them in database");
        scanForJobs(reflections, activeProfile);

        log.info("Resetting jobs in state running during last shutdown");
        List<ENTITY> toBeStopped = this.repository.findByStatusInAndActiveProfile
                        (Set.of(SchedulerJobStatus.RUNNING, SchedulerJobStatus.STOPPING), activeProfile).stream()
                .peek(schedulerJob -> schedulerJob.setStatus(SchedulerJobStatus.STOPPED)).collect(toList());

        if (CollectionUtils.isNotEmpty(toBeStopped)) {
            repository.saveAll(toBeStopped);
        }
        refreshSchedules(true, activeProfile);
    }

    /**
     * Hardly terminates all jobs, no matter if running or not.
     *
     * @param interruptIfRunning if a job is running, if it should be tried to terminate the job
     */
    public void terminateAllSchedules(boolean interruptIfRunning) {
        doTerminateAllSchedules(interruptIfRunning);
    }

    /**
     * Stops and reschedules all jobs.
     *
     * @param interruptIfRunning if a job is running, if it should be tried to terminate the job
     * @throws SchedulingException if scheduling fails
     */
    public void refreshSchedules(boolean interruptIfRunning) throws SchedulingException {
        refreshSchedules(interruptIfRunning, activeProfile);
    }

    public void terminateJobSchedule(String jobId, SchedulerJobMode mode, boolean interruptIfRunning) throws FormatException {
        terminateJobSchedule(Util.convertId(jobId), mode, interruptIfRunning);
    }

    public boolean runJob(@NotNull String id) throws SchedulingException, FormatException {
        return doRunJob(getSchedulerJob(id), SchedulerJobMode.MANUAL, Collections.emptyMap());
    }

    public boolean runJob(@NotNull String id, Map<String, String> params) throws SchedulingException, FormatException {
        return doRunJob(getSchedulerJob(id), SchedulerJobMode.MANUAL, params);
    }

    public SchedulerJobDto save(@NotNull SchedulerJobDto dto, boolean interruptIfRunning) throws SchedulingException, FormatException {
        validate(dto);
        // stop job in all possible modes to avoid duplications
        terminateJobSchedule(Util.convertId(dto.getId()), dto.getMode(), interruptIfRunning);
        ENTITY entity = repository.save(mapper.mapToEntity(dto));
        scheduleCronJob(entity);
        return mapper.mapToDto(entity);
    }

    /**
     * Terminate a job by id. The termination is an indirection storing the termination order in the database. The cron
     * method {@link #terminateJobSchedules()} cyclic picks up the records from the database and then terminates the job.
     *
     * @param jobId              the job to terminate
     * @param mode               the mode where the job shall be stopped
     * @param interruptIfRunning the job should be terminated if it is currently running
     */
    public void terminateJobSchedule(UUID jobId, SchedulerJobMode mode, boolean interruptIfRunning) {
        if (jobId == null) {
            return;
        }
        repository.findById(jobId).ifPresent(job -> terminationRepository.save(new AbstractSchedulerJobTermination(jobId, mode, job.getActiveProfile(), job.isOneTimeJob(), interruptIfRunning)));
    }

    public void delete(@NotNull UUID id) throws SchedulingException, FormatException {
        repository.deleteById(id);
    }

    public Optional<SchedulerJobDto> findJobByName(String name) {
        return getSchedulerJobByName(name).map(mapper::mapToDto);
    }

    public SchedulerJobDto getJob(@NotNull String id) throws SchedulingException, FormatException {
        return mapper.mapToDto(getSchedulerJob(id));
    }

    public List<SchedulerJobDto> getRunningJobs() {

        HashMap<Pair<UUID, SchedulerJobMode>, Pair<AbstractJob, Future<?>>> currentSchedules = new HashMap<>(schedules);
        return currentSchedules.keySet().stream()
                .map(pair -> {
                    try {
                        ENTITY entity = getSchedulerJob(pair.getKey().toString());
                        SchedulerJobDto dto = mapper.mapToDto(entity);
                        dto.setMode(pair.getRight());
                        return dto;
                    } catch (SchedulingException | FormatException e) {
                        logMessageService.logException("getRunningJobs", "Scheduler job " + pair.getKey().toString() + " (running mode=" + pair.getRight() + ") not found, skipping..", e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(toList());
    }

    public boolean isJobRunning(String id) throws FormatException {
        return repository.findById(Util.convertId(id)).map(entity -> entity.getStatus() == SchedulerJobStatus.RUNNING).orElse(false);
    }

    public List<SchedulerJobDto> getAllSchedulerJobs() {
        return Util.toStream(repository.findAll()).map(mapper::mapToDto).collect(toList());
    }

    @Transactional(readOnly = true)
    public ENTITY getSchedulerJob(String id) throws SchedulingException, FormatException {
        Optional<ENTITY> optional = repository.findById(Util.convertId(id));
        if (optional.isEmpty()) {
            throw new SchedulingException(SchedulingException.Mode.GET, "Job [id=" + id + "] could not be retrieved. Skipping execution", id);
        }
        return optional.get();
    }

    public Optional<ENTITY> getSchedulerJobByNameAndParameters(@NotNull String name, @NotNull Map<String, String> parameters) {
        return getSchedulerJobByNameAndParameterStartsWith(name, Util.convertToString(parameters));
    }

    public Optional<ENTITY> getSchedulerJobByNameAndParameterStartsWith(@NotNull String name, @NotNull String parameters) {
        return repository.findFirstByParametersStartingWithAndNameOrderByCreatedAtDesc(parameters, name);
    }

    public boolean isDependencyRunning(AbstractSchedulerJobEntity job) {
        String dependencyJob = job.getParameter(ENTITY.PARAM_DEPENDENCY);
        boolean waitForDependency = job.getParameterAsBoolean(ENTITY.PARAM_WAIT_FOR_DEPENDENCY);
        if (StringUtils.isEmpty(dependencyJob)) return false;
        AtomicBoolean done = new AtomicBoolean(true);
        Arrays.stream(dependencyJob.split(",")).forEach(jobName -> {
            Optional<ENTITY> childJob = getSchedulerJobByName(jobName);
            done.set(done.get() || childJob.isEmpty());
            childJob.ifPresent(adminSchedulerJob -> done.set(done.get() && isChildJobRunning(waitForDependency, adminSchedulerJob)));
        });
        return !done.get();
    }

    public Map<String, String> getJobParameters(String id) throws SchedulingException, FormatException {
        return getSchedulerJob(id).getParameterMap();
    }

    public String getParameter(String id, String name) throws SchedulingException, FormatException {
        return getSchedulerJob(id).getParameter(name);
    }

    public boolean getParameterAsBoolean(String id, String name) throws SchedulingException, FormatException {
        return getSchedulerJob(id).getParameterAsBoolean(name);
    }

    public Integer getParameterAsInteger(String id, String name) throws SchedulingException, FormatException {
        return getSchedulerJob(id).getParameterAsInteger(name);
    }

    /*
     * Callback services for job execution called by AbstractJob
     */

    public void finished(UUID jobId, SchedulerJobMode mode) throws SchedulingException, FormatException {
        // remove future
        ENTITY job = repository.findByIdAndDeletedIgnored(jobId)
                .orElseThrow(() -> new SchedulingException(SchedulingException.Mode.FINISHED, "Job [id=" + jobId + "] could not be retrieved from database. Bad configuration, manual cleanup action required."));

        schedules.remove(Pair.of(job.getId(), mode));

        // run followed by
        String followedByJob = job.getParameter(AdminSchedulerJob.PARAM_FOLLOWED_BY);
        if (StringUtils.isNotEmpty(followedByJob)) {
            repository.findFirstByName(followedByJob).ifPresent(childJob -> {
                try {
                    doRunJob(childJob, SchedulerJobMode.FOLLOW, Collections.emptyMap());
                } catch (SchedulingException e) {
                    Util.sneakyThrow(e);
                }
            });
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void success(AbstractJob runnable) {
        ENTITY job = getSchedulerJob(runnable.getId());
        ENTITY successJobEntity = getSuccessJobEntity(job);
        repository.save(successJobEntity);
        if (job.isOneTimeJob()) {
            repository.delete(successJobEntity);
        }
        logMessageService.logInfo(job.getName(), "End of job " + job.getName() + " at " + LocalDateTime.now());
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void running(AbstractJob runnable) {
        SchedulerJobMode mode = runnable.getMode();
        ENTITY job = getSchedulerJob(runnable.getId());

        Pair<UUID, SchedulerJobMode> key = Pair.of(job.getId(), mode);
        saveRunnable(runnable, key);

        LocalDateTime now = LocalDateTime.now();
        log.info("Starting job " + job.getName() + " at " + now);

        repository.save(getRunningJobEntity(job));
        logMessageService.logInfo(job.getName(), "Starting job " + job.getName() + " at " + now);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void failure(AbstractJob runnable, Throwable e) {
        //noinspection DuplicatedCode
        ENTITY job = getSchedulerJob(runnable.getId());
        LocalDateTime now = LocalDateTime.now();
        int duration = getDuration(job, now);
        log.error("Failure of job " + job.getName() + " at " + now + " after " + duration + " sec", e);

        ENTITY failureJobEntity = getFailureJobEntity(job, e, duration);
        repository.save(failureJobEntity);
        if (failureJobEntity.isOneTimeJob()) {
            repository.delete(failureJobEntity);
        }
        logMessageService.logException(job.getName(), "Failure of job " + job.getName() + " at " + now, e);
    }

    /*
     * Protected service methods for only derived scheduler services
     */
    protected Optional<ENTITY> getOneTimeJobByNameIncludingDeleted(String name) {
        return repository.findFirstByNameIncludingDeleted(name);
    }

    protected Optional<UUID> getSchedulerJobByNameAndParametersContain(String name, Map<String, String> parameters) {
        Optional<String> jobIdOptional = repository.findJobIdByParametersContainsAndName(Util.convertToString(parameters), name);
        return jobIdOptional.map(UUID::fromString);
    }

    protected boolean scheduleOneTimeJobs(ENTITY initialJob, List<Pair<LocalDateTime, Map<String, String>>> executionTimesAndParameters) {
        List<ENTITY> oneTimeJobs = new ArrayList<>();
        AtomicBoolean success = new AtomicBoolean(true);

        executionTimesAndParameters.forEach(entry -> {
            String cronExpression = Util.toCronExpression(entry.getLeft());
            Map<String, String> parameters = entry.getRight();

            ENTITY job = getParameterizedInstanceForOneTimeJob(initialJob.getName(), initialJob.getDescription(),
                    initialJob.getMode(), initialJob.getJobClass(), activeProfile);

            if (job != null) {
                job.setCronExpression(cronExpression);
                job.setParameterMap(parameters);
                oneTimeJobs.add(job);
            }
        });

        Iterable<ENTITY> result = repository.saveAll(oneTimeJobs);
        Util.toStream(result).forEach(job -> {
            try {
                scheduleCronJob(job);
            } catch (SchedulingException e) {
                logMessageService.logException("Cannot schedule cron job " + initialJob.getName(), e);
                log.error("Cannot schedule cron job " + initialJob.getName(), e);
                success.set(false);
            }
        });
        return success.get();
    }

    protected boolean isNotCronJob(ENTITY job) {
        if (job.getMode() != SchedulerJobMode.CRON) {
            log.warn("Job " + job + " scheduling mode is not cron. Skipping scheduling.");
            return true;
        }
        return false;
    }

    /*
     * Internal service methods for execution and bean management
     */

    private Optional<ENTITY> getSchedulerJobByName(@NotNull String name) {
        return repository.findFirstByName(name);
    }

    private void validate(SchedulerJobDto dto) throws SchedulingException {
        if (dto.getMode() == SchedulerJobMode.CRON && (StringUtils.isEmpty(dto.getCronExpression()) || !CronExpression.isValidExpression(dto.getCronExpression()))) {
            throw new SchedulingException(SchedulingException.Mode.VALIDATE, "Job " + dto + " is configured to run as cron job but has no valid cron expression");
        }
        try {
            getAbstractJob(dto.getJobClass());
        } catch (ClassNotFoundException e) {
            throw new SchedulingException(SchedulingException.Mode.VALIDATE, "Job class does not exist for job " + dto);
        }
        if (StringUtils.isEmpty(dto.getId()) && getSchedulerJobByName(dto.getName()).isPresent()) {
            throw new SchedulingException(SchedulingException.Mode.VALIDATE, "A job with the same name already exists. Cannot store job " + dto);
        }
        if (!Util.isValidUUID(dto.getId())) {
            throw new SchedulingException(SchedulingException.Mode.VALIDATE, "ID of job is not in valid UUID format. Cannot store job " + dto);
        }
    }

    private boolean doRunJob(ENTITY entity, SchedulerJobMode mode, Map<String, String> params) throws SchedulingException {
        Pair<UUID, SchedulerJobMode> key = Pair.of(entity.getId(), mode);
        if (schedules.containsKey(key)) {
            log.info("Job " + entity.getName() + " already scheduled. Do not schedule it again.");
            return false;
        }
        AbstractJob runnable = getRunnable(entity, mode);
        runnable.setAdditionalParams(params);
        Future<?> future = executor.submit(runnable);

        saveFuture(key, future);

        log.info("Scheduled immediate execution of entity " + entity.getName());
        return true;
    }

    private void scheduleCronJob(ENTITY entity) throws SchedulingException {
        if (isSchedulingDisabled(entity)) {
            return;
        }

        log.debug("Scheduling entity " + entity.getName() + " [" + entity.getJobClass() + "] to " + entity.getCronExpression());
        AbstractJob runnable = getRunnable(entity, SchedulerJobMode.CRON);
        ScheduledFuture<?> future = executor.schedule(runnable, new CronTrigger(entity.getCronExpression()));
        if (future == null) {
            throw new SchedulingException(SchedulingException.Mode.GET, "Cannot get future of job " + entity.getName() + " for cron expression " + entity.getCronExpression());
        }
        Pair<UUID, SchedulerJobMode> key = Pair.of(entity.getId(), SchedulerJobMode.CRON);
        saveFuture(key, future);
    }

    private void saveRunnable(AbstractJob runnable, Pair<UUID, SchedulerJobMode> key) {
        Pair<AbstractJob, Future<?>> existing = schedules.getOrDefault(key, Pair.of(null, null));
        Pair<AbstractJob, Future<?>> newOne = Pair.of(runnable, existing.getRight());
        schedules.put(key, newOne);
    }

    private void saveFuture(Pair<UUID, SchedulerJobMode> key, Future<?> future) {
        Pair<AbstractJob, Future<?>> existing = schedules.getOrDefault(key, Pair.of(null, null));
        Pair<AbstractJob, Future<?>> newOne = Pair.of(existing.getLeft(), future);
        schedules.put(key, newOne);
    }

    private AbstractJob getRunnable(ENTITY entity, SchedulerJobMode mode) throws SchedulingException {
        try {
            AbstractJob job = getAbstractJob(entity.getJobClass());
            job.initialize(entity.getId(), this, mode);
            return job;
        } catch (BeansException | ClassNotFoundException e) {
            throw new SchedulingException(SchedulingException.Mode.VALIDATE, "Could not create entity bean for job " + entity.getName() + e.getMessage(), e);
        }
    }

    private void scanForJobs(Reflections reflections, String activeProfile) {
        List<ENTITY> jobs = Util.toStream(reflections.getTypesAnnotatedWith(ScheduledJob.class))
                .filter(clazz -> !repository.existsByName(getJobName(clazz)))
                .map(clazz -> getInitialJobEntity(clazz, Objects.requireNonNull(getInstance())))
                .toList();
        jobs.forEach(job -> job.setActiveProfile(activeProfile));

        if (!jobs.isEmpty()) {
            repository.saveAll(jobs);
        }
        log.info("Created " + jobs.size() + " jobs");
    }

    private ENTITY getSchedulerJob(UUID id) {
        return repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Job [id=" + id + "] could not be retrieved from database. Bad configuration, manual cleanup action required."));
    }

    private boolean isChildJobRunning(boolean waitForDependency, ENTITY childJob) {
        boolean ok = (childJob.getStatus() == SchedulerJobStatus.STOPPED || childJob.getStatus() == SchedulerJobStatus.DISABLED)
                && childJob.getLastRunResult() == SchedulerJobResult.SUCCESS;
        if (!waitForDependency) {
            ok = ok || childJob.getStatus() == SchedulerJobStatus.RUNNING;
        }
        return ok;
    }

    private ENTITY getSuccessJobEntity(ENTITY job) {
        //noinspection DuplicatedCode
        LocalDateTime now = LocalDateTime.now();
        int duration = getDuration(job, now);
        log.info("Successful job execution " + job.getName() + " took " + duration + " sec");
        job.setTimeLastExecution(now);
        job.setTimeLastSuccess(now);
        job.setTimeFinished(now);
        job.setLastRunResult(SchedulerJobResult.SUCCESS);
        job.setStatus(SchedulerJobStatus.STOPPED);
        job.setLastRunDuration(duration);
        job.setLastRunMessage(null);
        return job;
    }

    private ENTITY getRunningJobEntity(ENTITY job) {
        job.setStatus(SchedulerJobStatus.RUNNING);
        job.setTimeFinished(null);
        job.setTimeStarted(LocalDateTime.now());
        return job;
    }

    private ENTITY getFailureJobEntity(ENTITY job, Throwable e, int duration) {
        job.setLastRunMessage(e.getMessage());
        job.setTimeLastExecution(LocalDateTime.now());
        job.setTimeFinished(LocalDateTime.now());
        job.setLastRunResult(SchedulerJobResult.FAILURE);
        job.setStatus(SchedulerJobStatus.STOPPED);
        job.setLastRunDuration(duration);
        return job;
    }



    /*
     * Internal service methods for execution and bean management
     */

    private void doTerminateAllSchedules(boolean interruptIfRunning) {
        schedules.forEach((key, value) -> terminateJobSchedule(key.getKey(), key.getValue(), interruptIfRunning));
    }

    private synchronized void refreshSchedules(boolean interruptIfRunning, String activeProfile) throws SchedulingException {
        doTerminateAllSchedules(interruptIfRunning);
        if (!repository.existsByModeAndCronExpressionNotNullAndActiveProfile(SchedulerJobMode.CRON, activeProfile)) {
            return;
        }

        // clean up expired one time jobs
        List<ENTITY> cronJobs = transactionHandler.runInNewTransactionReadOnly(() -> this.repository.findByModeAndCronExpressionNotNullAndActiveProfile(SchedulerJobMode.CRON, activeProfile));
        List<ENTITY> expiredOneTimeJobsToDelete = new ArrayList<>();
        List<ENTITY> expiredOneTimeJobsToRerun = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        // sort jobs in 2 classes: to be rerun as they are not expired that long and to be deleted as they are too old
        // for the too old ones other mechanism like a consistency job have to do the functional clean up
        cronJobs.stream()
                .filter(AbstractSchedulerJobEntity::isOneTimeJob)
                .forEach(job -> {

                    try {
                        if (!jobTemplates.containsKey(job.getName())) {
                            jobTemplates.put(job.getName(), getAbstractJob(job.getJobClass()));
                        }
                        AbstractJob jobTemplate = jobTemplates.get(job.getName());
                        LocalDateTime nextRun = Util.getNextCronExecutionTime(job.getCronExpression(), now);
                        LocalDateTime pastRun = Util.getNextCronExecutionTime(job.getCronExpression(), now.minus(jobTemplate.getMaxAge()));
                        if (nextRun.equals(pastRun)) {
                            pastRun = pastRun.minusYears(1);
                        }

                        /*
                         * possible timeline
                         *
                         *   outdated, delete     outdated, rerun    now   normal run        should not happen
                         * <--------------------|--------------------|||------------------|----------------------->
                         *                      [   maxRunDelay       |   maxAge          ]
                         */

                        //noinspection StatementWithEmptyBody
                        if (now.isBefore(nextRun) && now.plus(jobTemplate.getMaxAge()).isAfter(nextRun)) {
                            // job is fine in schedule, nothing to do
                        } else if (now.minus(jobTemplate.getMaxRunDelay()).isBefore(pastRun) && pastRun.isBefore(now)) {
                            expiredOneTimeJobsToRerun.add(job);
                        } else if (now.minus(jobTemplate.getMaxRunDelay()).isAfter(pastRun)) {
                            expiredOneTimeJobsToDelete.add(job);
                        } else {
                            String msg = "Unknown job schedule for id=" + job.getId() + ": pastRun=" + pastRun + ", nextRun=" + nextRun;
                            log.warn(msg);
                            logMessageService.logWarning("AbstractSchedulerService::refreshSchedules", msg);
                        }

                    } catch (ClassNotFoundException e) {
                        throw new RuntimeException("Non-existing job class used for scheduling. Immediately terminating, please clean up database or repair classpath", e);
                    } catch (FormatException e) {
                        throw new RuntimeException("job " + job.getId() + " has invalid cron expression [" + job.getCronExpression() + "]. Immediately terminating, please correct in database", e);
                    }
                });

        log.info("refreshSchedules: expiredOneTimeJobsToDelete count = " + expiredOneTimeJobsToDelete.size());
        log.info("refreshSchedules: expiredOneTimeJobsToRerun count = " + expiredOneTimeJobsToRerun.size());

        // rerun outdated jobs
        if (CollectionUtils.isNotEmpty(expiredOneTimeJobsToRerun)) {
            expiredOneTimeJobsToRerun.sort((o1, o2) -> {
                try {
                    return Util.getNextCronExecutionTime(o1.getCronExpression()).compareTo(Util.getNextCronExecutionTime(o2.getCronExpression()));
                } catch (FormatException e) {
                    throw new RuntimeException(e);
                }
            });
            expiredOneTimeJobsToRerun.forEach(job -> {
                try {
                    runJob(job.getId().toString());
                } catch (SchedulingException | FormatException e) {
                    log.error("Error running expired job", e);
                }
            });
        }

        // delete the too old ones
        if (CollectionUtils.isNotEmpty(expiredOneTimeJobsToDelete)) {
            repository.deleteAll(expiredOneTimeJobsToDelete);
        }

        // re-get the jobs that remain to be scheduled in the future.
        cronJobs = this.repository.findByModeAndCronExpressionNotNullAndActiveProfile(SchedulerJobMode.CRON, activeProfile);
        log.info("Scheduling " + cronJobs.size() + " cron jobs");
        for (ENTITY schedulerJob : cronJobs) {
            scheduleCronJob(schedulerJob);
        }
    }

    // Job scheduling temporary deactivated
    //@Scheduled(initialDelay = 5, fixedRate = 2, timeUnit = TimeUnit.MINUTES)
    public void scanForNotRunJobs() {
        log.info("Scan for not run jobs started");
        if (!repository.existsByModeAndCronExpressionNotNullAndActiveProfile(SchedulerJobMode.CRON, activeProfile)) {
            return;
        }

        // clean up expired one time jobs
        List<ENTITY> cronJobs = transactionHandler.runInNewTransactionReadOnly(() -> this.repository.findByModeAndCronExpressionNotNullAndActiveProfile(SchedulerJobMode.CRON, activeProfile));
        List<ENTITY> expiredOneTimeJobsToDelete = new ArrayList<>();
        List<ENTITY> expiredOneTimeJobsToRerun = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        // sort jobs in 2 classes: to be rerun as they are not expired that long and to be deleted as they are too old
        // for the too old ones other mechanism like a consistency job have to do the functional clean up
        cronJobs.stream()
                .filter(AbstractSchedulerJobEntity::isOneTimeJob)
                .forEach(job -> {

                    try {
                        if (!jobTemplates.containsKey(job.getName())) {
                            jobTemplates.put(job.getName(), getAbstractJob(job.getJobClass()));
                        }
                        AbstractJob jobTemplate = jobTemplates.get(job.getName());
                        LocalDateTime nextRun = Util.getNextCronExecutionTime(job.getCronExpression(), now);
                        LocalDateTime pastRun = Util.getNextCronExecutionTime(job.getCronExpression(), now.minus(jobTemplate.getMaxAge()));
                        if (nextRun.equals(pastRun)) {
                            pastRun = pastRun.minusYears(1);
                        }

                        /*
                         * possible timeline
                         *
                         *   outdated, delete     outdated, rerun    now   normal run        should not happen
                         * <--------------------|--------------------|||------------------|----------------------->
                         *                      [   maxRunDelay       |   maxAge          ]
                         */

                        //noinspection StatementWithEmptyBody
                        if (now.isBefore(nextRun) && now.plus(jobTemplate.getMaxAge()).isAfter(nextRun)) {
                            // job is fine in schedule, nothing to do
                        } else if (now.minus(jobTemplate.getMaxRunDelay()).isBefore(pastRun) && pastRun.isBefore(now)) {
                            expiredOneTimeJobsToRerun.add(job);
                        } else if (now.minus(jobTemplate.getMaxRunDelay()).isAfter(pastRun)) {
                            expiredOneTimeJobsToDelete.add(job);
                        } else {
                            String msg = "Unknown job schedule for id=" + job.getId() + ": pastRun=" + pastRun + ", nextRun=" + nextRun;
                            log.warn(msg);
                            logMessageService.logWarning("AbstractSchedulerService::refreshSchedules", msg);
                        }

                    } catch (ClassNotFoundException e) {
                        throw new RuntimeException("Non-existing job class used for scheduling. Immediately terminating, please clean up database or repair classpath", e);
                    } catch (FormatException e) {
                        throw new RuntimeException("job " + job.getId() + " has invalid cron expression [" + job.getCronExpression() + "]. Immediately terminating, please correct in database", e);
                    }
                });
        log.info("scanForNotRunJobs: expiredOneTimeJobsToDelete count = " + expiredOneTimeJobsToDelete.size());
        log.info("scanForNotRunJobs: expiredOneTimeJobsToRerun count = " + expiredOneTimeJobsToRerun.size());

        // rerun outdated jobs
        if (CollectionUtils.isNotEmpty(expiredOneTimeJobsToRerun)) {
            expiredOneTimeJobsToRerun.sort((o1, o2) -> {
                try {
                    return Util.getNextCronExecutionTime(o1.getCronExpression()).compareTo(Util.getNextCronExecutionTime(o2.getCronExpression()));
                } catch (FormatException e) {
                    throw new RuntimeException(e);
                }
            });
            expiredOneTimeJobsToRerun.forEach(job -> {
                try {
                    runJob(job.getId().toString());
                } catch (SchedulingException | FormatException e) {
                    log.error("Error running expired job", e);
                }
            });
        }

        // delete the too old ones
        if (CollectionUtils.isNotEmpty(expiredOneTimeJobsToDelete)) {
            repository.deleteAll(expiredOneTimeJobsToDelete);
        }
    }

    private AbstractJob getAbstractJob(String clazz) throws ClassNotFoundException {
        return (AbstractJob) this.context.getBean(Class.forName(clazz));
    }

    private <JOB_ENTITY extends AbstractSchedulerJobEntity> int getDuration(JOB_ENTITY job, LocalDateTime now) {
        return job.getTimeStarted() == null ? -1 : Math.toIntExact(job.getTimeStarted().until(now, ChronoUnit.SECONDS));
    }

    private String getJobName(Class<?> clazz) {
        String name = clazz.getAnnotation(ScheduledJob.class).name();
        if (StringUtils.isEmpty(name)) {
            name = clazz.getSimpleName();
        }
        return name;
    }

    private ENTITY getInitialJobEntity(Class<?> clazz, ENTITY job) {
        job.setName(getJobName(clazz));
        job.setDescription(clazz.getAnnotation(ScheduledJob.class).description());
        job.setStatus(SchedulerJobStatus.STOPPED);
        job.setLastRunResult(SchedulerJobResult.NONE);
        job.setMode(clazz.getAnnotation(ScheduledJob.class).mode());
        if (StringUtils.isNotEmpty(clazz.getAnnotation(ScheduledJob.class).cronExpression())) {
            job.setCronExpression(clazz.getAnnotation(ScheduledJob.class).cronExpression());
        }
        if (StringUtils.isNotEmpty(clazz.getAnnotation(ScheduledJob.class).parameters())) {
            job.setParameters(clazz.getAnnotation(ScheduledJob.class).parameters());
        }
        job.setOneTimeJob(clazz.getAnnotation(ScheduledJob.class).oneTimeJob());
        job.setJobClass(clazz.getName());
        return job;
    }

    private ENTITY getInstance() {
        return getInstance(clazz);
    }

    private <T> T getInstance(Class<T> clazz) {
        try {
            return clazz.getDeclaredConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            Util.sneakyThrow(e);
            return null;
        }
    }

    private ENTITY getParameterizedInstanceForOneTimeJob(String name, String description, SchedulerJobMode mode, String jobClass, String activeProfile) {
        try {
            return clazz.getDeclaredConstructor(String.class, String.class, SchedulerJobMode.class, SchedulerJobStatus.class, SchedulerJobResult.class, String.class, boolean.class, String.class)
                    .newInstance(name, description, mode, SchedulerJobStatus.STOPPED, SchedulerJobResult.NONE, jobClass, true, activeProfile);
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            Util.sneakyThrow(e);
            return null;
        }
    }

    private boolean isSchedulingDisabled(ENTITY job) {
        if (isValidCronJob(job) && job.isOneTimeJob() && !job.getLastRunResult().equals(SchedulerJobResult.NONE)) {
            log.warn("Job " + job + " has already run. This job is configured to run only once. Skipping scheduling.");
            return true;
        }
        return !isValidCronJob(job);
    }

    private boolean isValidCronJob(ENTITY job) {
        if (job.getMode() != SchedulerJobMode.CRON || StringUtils.isEmpty(job.getCronExpression())) {
            log.warn("Job " + job + " is not configured as cron job. Skipping scheduling.");
            return false;
        }
        return true;
    }

    public boolean existsByNameAndParameters(String name, String parameters) {
        return repository.existsByNameAndParameters(name, parameters);
    }

    public List<HmJobCronExpressionDO> getOpenJobsByRoundResultJobNameAndRoundId(String name, String parameters) {
        return repository.findCronExpressionsByOpenJobAndJobNameAndParameters(name, parameters);
    }

    public List<HmJobCronExpressionDO> getCronExpressionsByJobName(String name) {
        return repository.findCronExpressionsByJobName(name);
    }

    public List<SchedulerJobDto> getByNameAndParametersLike(String name, String parameters) {
        return repository.findByJobNameAndParametersLike(name, parameters).stream().map(mapper::mapToDto).toList();
    }

    public int deleteEndedJobs(List<UUID> jobIds) {
        return repository.deleteEndedJobs(jobIds);
    }

    public List<String> findEndedJobIds(int limit) {
        return repository.findEndedJobIds(limit);
    }
}
