package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.UUID;

@Table(name = "team", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@Entity
@ToString
@SQLDelete(sql = "UPDATE hm.team SET deleted = true, \"left\" = now(), deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmTeam extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false, cascade = CascadeType.REFRESH)
    @JoinColumn(name = "owner_id", referencedColumnName = "id", updatable = false)
    private HmUserProfile owner;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false, cascade = CascadeType.REFRESH)
    @JoinColumn(name = "league_id", referencedColumnName = "id", updatable = false)
    private HmLeague league;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false, cascade = CascadeType.REFRESH)
    @JoinColumn(name = "player_id", referencedColumnName = "id", updatable = false)
    private HmPlayer player;

    @NotNull
    @ToString.Exclude
    @Column(name = "joined")
    private LocalDateTime joined;

    @ToString.Exclude
    @Column(name = "`left`")
    private LocalDateTime left;

    public HmTeam(HmUserProfile owner, HmLeague league, HmPlayer player, LocalDateTime joined) {
        this.owner = owner;
        this.league = league;
        this.player = player;
        this.joined = joined;
    }
}
