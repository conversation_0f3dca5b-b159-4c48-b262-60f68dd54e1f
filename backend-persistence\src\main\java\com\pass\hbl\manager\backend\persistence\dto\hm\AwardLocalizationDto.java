package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Getter
@Setter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonRootName("AwardLocalization")
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Award descriptions by localization")
public class AwardLocalizationDto {

    @NotBlank
    @Size(max = 1024)
    @Schema(description = "name of the award", example = "league winner")
    public String name;

    @NotBlank
    @Size(max = 1024)
    @Schema(description = "description of the award", example = "league winner is the user with the highest score in the league")
    public String description;
}
