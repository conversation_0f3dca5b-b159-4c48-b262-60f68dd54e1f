package com.pass.hbl.manager.backend.restservice.job;

import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobMode;
import com.pass.hbl.manager.backend.persistence.job.ScheduledJob;
import com.pass.hbl.manager.backend.persistence.job.hm.HmAbstractJob;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.hm.UserProfileService;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.temporal.ChronoUnit;

import static com.pass.hbl.manager.backend.persistence.util.Constants.*;

@Slf4j
@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@ScheduledJob(description = "Handles the results of SSO Id Validation", mode = SchedulerJobMode.CRON, cronExpression = "0 0 2 ? * *")
public class SsoValidationJob extends HmAbstractJob {

    private final UserProfileService userProfileService;
    private final Environment environment;

    public SsoValidationJob(ParameterService parameterService, UserProfileService userProfileService, Environment environment) {
        super(parameterService);
        this.userProfileService = userProfileService;
        this.environment = environment;
    }

    public Duration getMaxRunDelay() {
        return Duration.of(2, ChronoUnit.HOURS);
    }

    @Override
    protected void work() throws Exception {
        String activeProfile = Util.getActiveProfile(environment);
        boolean isSsoValidationEnabled = Util.equalsOneOf(activeProfile, STAGE_PROFILE, STAGE2_PROFILE, STAGE3_PROFILE, PROD_PROFILE, PROD2_PROFILE, PROD3_PROFILE, PROD4_PROFILE, PROD5_PROFILE);
        log.info("Job SSO Id Validation Started.");
        // Check if SSO validation is enabled for the current profile
        if (!isSsoValidationEnabled) {
            log.info("SSO Id Validation is not enabled for the active profile: {}", activeProfile);
            return;
        }
        log.info("Job SSO Id Validation triggered.");
        try {
            userProfileService.validateSsoIds();
            log.info("SSO Id Validation successfully handled");
        } catch (Exception e) {
            log.error("System failed to handle SSO Id Validation", e);
        }
    }

    @Override
    protected void init() {

    }

    @Override
    protected void tearDown() {

    }

    @Override
    protected void terminate() {

    }
}
