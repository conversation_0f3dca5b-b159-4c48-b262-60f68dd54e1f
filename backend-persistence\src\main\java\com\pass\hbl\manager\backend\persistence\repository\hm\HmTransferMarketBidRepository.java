package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.TransferMarketBidUserDo;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmTransferMarketBid;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public interface HmTransferMarketBidRepository extends PagingAndSortingRepository<HmTransferMarketBid, UUID> {

    @Modifying
    @Query("update HmTransferMarketBid b set b.deletedAt = CURRENT_TIMESTAMP, b.deleted = true where b.offer.id = :id and b.deleted = false")
    void deleteAllByOfferId(@Param("id") UUID id);

    @Modifying
    @Query("update HmTransferMarketBid b set b.deletedAt = CURRENT_TIMESTAMP, b.deleted = true, b.status = 'REJECTED' where b.offer.id = :id and b.deleted = false")
    void deleteAndRejectAllByOfferId(@Param("id") UUID id);

    @Modifying
    @Query("update HmTransferMarketBid b set b.deletedAt = CURRENT_TIMESTAMP, b.deleted = true, b.status = 'REJECTED' where b.id = :id and b.deleted = false")
    void deleteAndRejectById(@Param("id") UUID id);

    @Query("select case when count(b) > 0 then true else false end from HmTransferMarketBid b where b.offer.id = :id and b.deleted=false")
    boolean existsByOfferId(@Param("id") UUID id);

    void deleteAllByBidderId(UUID bidderId);

    Iterable<HmTransferMarketBid> findByOfferOwnerId(UUID ownerId);

    Iterable<HmTransferMarketBid> findAllByOfferId(UUID offerId);

    @Query("select b.offer.id from HmTransferMarketBid b where b.id = :id and b.deleted = false")
    UUID findOfferIdById(@Param("id") UUID id);

    @Query("select b.bidder.id from HmTransferMarketBid b where b.id = :id and b.deleted = false")
    UUID findBidderIdById(@Param("id") UUID id);

    @Query(value = "SELECT * FROM hm.transfer_market_bid b WHERE b.transfer_market_id = :id and deleted = true", nativeQuery = true)
    Iterable<HmTransferMarketBid> findClosedBidsByTransferId(UUID id);

    Iterable<HmTransferMarketBid> findByOfferLeagueIdAndBidderId(UUID leagueId, UUID bidderId);

    @Query(value = "with user_league_bids as (select * from hm.transfer_market_bid b where b.bidder_id = :bidderId and b.status = 'OPEN' and b.deleted = false) select sum(bid) from user_league_bids b inner join hm.transfer_market tm on b.transfer_market_id = tm.id and tm.deleted = false where tm.league_id = :leagueId", nativeQuery = true)
    Integer  getTotalUserBidsByLeague(@Param("bidderId") UUID bidderId,@Param("leagueId") UUID leagueId);

    @Query("select b.id, b.bidder.id, b.bid, b.status, b.createdAt from HmTransferMarketBid b where b.deleted = false and b.offer.id = :offerId")
    List<Object[]> findBidInfoListByOfferId(@Param("offerId") UUID offerId);

    @Query("select b.offer.id, b.bidder.id, b.bid, b.status, b.createdAt from HmTransferMarketBid b where b.deleted = false and b.id = :id")
    List<Object[]> findBidInfoByOfferId(@Param("id") UUID id);

    @Query(value = "select Cast(u.id as varchar) as ownerId,Cast(t.player_id as varchar) as playerId, b.bid, b.status, Cast(ub.id as varchar) as bidderId, b.deleted_at as deletedAt, t.auction_end as auctionEnd, t.price " +
            " from hm.transfer_market_bid b" +
            " left outer join hm.transfer_market t on b.transfer_market_id = t.id" +
            " left outer join hm.user_profile u on u.id = t.owner_id" +
            " left outer join hm.user_profile ub on ub.id = b.bidder_id" +
            " where" +
            " (t.owner_id = :userId or b.bidder_id = :userId)" +
            " and  b.deleted = true and t.league_id = :leagueId AND b.deleted_at > CURRENT_DATE - INTERVAL '1 months'" +
            " order by b.deleted_at desc ", nativeQuery = true)
    List<TransferMarketBidUserDo> findEndedTransferByLeagueAndUser(UUID leagueId, UUID userId);

    @Query(value = "SELECT DISTINCT Cast(b.bidder_id as varchar) from hm.transfer_market_bid b  where  b.created_at > :changedAfter AND NOT b.bidder_id = :excludedId", nativeQuery = true)
    List<String> findAllUserWithSubmittedOfferAfter(@Param("changedAfter") LocalDateTime changedAfter, @Param("excludedId") UUID excludedId);

    @Query(value = "SELECT DISTINCT CAST(b.bidder_id AS varchar) " +
            "FROM hm.transfer_market_bid b " +
            "WHERE (b.created_at > :changedAfter) AND NOT b.bidder_id = :excludedId",
            nativeQuery = true)
    List<String> findAllUsersWithTransferMarketBidActivityAfter(@Param("changedAfter") LocalDateTime changedAfter, @Param("excludedId") UUID excludedId);

    @Query(value = """
    SELECT DISTINCT CAST(t.owner_id AS VARCHAR)
    FROM hm.transfer_market_bid b
    JOIN hm.transfer_market t ON t.id = b.transfer_market_id
    WHERE b.status = 'REJECTED'
      AND b.deleted = false
      AND b.modified_at > :changedAfter
      AND not t.owner_id = :excludedId
    """, nativeQuery = true)
    List<String> findAllOwnersWithRejectedTransferMarketBidsAfter(@Param("changedAfter") LocalDateTime changedAfter, @Param("excludedId") UUID excludedId);
}
