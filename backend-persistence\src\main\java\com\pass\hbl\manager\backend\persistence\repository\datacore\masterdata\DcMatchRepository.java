package com.pass.hbl.manager.backend.persistence.repository.datacore.masterdata;

import com.pass.hbl.manager.backend.persistence.entity.datacore.DcMatch;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public interface DcMatchRepository extends PagingAndSortingRepository<DcMatch, String> {

    List<DcMatch> getDcMatchByModifiedAtAfter(LocalDateTime after);

    List<DcMatch> getDcMatchByIdIsIn(Set<String> ids);

    @Query(value = "select Cast(m.home_id as varchar), Cast(m.away_id as varchar) from datacore.match m where m.id = :matchId", nativeQuery = true)
    List<String[]> findHomeAwayEntityIdByMatchId(String matchId);

    @Query(value = "select Cast(m.id as varchar), Cast(m.home_id as varchar), Cast(m.away_id as varchar) from datacore.match m where m.id in (:matchIds)", nativeQuery = true)
    List<String[]> findHomeAwayEntityIdByMatchIdIn(List<String> matchIds);
}
