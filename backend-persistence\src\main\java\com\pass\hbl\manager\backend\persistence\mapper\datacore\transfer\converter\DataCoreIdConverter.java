package com.pass.hbl.manager.backend.persistence.mapper.datacore.transfer.converter;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminExternalDataMapping;
import com.pass.hbl.manager.backend.persistence.repository.admin.AdminExternalDataMappingRepository;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class DataCoreIdConverter extends AbstractConverter<String, UUID> {

    private final AdminExternalDataMappingRepository repository;

    public DataCoreIdConverter(AdminExternalDataMappingRepository repository) {
        this.repository = repository;
    }

    @Override
    protected UUID convert(String source) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        return repository.findFirstByDatasourceAndSourceId(Datasource.DATACORE, source).map(AdminExternalDataMapping::getHmId).orElse(null);
    }


}
