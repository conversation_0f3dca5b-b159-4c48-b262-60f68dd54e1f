package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@JsonRootName("PlayerOfMonth")
@Getter
@Setter
@NoArgsConstructor
@Schema(description = "Info related to the player of the moth")
public class PlayerOfMonthDto extends AbstractPictureDto<PlayerOfMonthDto, String> {

    @Schema(description = "player of the month id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "Id of Player selected as player of the month", required = true)
    @NotBlank
    private String playerId;

    @NotBlank
    @Schema(description = "First name", example = "John", required = true)
    private String firstName;

    @NotBlank
    @Schema(description = "Last name", example = "Doe", required = true)
    private String lastName;

    @NotNull
    @Schema(description = "Position of the player", required = true)
    private Position position;

    @Min(0)
    @Schema(description = "goals thrown overall up to the given month")
    private Integer goalsThrown;

    @Min(0)
    @Schema(description = "seven meter goals thrown overall up to the given month")
    private Integer sevenMeterGoalsThrown;

    @Min(0)
    @Schema(description = "throw rate overall up to the given month")
    private Double throwRate;

    @Min(0)
    @Schema(description = "goals saved overall up to the given month")
    private Integer savedSevenMeterGoals;

    @Min(0)
    @Schema(description = "parades performed overall up to the given month")
    private Integer savedGoals;

    @Min(0)
    @Schema(description = "parade rate overall up to the given month")
    private Double saveRate;

    @Schema(description = "Total score of already played games in current season")
    private Integer totalScore;

    @Schema(description = "Id of season in which the player was selected", required = true)
    @NotBlank
    private String seasonId;

    @Schema(description = "month number", required = true)
    @Min(1)
    private int month;

    // Copy constructor
    public PlayerOfMonthDto(PlayerOfMonthDto original) {
        this.setPicture(original.getPicture());
        this.id = original.id;
        this.playerId = original.playerId;
        this.firstName = original.firstName;
        this.lastName = original.lastName;
        this.position = original.getPosition();
        this.goalsThrown = original.goalsThrown;
        this.sevenMeterGoalsThrown = original.sevenMeterGoalsThrown;
        this.throwRate = original.throwRate;
        this.savedSevenMeterGoals = original.savedSevenMeterGoals;
        this.savedGoals = original.savedGoals;
        this.saveRate = original.saveRate;
        this.totalScore = original.totalScore;
        this.seasonId = original.seasonId;
        this.month = original.month;
    }
}
