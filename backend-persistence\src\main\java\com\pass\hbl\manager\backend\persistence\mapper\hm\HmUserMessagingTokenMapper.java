package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.UserMessagingTokenDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserMessagingToken;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.springframework.stereotype.Component;


@Component
public class HmUserMessagingTokenMapper extends AbstractMapper<HmUserMessagingToken, UserMessagingTokenDto> {

    public HmUserMessagingTokenMapper() {
        super(HmUserMessagingToken.class, UserMessagingTokenDto.class);
    }
}