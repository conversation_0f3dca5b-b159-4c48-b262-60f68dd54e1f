package com.pass.hbl.manager.backend.persistence.mapper.hm.converters;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserProfile;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserProfileRepository;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.SneakyThrows;
import org.modelmapper.AbstractConverter;

public class StringToHmUserProfileConverter extends AbstractConverter<String, HmUserProfile> {

    private final HmUserProfileRepository repository;

    public StringToHmUserProfileConverter(HmUserProfileRepository repository) {
        this.repository = repository;
    }

    @SneakyThrows
    @Override
    protected HmUserProfile convert(String source) {
        if (source == null) {
            return null;
        }
        return repository.findById(Util.convertId(source)).orElseThrow(() -> new EntityNotExistException(HmUserProfile.class, source));
    }
}
