package com.pass.hbl.manager.backend.persistence.entity.sportradar;

import com.pass.hbl.manager.backend.persistence.dto.hm.MatchStatus;
import com.pass.hbl.manager.backend.persistence.dto.hm.Winner;
import com.pass.hbl.manager.backend.persistence.exception.MappingException;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Table(name = "match", schema = "sportradar", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class SrMatch extends SrAbstractMatch {

    @NotNull
    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Column(name = "start_time_confirmed")
    private boolean startTimeConfirmed;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "season_id", referencedColumnName = "id", nullable = false)
    private SrSeason season;

    @Column(name = "round")
    private Integer round;

    @Column(name = "live")
    private Boolean live;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "home_id", referencedColumnName = "id", nullable = false)
    private SrCompetitor homeCompetitor;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "away_id", referencedColumnName = "id", nullable = false)
    private SrCompetitor awayCompetitor;

    @NotNull
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private SrSportEventStatus status;

    @Column(name = "match_status")
    @Enumerated(EnumType.STRING)
    private SrMatchStatus matchStatus;

    @Column(name = "home_score")
    private Integer homeScore;

    @Column(name = "away_score")
    private Integer awayScore;

    @Column(name = "home_half_time_score")
    private Integer homeHalfTimeScore;

    @Column(name = "away_half_time_score")
    private Integer awayHalfTimeScore;

    @Enumerated(EnumType.STRING)
    @NotNull
    @Column(name = "winner")
    private Winner winner;

    @OneToMany(mappedBy = "match", fetch = FetchType.LAZY, cascade = {CascadeType.REFRESH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.DETACH})
    @ToString.Exclude
    private List<SrMatchPlayerStatistics> playerStatistics;

    @Override
    public String getHomeClubId() {
        return getHomeCompetitor().getId();
    }

    @Override
    public String getAwayClubId() {
        return getAwayCompetitor().getId();
    }

    @Override
    public Integer getHalfTimeHomeScore() {
        return homeHalfTimeScore;
    }

    @Override
    public Integer getHalfTimeAwayScore() {
        return awayHalfTimeScore;
    }

    @Override
    public String getMatchTime() {
        return null;
    }

    @Override
    public MatchStatus getMatchStatus() {
        try {
            return MatchStatus.getByHv2MatchStatus(matchStatus);
        } catch (MappingException e) {
            Util.sneakyThrow(e);
            return null;
        }
    }

    @Override
    public String getSeasonId() {
        return season.getId();
    }

    @Override
    public void setSeasonId(String seasonId) {
        // ignore for static matches
    }

    @Override
    public Integer getHblRound() {
        return round;
    }
}
