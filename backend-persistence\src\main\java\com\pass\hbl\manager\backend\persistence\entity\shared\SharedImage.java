package com.pass.hbl.manager.backend.persistence.entity.shared;

import com.pass.hbl.manager.backend.persistence.dto.shared.ImageDomain;
import com.pass.hbl.manager.backend.persistence.dto.shared.ImageMediaType;
import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.UUID;

@Table(name = "image", schema = "shared", catalog = "handball_manager")
@Getter
@Setter
@ToString
@Entity(name = "Image")
@SQLDelete(sql = "UPDATE shared.image SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class SharedImage extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;
    @ToString.Exclude
    @Column(name = "content")
    @NotNull
    private byte[] content;

    @Size(max = 256)
    @Column(name = "name")
    private String name;

    @ToString.Exclude
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "domain")
    private ImageDomain domain;

    @ToString.Exclude
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "media_type")
    private ImageMediaType mediaType;

    public SharedImage() {
    }

    public SharedImage(@NotNull byte[] content, String name, ImageDomain domain, ImageMediaType mediaType) {
        this.content = content;
        this.name = name;
        this.domain = domain;
        this.mediaType = mediaType;
    }
}
