package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@JsonRootName("League Score")
@Getter
@Setter
@Schema(description = "User round score by league")
public class LeagueScoreDto extends AbstractDto<LeagueScoreDto, String> {

    @NotNull
    @Schema(description = "id of the league score object", required = true)
    private String id;

    @NotNull
    @Schema(description = "user as member of the league", required = true)
    private UserDto user;

    @NotNull
    @Schema(description = "id of the round in which the score was gained", required = true)
    private String roundId;

    @NotNull
    @Schema(description = "id of the league in which the score was gained", required = true)
    private String leagueId;

    @Min(0)
    @Schema(description = "live score gained by the user overall up to the given round")
    private Integer liveScore;

    @Min(0)
    @Schema(description = "end of day score gained by the user overall up to the given round")
    private Integer endOfDayScore;
}
