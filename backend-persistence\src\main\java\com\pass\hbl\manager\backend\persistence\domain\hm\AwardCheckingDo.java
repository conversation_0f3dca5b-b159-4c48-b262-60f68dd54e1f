package com.pass.hbl.manager.backend.persistence.domain.hm;

import lombok.Getter;
import lombok.Setter;

import java.util.Locale;

/**
 * Domain object for transfer market awards checking
 */
@Getter
@Setter
public class AwardCheckingDo {

    private int transfersInLeagueCount;

    private int bidValue;

    private int teamSize;

    private int maxTeamSize;

    private Locale locale;

    public AwardCheckingDo(int transfersInLeagueCount, Locale locale) {
        this.transfersInLeagueCount = transfersInLeagueCount;
        this.locale = locale;
    }

    public AwardCheckingDo(int transfersInLeagueCount, int bidValue, int teamSize, int maxTeamSize, Locale locale) {
        this.transfersInLeagueCount = transfersInLeagueCount;
        this.bidValue = bidValue;
        this.teamSize = teamSize;
        this.maxTeamSize = maxTeamSize;
        this.locale = locale;
    }
}