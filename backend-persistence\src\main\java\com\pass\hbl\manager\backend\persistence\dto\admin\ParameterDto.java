package com.pass.hbl.manager.backend.persistence.dto.admin;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@JsonRootName("Parameter")
@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ParameterDto extends AbstractDto<ParameterDto, String> {

    @Schema(name = "id", description = "UUID of the parameter", required = true)
    @Size(min = 36, max = 36)
    @NotBlank
    private String id;

    @Schema(name = "name", description = "Parameter name", required = true)
    @Size(max = 64)
    @NotBlank
    private String name;

    @Schema(name = "value", description = "Parameter value", required = true)
    @Size(max = 1024)
    @NotBlank
    private String value;

    @Schema(name = "dateValue", description = "Parameter value as date time", required = true)
    private LocalDateTime dateValue;
}
