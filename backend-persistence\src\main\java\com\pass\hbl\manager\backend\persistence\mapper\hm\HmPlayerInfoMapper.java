package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.PlayerInfoDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayer;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.springframework.stereotype.Component;

@Component
public class HmPlayerInfoMapper extends AbstractMapper<HmPlayer, PlayerInfoDto> {

    public HmPlayerInfoMapper() {
        super(HmPlayer.class, PlayerInfoDto.class);
    }
}
