package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@JsonRootName("Team")
@Getter
@Setter
@Schema(description = "Team object")
public class TeamDetailsDto extends AbstractDto<TeamDetailsDto, String> {

    @JsonIgnore
    private String id;

    @NotNull
    @Schema(description = "League where this team plays in", required = true)
    private LeagueInfoDto league;

    @NotNull
    @Schema(description = "Team owner", required = true)
    private UserDto owner;

    @NotNull
    @Schema(description = "Market value of the team", required = true)
    private int marketValue;

    @NotEmpty
    @Schema(description = "Players of the team", required = true)
    private List<PlayerDetailsDto> players;

    @JsonCreator
    public TeamDetailsDto() {
    }

    public TeamDetailsDto(LeagueInfoDto league, UserDto owner, List<PlayerDetailsDto> players, int marketValue) {
        this.league = league;
        this.owner = owner;
        this.players = players;
        this.marketValue = marketValue;
    }
}
