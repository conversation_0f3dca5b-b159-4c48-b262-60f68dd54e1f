package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.util.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Objects;

@JsonRootName("User")
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@Schema(description = "Reduced user info object for listing users with basic attributes")
public class UserDto extends AbstractPictureDto<UserDto, String> {

    @Schema(description = "User id is registered with", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @Schema(description = "Username user is registered with", example = "john-doe", required = true)
    @NotBlank
    @Size(max = 256)
    private String username;

    @Schema(description = "Mail address user is registered with", example = "<EMAIL>", required = true)
    @Email(message = "Email address")
    @NotBlank
    @Size(max = 256)
    private String emailAddress;

    @Schema(description = "true, if user is premium otherwise false")
    @JsonProperty(value = "isPremium")
    private boolean premium;

    @Schema(description = "days left in the trial subscription")
    private Integer trialSubscriptionDaysLeft;

    @Schema(description = "days left in the manual subscription, activated manually in the database")
    private Integer manualSubscriptionDaysLeft;

    @Schema(description = "currently used app language")
    private String appLanguage;


    @JsonCreator
    public UserDto() {
    }

    public UserDto(String username, String emailAddress) {
        this.username = username;
        this.emailAddress = emailAddress;
    }

    public UserDto(String id, String username, String emailAddress) {
        this.id = id;
        this.username = username;
        this.emailAddress = emailAddress;
    }

    public void anonymize() {
        this.emailAddress = Constants.ANONYMOUS_EMAIL;
    }

    public static UserDto getAnonymous() {
        return new UserDto(Constants.ANONYMOUS_ID, Constants.ANONYMOUS_USERNAME, Constants.ANONYMOUS_EMAIL);
    }

    public boolean isAnonymous() {
        return Objects.equals(username, Constants.ANONYMOUS_USERNAME);
    }
}
