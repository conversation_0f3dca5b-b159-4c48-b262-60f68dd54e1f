package com.pass.hbl.manager.backend.admin;

import com.pass.hbl.manager.backend.persistence.exception.SchedulingException;
import com.pass.hbl.manager.backend.persistence.service.admin.AdminSchedulerService;
import com.pass.hbl.manager.backend.persistence.service.datacore.streaming.DataCoreRestStreamingService;
import lombok.extern.slf4j.Slf4j;
import org.reflections.Reflections;
import org.reflections.util.ClasspathHelper;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class HandballManagerImporterCommandLineRunner implements CommandLineRunner {

    private final AdminSchedulerService adminSchedulerService;
    private final DataCoreRestStreamingService dataCoreRestStreamingService;

    public HandballManagerImporterCommandLineRunner(AdminSchedulerService adminSchedulerService, DataCoreRestStreamingService dataCoreRestStreamingService) {
        this.adminSchedulerService = adminSchedulerService;
        this.dataCoreRestStreamingService = dataCoreRestStreamingService;
    }

    @Override
    public void run(String... args) throws SchedulingException {
        log.info("Starting importer command line application");
        adminSchedulerService.initJobs(new Reflections(ClasspathHelper.forClass(getClass())));
        try {
            dataCoreRestStreamingService.initializeDatCoreStreaming();
            log.info("DataCore Streaming initialized successfully");
        } catch (Exception e) {
            log.info("DataCore Streaming initialization failed. Skipping.. Reason: " + e.getMessage());
        }
    }
}
