package com.pass.hbl.manager.backend.persistence.mapper.datacore.masterdata;

import com.pass.hbl.manager.backend.persistence.entity.datacore.DcMatch;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.datacore.masterdata.converter.*;
import com.sportradar.datacore.rest.model.FixturesModel;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class DcMatchMapper extends AbstractMapper<DcMatch, FixturesModel> {
    private final DcCompetitorConverter.HomeDcCompetitorConverter homeDcCompetitorConverter;
    private final DcScoreCompetitorConverter.HomeDcScoreCompetitorConverter homeScoreDcCompetitorConverter;
    private final DcScoreCompetitorConverter.AwayDcScoreCompetitorConverter awayScoreDcCompetitorConverter;
    private final DcCompetitorConverter.AwayDcCompetitorConverter awayDcCompetitorConverter;
    private final WinnerDcConverter winnerDcConverter;
    private final LocalDateTimeConverter localDateTimeConverter;


    public DcMatchMapper(DcCompetitorConverter.HomeDcCompetitorConverter homeDcCompetitorConverter, DcScoreCompetitorConverter.HomeDcScoreCompetitorConverter homeScoreDcCompetitorConverter, DcCompetitorConverter.AwayDcCompetitorConverter awayDcCompetitorConverter, DcScoreCompetitorConverter.AwayDcScoreCompetitorConverter awayScoreDcCompetitorConverter, WinnerDcConverter winnerDcConverter, LocalDateTimeConverter localDateTimeConverter) {
        super(DcMatch.class, FixturesModel.class);
        this.homeDcCompetitorConverter = homeDcCompetitorConverter;
        this.homeScoreDcCompetitorConverter = homeScoreDcCompetitorConverter;
        this.awayDcCompetitorConverter = awayDcCompetitorConverter;
        this.awayScoreDcCompetitorConverter = awayScoreDcCompetitorConverter;
        this.winnerDcConverter = winnerDcConverter;
        this.localDateTimeConverter = localDateTimeConverter;
    }

    @Override
    protected DcMatch customizeMapToEntity(FixturesModel fixturesModel, DcMatch dcMatch, Map<String, Object> context) {
        TypeMap<FixturesModel, DcMatch> typeMap = getOrCreateTypeMap(FixturesModel.class, DcMatch.class);
        typeMap.addMappings(mapper -> mapper.map(FixturesModel::getFixtureId, DcMatch::setId));
        typeMap.addMappings(mapper -> mapper.using(localDateTimeConverter).map(FixturesModel::getStartTimeUTC, DcMatch::setStartTime));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getRoundNumber() != null ? Integer.parseInt(source.getRoundNumber()) : null, DcMatch::setRound));
        typeMap.addMappings(mapper -> mapper.using(homeDcCompetitorConverter).map(FixturesModel::getCompetitors, DcMatch::setHomeCompetitor));
        typeMap.addMappings(mapper -> mapper.using(awayDcCompetitorConverter).map(FixturesModel::getCompetitors, DcMatch::setAwayCompetitor));
        typeMap.addMappings(mapper -> mapper.map(FixturesModel::getStatus, DcMatch::setStatus));
        typeMap.addMappings(mapper -> mapper.using(homeScoreDcCompetitorConverter).map(FixturesModel::getCompetitors, DcMatch::setHomeScore));
        typeMap.addMappings(mapper -> mapper.using(awayScoreDcCompetitorConverter).map(FixturesModel::getCompetitors, DcMatch::setAwayScore));
        typeMap.addMappings(mapper -> mapper.using(winnerDcConverter).map(FixturesModel::getCompetitors, DcMatch::setWinner));
        return typeMap.map(fixturesModel);
    }
}