package com.pass.hbl.manager.backend.persistence.domain.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.NotificationEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Domain object for transfer market push notification
 */
@Getter
@Setter
public class TransferMarketNotificationDo extends AbstractNotificationDo {

    private String transferItemId;

    private String playerPicture;

    private String transferItemBidId;

    private String leagueId;

    private String playerId;

    private Integer amount;

    public TransferMarketNotificationDo(String transferItemId, String playerPicture, String transferItemBidId, String leagueId, NotificationEvent notificationEvent) {
        this.transferItemId = transferItemId;
        this.playerPicture = playerPicture;
        this.transferItemBidId = transferItemBidId;
        this.leagueId = leagueId;
        this.notificationEvent = notificationEvent;
    }

    public TransferMarketNotificationDo(String transferItemId, String playerPicture, String transferItemBidId, String leagueId, String playerId, NotificationEvent notificationEvent) {
        this.transferItemId = transferItemId;
        this.playerPicture = playerPicture;
        this.transferItemBidId = transferItemBidId;
        this.leagueId = leagueId;
        this.playerId = playerId;
        this.notificationEvent = notificationEvent;
    }

    public TransferMarketNotificationDo(String transferItemId, String playerPicture, String transferItemBidId, String leagueId, String playerId, Integer amount, NotificationEvent notificationEvent) {
        this.transferItemId = transferItemId;
        this.playerPicture = playerPicture;
        this.transferItemBidId = transferItemBidId;
        this.leagueId = leagueId;
        this.playerId = playerId;
        this.notificationEvent = notificationEvent;
        this.amount = amount;
    }
}
