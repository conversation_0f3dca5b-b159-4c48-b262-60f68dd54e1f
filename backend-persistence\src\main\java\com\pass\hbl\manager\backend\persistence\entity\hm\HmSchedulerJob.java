package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobMode;
import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobResult;
import com.pass.hbl.manager.backend.persistence.dto.SchedulerJobStatus;
import com.pass.hbl.manager.backend.persistence.entity.AbstractSchedulerJobEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

@Table(name = "scheduler_job", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor

@Entity
@SQLDelete(sql = "UPDATE hm.scheduler_job SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmSchedulerJob extends AbstractSchedulerJobEntity {

    public HmSchedulerJob(String name, String description, SchedulerJobMode mode, SchedulerJobStatus status,
                          SchedulerJobResult result, String jobClass, boolean oneTimeJob, String activeProfile) {
        super(name, description, mode, status, result, jobClass, oneTimeJob, activeProfile);
    }
}
