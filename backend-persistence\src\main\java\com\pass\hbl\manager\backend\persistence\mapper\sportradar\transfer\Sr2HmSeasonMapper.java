package com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmSeason;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrSeason;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.transfer.converter.IdConverter;
import org.springframework.stereotype.Component;

@Component
public class Sr2HmSeasonMapper extends AbstractSr2HmMapper<SrSeason, HmSeason> {


    public Sr2HmSeasonMapper(IdConverter idConverter) {
        super(SrSeason.class, HmSeason.class, idConverter);
    }
}
