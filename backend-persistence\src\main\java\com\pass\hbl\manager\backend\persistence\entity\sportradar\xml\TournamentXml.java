package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "Tournament")
@NoArgsConstructor
@Data
public class TournamentXml {

    @JacksonXmlProperty(localName = "BetradarTournamentId", isAttribute = true)
    private Integer betradarTournamentId;

    @JacksonXmlProperty(localName = "UniqueTournamentId", isAttribute = true)
    private Integer uniqueTournamentId;

    @JacksonXmlProperty(localName = "Name")
    private List<NameXml> names;

    @JacksonXmlProperty(localName = "Match")
    private List<MatchXml> matches;

}
