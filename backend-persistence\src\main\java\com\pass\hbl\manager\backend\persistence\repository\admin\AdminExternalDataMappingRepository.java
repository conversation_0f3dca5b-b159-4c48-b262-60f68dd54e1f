package com.pass.hbl.manager.backend.persistence.repository.admin;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.entity.admin.AdminExternalDataMapping;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface AdminExternalDataMappingRepository extends PagingAndSortingRepository<AdminExternalDataMapping, UUID> {

    Optional<AdminExternalDataMapping> findByDatasourceAndEntityAndSourceId(Datasource datasource, ExternalEntity entity, String sourceId);

    Optional<AdminExternalDataMapping> findFirstByDatasourceAndSourceId(Datasource datasource, String sourceId);
    void deleteByDatasourceAndEntityAndSourceId(Datasource datasource, ExternalEntity entity, String sourceId);

    Page<AdminExternalDataMapping> findByDatasource(Datasource datasource, Pageable pageable);
    Page<AdminExternalDataMapping> findByEntity(ExternalEntity entity, Pageable pageable);
    Page<AdminExternalDataMapping> findBySourceId(String id, Pageable pageable);
    Page<AdminExternalDataMapping> findByHmId(UUID id, Pageable pageable);

    Page<AdminExternalDataMapping> findByDatasourceAndEntity(Datasource datasource, ExternalEntity entity, Pageable pageable);
    Page<AdminExternalDataMapping> findByDatasourceAndSourceId(Datasource datasource, String sourceId, Pageable pageable);
    Page<AdminExternalDataMapping> findByDatasourceAndHmId(Datasource datasource, UUID hmId, Pageable pageable);
    Page<AdminExternalDataMapping> findByEntityAndSourceId(ExternalEntity entity, String sourceId, Pageable pageable);
    Page<AdminExternalDataMapping> findByEntityAndHmId(ExternalEntity entity, UUID hmId, Pageable pageable);
    Page<AdminExternalDataMapping> findBySourceIdAndHmId(String sourceId, UUID hmId, Pageable pageable);

    Page<AdminExternalDataMapping> findByDatasourceAndEntityAndSourceId(Datasource datasource, ExternalEntity entity, String sourceId, Pageable pageable);
    Page<AdminExternalDataMapping> findByDatasourceAndEntityAndHmId(Datasource datasource, ExternalEntity entity, UUID hmId, Pageable pageable);
    Page<AdminExternalDataMapping> findByDatasourceAndSourceIdAndHmId(Datasource datasource, String sourceId, UUID hmId, Pageable pageable);
    Page<AdminExternalDataMapping> findByEntityAndSourceIdAndHmId(ExternalEntity entity, String sourceId, UUID hmId, Pageable pageable);
    Optional<AdminExternalDataMapping> findByDatasourceAndEntityAndHmId(Datasource datasource, ExternalEntity entity, UUID hmId);


    Page<AdminExternalDataMapping> findByDatasourceAndEntityAndSourceIdAndHmId(Datasource datasource, ExternalEntity entity, String sourceId, UUID hmId, Pageable pageable);

    List<AdminExternalDataMapping> findByDatasourceAndEntityAndSourceIdIn(Datasource datasource, ExternalEntity entity, List<String> sourceIds);

    List<AdminExternalDataMapping> findByDatasourceAndEntityAndHmIdIn(Datasource datasource, ExternalEntity entity, List<UUID> hmIds);
}
