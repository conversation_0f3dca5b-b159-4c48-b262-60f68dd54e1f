package com.pass.hbl.manager.backend.admin.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

@Configuration
@EnableScheduling
public class ScheduledConfig implements SchedulingConfigurer {

    private final ThreadPoolTaskScheduler customTaskScheduler;

    public ScheduledConfig(@Qualifier("HBLThreadPoolScheduler") ThreadPoolTaskScheduler customTaskScheduler) {
        this.customTaskScheduler = customTaskScheduler;
    }

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setTaskScheduler(customTaskScheduler);
    }
}
