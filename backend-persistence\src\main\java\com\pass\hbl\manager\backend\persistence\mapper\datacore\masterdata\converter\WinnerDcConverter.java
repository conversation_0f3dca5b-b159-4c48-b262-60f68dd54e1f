package com.pass.hbl.manager.backend.persistence.mapper.datacore.masterdata.converter;

import com.pass.hbl.manager.backend.persistence.dto.hm.Winner;
import com.pass.hbl.manager.backend.persistence.util.Util;
import com.sportradar.datacore.rest.model.MatchCompetitor;
import org.modelmapper.AbstractConverter;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static java.util.Objects.nonNull;

@Component
public class WinnerDcConverter extends AbstractConverter<List<MatchCompetitor>, Winner> {

    @Override
    protected Winner convert(List<MatchCompetitor> matchCompetitors) {
        Optional<MatchCompetitor> optionalMatchHomeCompetitor = Util.toStream(matchCompetitors).filter(Objects::nonNull).filter(c -> nonNull(c.getIsHome()) && c.getIsHome()).findFirst();
        Optional<MatchCompetitor> optionalMatchAwayCompetitor = Util.toStream(matchCompetitors).filter(Objects::nonNull).filter(c -> nonNull(c.getIsHome()) && !c.getIsHome()).findFirst();

        if (optionalMatchHomeCompetitor.isPresent() && optionalMatchAwayCompetitor.isPresent()) {
            MatchCompetitor matchHomeCompetitor = optionalMatchHomeCompetitor.get();
            MatchCompetitor matchAwayCompetitor = optionalMatchAwayCompetitor.get();
            if (nonNull(matchHomeCompetitor.getResultPlace()) && nonNull(matchAwayCompetitor.getResultPlace())) {

                if (matchHomeCompetitor.getResultPlace().equals(matchAwayCompetitor.getResultPlace())) {
                    return Winner.DRAW;
                }
                if (Objects.equals(matchHomeCompetitor.getResultPlace(), BigDecimal.valueOf(1L))) {
                    return Winner.HOME;
                } else if (Objects.equals(matchHomeCompetitor.getResultPlace(), BigDecimal.valueOf(2L))) {
                    return Winner.AWAY;
                }
            }
        }
        return Winner.N_A;
    }
}
