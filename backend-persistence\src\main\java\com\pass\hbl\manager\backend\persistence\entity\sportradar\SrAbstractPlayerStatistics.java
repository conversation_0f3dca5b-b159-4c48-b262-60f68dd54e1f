package com.pass.hbl.manager.backend.persistence.entity.sportradar;

import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;

public class SrAbstractPlayerStatistics extends AbstractSportradarEntity {

    @Override
    public ExternalEntity getExternalEntity() {
        return ExternalEntity.PLAYER_STATISTICS;
    }

    public String getMatchSrId() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public String getPlayerSrId() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public Integer getAssistsNumber() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public Integer getSecondsPlayed() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public Integer getGoalsConceded() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public Integer getThrowsNumber() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public Integer getGoalsThrown() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public Integer getGoalsSaved() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }

    public Float getThrowRate() {
        throw new RuntimeException("Must be overridden by implementing class. This class could not declared abstract due to ModelMapper");
    }
}
