package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.MatchDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmMatch;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import org.springframework.stereotype.Component;

@Component
public class HmMatchMapper extends AbstractMapper<HmMatch, MatchDto> {

    public HmMatchMapper() { super(HmMatch.class, MatchDto.class);   }
}
