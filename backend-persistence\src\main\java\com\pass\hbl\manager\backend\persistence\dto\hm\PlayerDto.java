package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;

@JsonRootName("Player")
@Getter
@Setter
@Schema(description = "Reduced player info object")
public class PlayerDto extends AbstractPictureDto<PlayerDto, String>  implements Serializable {

    // relevant for Redis cache
    @Serial
    private static final long serialVersionUID = 1L;


    @Schema(description = "Player id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @NotBlank
    @Schema(description = "First name", example = "<PERSON>", required = true)
    private String firstName;

    @NotBlank
    @Schema(description = "Last name", example = "Doe", required = true)
    private String lastName;

    @Schema(description = "An optional status if player could not play")
    private PlayerStatus status;

    @NotNull
    @Schema(description = "Position of the player", required = true)
    private Position position;

    @Schema(description = "Optional jersey number")
    private Integer jerseyNumber;

    @Schema(description = "Current market value", required = true)
    private Integer marketValue;

    @Schema(description = "Total score of already played games in current season")
    private Integer totalScore;

    @Schema(description = "Total score of already played games in current season")
    private Integer playedGames;

    @Schema(description = "Total score by round")
    private Integer roundScore;
}
