package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

@Table(name = "client_request", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@Entity
@ToString
@NoArgsConstructor
@SQLDelete(sql = "UPDATE hm.client_request SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmClientRequest extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @Column(name = "request")
    private String request;

    @NotNull
    @Column(name = "external_client")
    private String externalClient;

    @NotNull
    @Column(name = "last_request_date")
    private LocalDateTime lastRequestDate;

    @Column(name = "parameters")
    private String parameters;

    public HmClientRequest(String request, String externalClient, LocalDateTime lastRequestDate) {
        this.request = request;
        this.externalClient = externalClient;
        this.lastRequestDate = lastRequestDate;
    }

    /**
     * Constructor with parameters map for statistics requests
     */
    public HmClientRequest(String request, String externalClient, LocalDateTime lastRequestDate,
                          Map<String, String> parameters) {
        this.request = request;
        this.externalClient = externalClient;
        this.lastRequestDate = lastRequestDate;
        setParameterMap(parameters);
    }

    /**
     * Get the parameters as a map
     *
     * @return Map of parameters
     */
    public Map<String, String> getParameterMap() {
        return Util.convertToMap(parameters);
    }

    /**
     * Set the parameters from a map
     *
     * @param map Map of parameters
     */
    public void setParameterMap(Map<String, String> map) {
        setParameters(Util.convertToString(map));
    }
}