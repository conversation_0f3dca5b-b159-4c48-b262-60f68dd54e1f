package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.pass.hbl.manager.backend.persistence.dto.admin.Datasource;
import com.pass.hbl.manager.backend.persistence.exception.MappingException;

import java.util.Arrays;

public enum LastGoal {
    HOME(1), AWAY(2);

    private final int srLiveMatchIdentifier;

    LastGoal(int srLiveMatchIdentifier) {
        this.srLiveMatchIdentifier = srLiveMatchIdentifier;
    }

    public static LastGoal getBySrLiveMatchValue(int value) throws MappingException {
        return Arrays.stream(LastGoal.values()).filter(w -> w.srLiveMatchIdentifier == value).findFirst()
                .orElseThrow(() -> new MappingException("Unknown last goal live identifier", Datasource.SPORTRADAR, String.valueOf(value), LastGoal.class));
    }
}
