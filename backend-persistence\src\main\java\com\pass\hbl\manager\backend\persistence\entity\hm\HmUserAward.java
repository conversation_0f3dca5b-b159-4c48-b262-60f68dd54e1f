package com.pass.hbl.manager.backend.persistence.entity.hm;


import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.UUID;

@Table(name = "user_award", schema = "hm", catalog = "handball_manager")
@Getter
@Setter
@Entity
@NoArgsConstructor
@ToString
@SQLDelete(sql = "UPDATE hm.user_award SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmUserAward extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @Column(name = "number_of_achievements")
    private int numberOfAchievements;

    @Column(name = "current_series")
    private int currentSeries;

    @Column(name = "longest_series")
    private int longestSeries;

    @ToString.Exclude
    @OneToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "award_id", referencedColumnName = "id", updatable = false)
    private HmAward award;

    @ToString.Exclude
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id", updatable = false)
    private HmUserProfile userProfile;

    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "league_id", referencedColumnName = "id", updatable = false)
    private HmLeague league;

    /**
     * Optional-Id of the last round in which the user award was assigned
     */
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "last_round_id", referencedColumnName = "id", updatable = false)
    private HmRound lastRound;


    public HmUserAward(HmAward award, HmUserProfile userProfile, HmLeague league) {
        this.award = award;
        this.userProfile = userProfile;
        this.league = league;
        this.currentSeries = 0;
        this.longestSeries = 0;
        this.numberOfAchievements = 1;
    }

    public HmUserAward(HmAward award, HmUserProfile userProfile, HmLeague league, HmRound lastRound) {
        this.award = award;
        this.userProfile = userProfile;
        this.league = league;
        this.currentSeries = 0;
        this.longestSeries = 0;
        this.numberOfAchievements = 1;
        this.lastRound = lastRound;
    }

    public HmUserAward(HmAward award, HmUserProfile userProfile) {
        this.award = award;
        this.userProfile = userProfile;
        this.currentSeries = 0;
        this.longestSeries = 0;
        this.numberOfAchievements = 1;
    }
}
