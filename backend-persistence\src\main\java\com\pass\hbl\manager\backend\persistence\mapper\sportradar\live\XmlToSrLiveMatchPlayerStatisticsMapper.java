package com.pass.hbl.manager.backend.persistence.mapper.sportradar.live;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrLiveMatchPlayerStatistics;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.xml.ItemXml;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.pass.hbl.manager.backend.persistence.mapper.sportradar.live.converter.IdConverter;
import com.pass.hbl.manager.backend.persistence.util.Util;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
public class XmlToSrLiveMatchPlayerStatisticsMapper extends AbstractMapper<ItemXml, SrLiveMatchPlayerStatistics> {

    public static final String KEY_SUBSTITUTE = "substitute";
    public static final String KEY_PLAYER_ID = "playerId";
    public static final String KEY_MATCH_ID = "matchId";
    public static final String KEY_TEAM_ID = "teamId";

    private final IdConverter.PlayerIdConverter playerIdConverter;
    private final IdConverter.MatchIdConverter matchIdConverter;
    private final IdConverter.CompetitorIdConverter competitorIdConverter;

    public XmlToSrLiveMatchPlayerStatisticsMapper(IdConverter.PlayerIdConverter playerIdConverter,
                                                  IdConverter.MatchIdConverter matchIdConverter,
                                                  IdConverter.CompetitorIdConverter competitorIdConverter) {
        super(ItemXml.class, SrLiveMatchPlayerStatistics.class);
        this.playerIdConverter = playerIdConverter;
        this.matchIdConverter = matchIdConverter;
        this.competitorIdConverter = competitorIdConverter;
    }

    @Override
    protected void customizeInit() {
        TypeMap<ItemXml, SrLiveMatchPlayerStatistics> typeMap = getOrCreateTypeMap(ItemXml.class, SrLiveMatchPlayerStatistics.class);
        typeMap.addMappings(mapper -> mapper.skip(SrLiveMatchPlayerStatistics::setId));
        typeMap.addMappings(mapper -> mapper.map(ItemXml::getType, SrLiveMatchPlayerStatistics::setType));
        typeMap.addMappings(mapper -> mapper.map(ItemXml::getValue, SrLiveMatchPlayerStatistics::setValue));
    }

    @Override
    protected SrLiveMatchPlayerStatistics customizeMapToDto(SrLiveMatchPlayerStatistics statistics, ItemXml itemXml, Map<String, Object> context) {
        statistics.setPlayerId(playerIdConverter.convert((Integer) context.get(KEY_PLAYER_ID)));
        statistics.setMatchId(matchIdConverter.convert((Integer) context.get(KEY_MATCH_ID)));
        statistics.setTeamId(competitorIdConverter.convert((Integer) context.get(KEY_TEAM_ID)));
        statistics.setId("sr:match:player:type:" + Util.stripSportradarId(statistics.getMatchId()) + ":" + Util.stripSportradarId(statistics.getPlayerId()) + ":" + statistics.getType());
        statistics.setSubstitute(Objects.equals(context.get(KEY_SUBSTITUTE), 1));
        return super.customizeMapToDto(statistics, itemXml, context);
    }
}
