package com.pass.hbl.manager.backend.persistence.dto.exception;

import com.pass.hbl.manager.backend.persistence.exception.CodedException;
import com.pass.hbl.manager.backend.persistence.exception.ExceptionCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpStatus;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "Error message object for handball manager API")
public record ErrorMessage(
        @Schema(description = "http status code", example = "400")
        int status,
        @Schema(description = "API specific error code")
        int code,
        @Schema(description = "Arguments for  i18n text building")
        List<String> args,
        @Schema(description = "timestamp when exception was created", format = "date-time")
        LocalDateTime dateTime,
        @Schema(description = "technical exception message")
        String message,
        @Schema(description = "complete exception stacktrace")
        String description
) {
    public static ErrorMessage produce(HttpStatus status, Throwable exception) {
        if (exception instanceof CodedException e) {
            return new ErrorMessage(status.value(), e.getCode().getCode(), e.getArgs(), LocalDateTime.now(), exception.getMessage(), ExceptionUtils.getStackTrace(exception));
        } else {
            return new ErrorMessage(status.value(), ExceptionCode.UNKNOWN.getCode(), null, LocalDateTime.now(), exception.getMessage(), ExceptionUtils.getStackTrace(exception));
        }
    }
}
