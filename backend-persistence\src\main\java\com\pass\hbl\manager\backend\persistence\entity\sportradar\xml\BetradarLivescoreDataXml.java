package com.pass.hbl.manager.backend.persistence.entity.sportradar.xml;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "BetradarLivescoreData")
@NoArgsConstructor
@Data
public class BetradarLivescoreDataXml {

    @JacksonXmlProperty(localName = "generatedAt", isAttribute = true)
    private LocalDateTime generatedAt;

    @JacksonXmlProperty(localName = "generatedFor", isAttribute = true)
    private Integer generatedFor;

    @JacksonXmlProperty(localName = "sentAt", isAttribute = true)
    private LocalDateTime sentAt;

    @JacksonXmlProperty(localName = "type", isAttribute = true)
    private String type;

    @JacksonXmlProperty(localName = "counter", isAttribute = true)
    private Integer counter;

    @JacksonXmlProperty(localName = "Sport")
    private List<SportXml> sports;
}
