package com.pass.hbl.manager.backend.persistence.mapper.sportradar.masterdata;

import com.pass.hbl.manager.backend.persistence.entity.sportradar.SrCompetitor;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import com.sportradar.handball.v2.model.SeasonCompetitor;
import org.springframework.stereotype.Component;

@Component
public class SrCompetitorMapper extends AbstractMapper<SrCompetitor, SeasonCompetitor> {
    public SrCompetitorMapper() {
        super(SrCompetitor.class, SeasonCompetitor.class);
    }
}
