package com.pass.hbl.manager.backend.persistence.mapper.datacore.live;

import com.pass.hbl.manager.backend.persistence.domain.datacore.Entity;
import com.pass.hbl.manager.backend.persistence.domain.datacore.FixtureStatus;
import com.pass.hbl.manager.backend.persistence.domain.datacore.LiveMatchSummaryDo;
import com.pass.hbl.manager.backend.persistence.dto.hm.Winner;
import com.pass.hbl.manager.backend.persistence.entity.datacore.DcLiveMatch;
import com.pass.hbl.manager.backend.persistence.mapper.AbstractMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.modelmapper.TypeMap;
import org.springframework.stereotype.Component;

import java.util.*;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
@Component
public class DoToDcLiveMatchSummaryMapper extends AbstractMapper<LiveMatchSummaryDo, DcLiveMatch> {

    public static final String DC_HOME_CLUB_ID_PAIR = "DC_HOME_CLUB_ID_PAIR";

    /*private final DcLiveMatchSummaryWinnerConverter dcLiveMatchWinnerConverter;

    private final DcLiveMatchSummaryEntityConverter.Team1EntityConverter team1EntityConverter;
    private final DcLiveMatchSummaryEntityConverter.Team2EntityConverter team2EntityConverter;

    private final DcLiveMatchSummaryScoreConverter.Team1ScoreConverter team1ScoreConverter;
    private final DcLiveMatchSummaryScoreConverter.Team2ScoreConverter team2ScoreConverter;
    */
    public DoToDcLiveMatchSummaryMapper() {
        super(LiveMatchSummaryDo.class, DcLiveMatch.class);
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    protected void customizeInit() {

        TypeMap<LiveMatchSummaryDo, DcLiveMatch> typeMap = getOrCreateTypeMap(LiveMatchSummaryDo.class, DcLiveMatch.class);
        //typeMap.addMappings(mapper -> mapper.using(team1EntityConverter).map(source -> source, DcLiveMatch::setTeam1Id));
        //typeMap.addMappings(mapper -> mapper.using(team2EntityConverter).map(source -> source, DcLiveMatch::setTeam2Id));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getStatus().getFixture(), DcLiveMatch::setStatus));
        //typeMap.addMappings(mapper -> mapper.using(dcLiveMatchWinnerConverter).map(source -> source, DcLiveMatch::setWinner));
        //typeMap.addMappings(mapper -> mapper.using(team1ScoreConverter).map(source -> source, DcLiveMatch::setTeam1CurrentScore));
        //typeMap.addMappings(mapper -> mapper.using(team2ScoreConverter).map(source -> source, DcLiveMatch::setTeam2CurrentScore));

        typeMap.addMappings(mapper -> mapper.map(source -> source.getClock().getMain(), DcLiveMatch::setClock));
        typeMap.addMappings(mapper -> mapper.map(source -> source.getClock().isRunning(), DcLiveMatch::setRunning));
    }

    @Override
    protected DcLiveMatch customizeMapToDto(DcLiveMatch dcLiveMatch, LiveMatchSummaryDo liveMatchSummaryDo, Map<String, Object> ctx) {
        if (isNull(liveMatchSummaryDo) || isNull(dcLiveMatch)) {
            return null;
        }
        // Map of key = entityId (matchId), value = Entity data (score ..)
        Map<String, Entity> entities = liveMatchSummaryDo.getEntities();
        if (isNull(liveMatchSummaryDo.getStatus()) || isNull(entities) || entities.isEmpty()) {
            return dcLiveMatch;
        }
        Entity entity1 = null;
        Entity entity2 = null;
        // if the context is empty the first entityId in the Map count as home club, second as away club
        if (isNull(ctx) || ctx.isEmpty()) {
            Set<String> keys = entities.keySet();
            List<String> keyList = new ArrayList<>(keys);
            if (keyList.size() == 2) {
                // Get the match id
                String team1Id = keyList.get(0);
                dcLiveMatch.setTeam1Id(team1Id);
                entity1 = entities.get(team1Id);
                dcLiveMatch.setTeam1CurrentScore(entity1.getScore());
                String team2Id = keyList.get(1);
                dcLiveMatch.setTeam2Id(team2Id);
                entity2 = entities.get(team2Id);
                dcLiveMatch.setTeam2CurrentScore(entity2.getScore());
            }
        } else {
            Pair<String, String> homeClubIdPair = (Pair<String, String>) ctx.get(DC_HOME_CLUB_ID_PAIR);
            if (entities.size() == 2) {
                // Get the match id
                dcLiveMatch.setTeam1Id(homeClubIdPair.getKey());
                entity1 = entities.get(homeClubIdPair.getKey());
                dcLiveMatch.setTeam1CurrentScore(entity1.getScore());

                dcLiveMatch.setTeam2Id(homeClubIdPair.getValue());
                entity2 = entities.get(homeClubIdPair.getValue());
                dcLiveMatch.setTeam2CurrentScore(entity2.getScore());
            }
        }

        // set the winner
        dcLiveMatch.setWinner(Winner.N_A);
        if (nonNull(entity1) && Objects.equals(liveMatchSummaryDo.getStatus().getFixture(), FixtureStatus.FINISHED.name())) {
            try {
                if (entity1.getScore() > entity2.getScore()) {
                    dcLiveMatch.setWinner(Winner.HOME);
                } else if (entity2.getScore() > entity1.getScore()) {
                    dcLiveMatch.setWinner(Winner.AWAY);
                } else {
                    dcLiveMatch.setWinner(Winner.DRAW);
                }
            } catch (Exception e) {
                log.error("Get Winner from Datacore streaming live match status message failed", e);
            }
        }
        return dcLiveMatch;

    }
}
