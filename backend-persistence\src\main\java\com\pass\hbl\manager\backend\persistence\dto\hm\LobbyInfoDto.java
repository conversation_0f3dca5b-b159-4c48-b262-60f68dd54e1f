package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@JsonRootName("LobbyInfo")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Lobby info used for displaying all lobby data")
public class LobbyInfoDto {

    @Schema(description = "new transfers in the league", required = true)
    private List<TransferMarketLobbyDto> newTransfers;

    @Schema(description = "last transfers in the league", required = true)
    private List<TransferMarketHistoryDto> lastTransfers;

    @Schema(description = "list of the to scorer by position", required = true)
    private List<PlayerOfMonthDto> topScorers;
}
