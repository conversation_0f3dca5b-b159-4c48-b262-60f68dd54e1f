package com.pass.hbl.manager.backend.persistence.domain.hm;

import com.pass.hbl.manager.backend.persistence.dto.hm.Position;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

/**
 * Domain object for lineup
 */
@Getter
@Setter
public class LineupDo {

    private UUID id;

    private UUID teamId;

    private UUID playerId;

    private Position position;

    public LineupDo(UUID id, UUID teamId, UUID playerId, Position position) {
        this.id = id;
        this.teamId = teamId;
        this.playerId = playerId;
        this.position = position;
    }
}