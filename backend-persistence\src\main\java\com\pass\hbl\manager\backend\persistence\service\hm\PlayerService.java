package com.pass.hbl.manager.backend.persistence.service.hm;

import com.pass.hbl.manager.backend.persistence.domain.admin.AdminPlayerInfoDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmPlayerDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmPlayerMarketValueDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmPlayerScoreStatisticsDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.*;
import com.pass.hbl.manager.backend.persistence.entity.hm.*;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.mapper.hm.*;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerMarketValueRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerOfMonthRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerRoundStatisticsRepository;
import com.pass.hbl.manager.backend.persistence.service.AbstractService;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.PlayerAdminHandler;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.PlayerOfMonthHandler;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.PlayerScoreHandler;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.PlayerStatisticsHandler;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.PageableResult;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import com.pass.hbl.manager.backend.persistence.util.ProgressLogger;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static com.pass.hbl.manager.backend.persistence.util.MappingConstants.LEAGUE_ID;
import static com.pass.hbl.manager.backend.persistence.util.Util.toLocalDateTime;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;
import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

/**
 * Service for managing all operations related to player entities
 */
@Slf4j
@Service
@Transactional
public class PlayerService extends AbstractService<HmPlayer, PlayerDto> {

    private final Random RAND = new Random();

    private final HmPlayerMapper playerMapper;
    private final HmPlayerHistoryMapper playerHistoryMapper;
    private final HmPlayerDetailsMapper playerDetailsMapper;
    private final HmPlayerProfileMapper playerProfileMapper;
    private final HmPlayerMatchScoreMapper playerMatchScoreMapper;
    private final HmClubMapper clubMapper;

    private final HmPlayerRoundStatisticsRepository statisticsRepository;
    private final HmPlayerRepository playerRepository;
    private final HmPlayerMarketValueRepository marketValueRepository;
    private final HmPlayerOfMonthRepository playerOfMonthRepository;

    private final SeasonService seasonService;
    private final LeagueService leagueService;
    private final ParameterService parameterService;
    private final UserProfileService userProfileService;
    private final TransferMarketService transferMarketService;
    private final LogMessageService logMessageService;

    private final PlayerStatisticsHandler statisticsHandler;
    private final TransactionHandler transactionHandler;
    private final PlayerOfMonthHandler playerOfMonthHandler;
    private final PlayerScoreHandler playerScoreHandler;
    private final PlayerAdminHandler playerAdminHandler;

    @Getter
    List<HmPlayerDO> allValidPlayers = new ArrayList<>();

    @Getter
    List<HmPlayer> allValidHmPlayers = new ArrayList<>();

    @Getter
    List<PlayerOfMonthDto> currentTopScorers = new ArrayList<>();

    @Getter
    private boolean isTopScorerActive = true;

    public PlayerService(HmPlayerRepository playerRepository,
                         HmPlayerMapper playerMapper,
                         HmPlayerHistoryMapper playerHistoryMapper, HmPlayerDetailsMapper playerDetailsMapper,
                         HmPlayerRoundStatisticsRepository statisticsRepository,
                         HmPlayerProfileMapper playerProfileMapper,
                         HmPlayerMatchScoreMapper playerMatchScoreMapper,
                         HmClubMapper clubMapper, HmPlayerMarketValueRepository marketValueRepository,
                         HmPlayerOfMonthRepository playerOfMonthRepository,
                         @Lazy SeasonService seasonService,
                         @Lazy LeagueService leagueService,
                         PlayerStatisticsHandler statisticsHandler,
                         ParameterService parameterService,
                         UserProfileService userProfileService, @Lazy TransferMarketService transferMarketService, LogMessageService logMessageService, TransactionHandler transactionHandler, PlayerOfMonthHandler playerOfMonthHandler, PlayerScoreHandler playerScoreHandler, PlayerAdminHandler playerAdminHandler) {
        super(playerRepository, playerMapper, HmPlayer.class);
        this.playerRepository = playerRepository;
        this.playerMapper = playerMapper;
        this.playerHistoryMapper = playerHistoryMapper;
        this.playerDetailsMapper = playerDetailsMapper;
        this.statisticsRepository = statisticsRepository;
        this.playerProfileMapper = playerProfileMapper;
        this.playerMatchScoreMapper = playerMatchScoreMapper;
        this.clubMapper = clubMapper;
        this.marketValueRepository = marketValueRepository;
        this.playerOfMonthRepository = playerOfMonthRepository;
        this.seasonService = seasonService;
        this.leagueService = leagueService;
        this.statisticsHandler = statisticsHandler;
        this.parameterService = parameterService;
        this.userProfileService = userProfileService;
        this.transferMarketService = transferMarketService;
        this.logMessageService = logMessageService;
        this.transactionHandler = transactionHandler;
        this.playerOfMonthHandler = playerOfMonthHandler;
        this.playerScoreHandler = playerScoreHandler;
        this.playerAdminHandler = playerAdminHandler;
    }


    @EventListener(ApplicationReadyEvent.class)
    @Transactional(readOnly = true)
    public void init() throws InvalidOperationException, FormatException, EntityNotExistException {
        allValidPlayers = findAllPlayersForInitialTeamSetup(Integer.MIN_VALUE, Integer.MAX_VALUE);
        logAllValidPlayersList("PlayerService: all valid players initialized");
        allValidHmPlayers = playerRepository.findAllByIdIn(allValidPlayers.stream().map(HmPlayerDO::getId).toList());
        //Important: Logging will initialize player market values
        logAllValidHmPlayersList("PlayerService: all valid HmPlayers initialized");
        LocalDateTime firstRoundClosing = seasonService.getFirstRoundInCurrentSeason().getClosing();
        if (nonNull(firstRoundClosing)) {
            LocalDateTime secondSeasonMonth = LocalDateTime.of(firstRoundClosing.getYear(), firstRoundClosing.getMonthValue() + 1, 1, 0, 0, 0);
            if (LocalDateTime.now().isAfter(secondSeasonMonth)) {
                currentTopScorers = playerOfMonthHandler.getCurrentTopScorers();
                logCurrentTopScorerList("PlayerService: current topScorers initialized");
            } else {
                isTopScorerActive = false;
                log.info("Current date is before or equal the second month of the season [" + secondSeasonMonth + "], isTopScorerActive set to [false]. Current topScorers initialization skipped..");
            }
        } else {
            isTopScorerActive = false;
            log.info("firstRoundClosing is null, isTopScorerActive set to [false]. Current topScorers initialization skipped..");
        }
    }

    @Transactional(readOnly = true, propagation = REQUIRES_NEW)
    public void updateAllValidPlayersCache() {
        allValidPlayers = findAllPlayersForInitialTeamSetup(Integer.MIN_VALUE, Integer.MAX_VALUE);
        logAllValidPlayersList("PlayerService: all valid players updated");
        allValidHmPlayers = playerRepository.findAllByIdIn(allValidPlayers.stream().map(HmPlayerDO::getId).toList());
        //Important: Logging will initialize player market values
        logAllValidHmPlayersList("PlayerService: all valid HmPlayers updated");
    }

    @Transactional(readOnly = true, propagation = REQUIRES_NEW)
    public void updateCurrentTopScorerCache() {
        currentTopScorers = playerOfMonthHandler.getCurrentTopScorers();
        logCurrentTopScorerList("PlayerService: current topScorers updated");
    }

    private void logAllValidPlayersList(String process) {
        AtomicReference<String> msg = new AtomicReference<>(process + ", count = " + allValidPlayers.size() + ", players: ");
        allValidPlayers.forEach(p -> msg.set(msg + ", [id = " + p.getId() + ", position = " + p.getPosition() + ", market value = " + p.getMarketValue() + "]"));
        log.info(msg.get());
    }

    private void logAllValidHmPlayersList(String process) {
        AtomicReference<String> msg = new AtomicReference<>(process + ", count = " + allValidPlayers.size() + ", players: ");
        allValidHmPlayers.forEach(p -> msg.set(msg + ", [id = " + p.getId() + ", position = " + p.getPosition() + ", market value = " + p.getMarketValue() + "]"));
        log.info(msg.get());
    }

    private void logCurrentTopScorerList(String process) {
        AtomicReference<String> msg = new AtomicReference<>(process + ", count = " + currentTopScorers.size() + ", players: ");
        currentTopScorers.forEach(p -> msg.set(msg + ", [id = " + p.getId() + ", position = " + p.getPosition() + ", totalScore = " + p.getTotalScore() + ", month = " + p.getMonth() + "]"));
        log.info(msg.get());
    }

    public PageableResult<PlayerDto> findPlayer(String firstName, String lastName, Position position, Pageable pageable) {
        if (StringUtils.isNotEmpty(firstName)) {
            firstName = "%" + firstName + "%";
        }
        if (StringUtils.isNotEmpty(lastName)) {
            firstName = "%" + lastName + "%";
        }

        if (StringUtils.isEmpty(firstName) && StringUtils.isEmpty(lastName) && position == null) {
            return getAllAsDto(pageable);
        }
        if (StringUtils.isEmpty(firstName) && StringUtils.isEmpty(lastName)) {
            return mapEntityPageToDtoPageableResult(playerRepository.findByPosition(position, pageable), playerMapper, pageable);
        }
        if (StringUtils.isEmpty(firstName) && position == null) {
            return mapEntityPageToDtoPageableResult(playerRepository.findByLastNameLikeIgnoreCase(lastName, pageable), playerMapper, pageable);
        }
        if (StringUtils.isEmpty(lastName) && position == null) {
            return mapEntityPageToDtoPageableResult(playerRepository.findByFirstNameLikeIgnoreCase(firstName, pageable), playerMapper, pageable);
        }
        if (StringUtils.isEmpty(firstName)) {
            return mapEntityPageToDtoPageableResult(playerRepository.findByLastNameLikeIgnoreCaseAndPosition(lastName, position, pageable), playerMapper, pageable);
        }
        if (StringUtils.isEmpty(lastName)) {
            return mapEntityPageToDtoPageableResult(playerRepository.findByFirstNameLikeIgnoreCaseAndPosition(firstName, position, pageable), playerMapper, pageable);
        }
        if (position == null) {
            return mapEntityPageToDtoPageableResult(playerRepository.findByFirstNameLikeIgnoreCaseAndLastNameLikeIgnoreCase(firstName, lastName, pageable), playerMapper, pageable);
        }
        return mapEntityPageToDtoPageableResult(playerRepository.findByFirstNameLikeIgnoreCaseAndLastNameLikeIgnoreCaseAndPosition(firstName, lastName, position, pageable), playerMapper, pageable);
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public HmPlayer getRandomPlayer(@NotNull UUID leagueId, Position position, Set<UUID> existing, List<Integer> prices) throws InvalidOperationException, EntityNotExistException, FormatException {
        //List<HmPlayerDO> allValidPlayers = findAllPlayersForInitialTeamSetup(Integer.MIN_VALUE, Integer.MAX_VALUE);
        if (allValidPlayers.isEmpty() || allValidHmPlayers.isEmpty()) {
            log.info("getRandomPlayer: allValidPlayers is empty. Update is necessary");
            updateAllValidPlayersCache();
        }
        return doGetRandomPlayer(leagueId, position, existing, allValidPlayers);
    }

    @org.jetbrains.annotations.NotNull
    public HmPlayer doGetRandomPlayer(UUID leagueId, Position position, Set<UUID> existing, List<HmPlayerDO> allValidPlayers) throws InvalidOperationException, EntityNotExistException, FormatException {
        HmPlayer player = null;
        Pageable pageable = Pageable.ofSize(1);
        int maxTries = 50;
        //boolean isValidMarketValue = false;
        // while (player == null || !isValidMarketValue) {
        while (player == null) {
            Optional<HmPlayerDO> randomPlayerOpt = getRandomPlayerForTeamSetup(position, allValidPlayers, existing.stream().toList());
            //player = randomPlayerOpt.isPresent()? getById(randomPlayerOpt.get().getId()): null;
            player = randomPlayerOpt.isPresent() ? getByIdFromCache(randomPlayerOpt.get().getId()) : null;
            /*if (existing.isEmpty()) {
                player = playerRepository.findRandomPlayer(leagueId, position, pageable).stream().findFirst().orElse(null);
            } else {
                player = playerRepository.findRandomPlayerForDuplicatedPosition(leagueId, position, existing, pageable).stream().findFirst().orElse(null);
            }*/
            maxTries--;
            if (maxTries == 0) {
                throw new InvalidOperationException("getRandomPlayer", "system", "cannot find free random player for league " + leagueId + " and position " + position + " ignoring " + existing);
            }
            // validate that the player market value meets the condition: Players who have a market_value of 15.000 €
            // should be offered by START7 with a probability of only 75% to prevent flooding the transfer market with "poor players".
            // temporary deactivated
            /*if (nonNull(player)) {
                isValidMarketValue = isValidPlayerMarketValue(prices, player.getMarketValue());
            }*/
        }
        int numberOfTries = 50 - maxTries;
        log.info("getRandomPlayer: number of tries = [" + numberOfTries + "] for league id[" + leagueId + "] and position = " + position);
        // call getMarketValue once so that lazy collection is already initialized even if we leave this transaction
        //TODO hbs check if this is relevant
        //player.getMarketValue();
        return player;
    }

    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public HmPlayer getByIdFromCache(UUID id) throws EntityNotExistException, FormatException {
        Optional<HmPlayer> optionalHmPlayer = allValidHmPlayers.stream().filter(p -> Objects.equals(p.getId(), id)).findFirst();
        if (optionalHmPlayer.isPresent()) {
            return optionalHmPlayer.get();
        } else {
            HmPlayer player = getById(id);
            // market value should be explicitly initialized in read-only transaction
            player.getMarketValue();
            return player;
        }
    }

    private boolean isValidPlayerMarketValue(List<Integer> prices, Integer playerMarketValue) {
        if (isNull(playerMarketValue)) return false;
        List<Integer> poorPlayers = prices.stream().filter(p -> p <= POOR_PLAYER_MARKET_VALUE).collect(toList());
        if (playerMarketValue <= POOR_PLAYER_MARKET_VALUE) {
            poorPlayers.add(playerMarketValue);
            double currentPoorPlayersPercentage = ((double) poorPlayers.size() / prices.size()) * 100;
            return currentPoorPlayersPercentage < POOR_PLAYERS_PERCENTAGE;
        } else {
            return true;
        }
    }

    @Lock(LockModeType.READ)
    @Transactional(propagation = REQUIRES_NEW, readOnly = true)
    public List<HmPlayerDO> findAllPlayersForInitialTeamSetup(int minMarketValue, int maxMarketValue) {
        return playerRepository.findAllPlayersByMarketValueIn(minMarketValue, maxMarketValue);
    }

    public Optional<HmPlayerDO> getRandomPlayerForTeamSetup(Position position, List<HmPlayerDO> allPlayers, List<UUID> existing) {
        List<HmPlayerDO> players;
        if (isNull(existing) || existing.isEmpty()) {
            players = allPlayers.stream().filter(hmPlayerDO -> hmPlayerDO.getPosition().equals(position)).toList();
        } else {
            players = allPlayers.stream().filter(hmPlayerDO -> hmPlayerDO.getPosition().equals(position)
                    && !existing.contains(hmPlayerDO.getId())).toList();
        }
        return players.isEmpty() ? Optional.empty() : Optional.of(players.get(RAND.nextInt(players.size())));
    }

    public void setAllInactive() {
        playerRepository.setAllInactive();
    }

    public PlayerDto mapToDto(HmPlayer entity, Map<String, Object> context) {
        return playerMapper.mapToDto(entity, context);
    }

    public PlayerHistoryDto mapToHistoryDto(HmPlayer entity, Map<String, Object> context) {
        return playerHistoryMapper.mapToDto(entity, context);
    }
    public PlayerDetailsDto mapToPlayerDetailsDto(HmPlayer entity) {
        return playerDetailsMapper.mapToDto(entity);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public PlayerProfileDto getPlayerProfile(@NotNull String id, @NotNull String leagueId) throws EntityNotExistException, FormatException {
        HmPlayer hmPlayer = getById(id);
        HmLeague hmLeague = leagueService.getById(leagueId);
        return playerProfileMapper.mapToDto(hmPlayer, Map.of(LEAGUE_ID, hmLeague.getId()));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<PlayerMatchScoreResponseDto> getScoreHistory(@NotNull String id) throws EntityNotExistException, FormatException {
        HmPlayer player = getById(id);
        List<PlayerMatchScoreResponseDto> playerMatchScoreList = new ArrayList<>();
        // last matchByPlayerClubIdPairs relevant for player score:
        // - id of the club in which the player has player assigned to the corresponding match
        // - related to the matches of current and previous round

        List<HmSeason> seasons = seasonService.getCurrentAndPreviousSeason();
        List<Pair<UUID, HmMatch>> matchByPlayerClubIdPairs = statisticsHandler.getPlayerScoreHistoryMatches(player, seasons);
        List<UUID> matchIds = matchByPlayerClubIdPairs.stream().map(pair -> pair.getValue().getId()).collect(toList());
        // score statistics
        List<HmPlayerRoundStatistics> playerRoundStatistics
                = statisticsRepository.findByPlayerIdAndMatchIds(Util.convertId(id), matchIds);
        List<PlayerMatchScoreDto> playerMatchScoreDtos
                = playerRoundStatistics.stream().map(playerMatchScoreMapper::mapToDto).toList();
        for (int i = matchByPlayerClubIdPairs.size() - 1; i >= 0; i--) {
            UUID playerClubId = matchByPlayerClubIdPairs.get(i).getKey();
            HmMatch match = matchByPlayerClubIdPairs.get(i).getValue();
            HmClub challenger = match.getHome().getId().equals(playerClubId) ? match.getAway() : match.getHome();
            PlayerMatchScoreDto playerMatchScore = playerMatchScoreDtos.stream()
                    .filter(ps -> Objects.equals(ps.getMatchId(), match.getId().toString())).findFirst().orElse(null);
            // set the right challenger explicitly
            if (nonNull(playerMatchScore) && nonNull(challenger)) {
                playerMatchScore.setChallenger(clubMapper.mapToDto(challenger));
            }
            //Player match score found with null score, set to zero
            if (nonNull(playerMatchScore) && (isNull(playerMatchScore.getTotalScore())))
                playerMatchScore.setTotalScore(0);
            //Player Match score not found, create new playerMatchScore with score = null. This case is handled in the Frontend
            // as player has not played
            if (isNull(playerMatchScore)) {
                //HmClub challenger = match.getHome().getId().equals(playerClubId)? match.getAway(): match.getHome();
                playerMatchScore = new PlayerMatchScoreDto(match.getId().toString(), null, clubMapper.mapToDto(challenger));
            }
            playerMatchScoreList.add(new PlayerMatchScoreResponseDto(match.getId().toString(), i + 1,
                    seasonService.getSeasonLabel(match.getRound().getId().toString()), playerMatchScore));
        }
        return playerMatchScoreList;
    }

    public List<PlayerMarketValueStatisticDto> getMarketValueHistory(@NotNull String playerId, Integer quantity, StatisticsUnit unit)
            throws EntityNotExistException, FormatException {
        HmPlayer hmPlayer = getById(playerId);
        List<PlayerMarketValueStatisticDto> playerMarketValueStatistics = statisticsHandler.createPlayerMarketValueStatistics(unit, quantity);
        // get reduced dataset
        LocalDateTime statisticStart = playerMarketValueStatistics.get(playerMarketValueStatistics.size() - 1).getFrom().toLocalDateTime();
        List<HmPlayerMarketValue> marketValueHistory = marketValueRepository.findByPlayerIdAndValidAfterDate(hmPlayer.getId(), statisticStart);
        if (marketValueHistory.isEmpty()) {
            Optional<HmPlayerMarketValue> playerMarketValue = marketValueRepository.findLastMarketValueValidAfterDate(hmPlayer.getId(), statisticStart);
            playerMarketValue.ifPresent(marketValueHistory::add);
        }
        List<HmPlayerMarketValue> sortedMarketValueHistory = new ArrayList<>(marketValueHistory);
        sortedMarketValueHistory.sort(Comparator.comparing(HmPlayerMarketValue::getValidFrom).reversed());
        for (PlayerMarketValueStatisticDto statistic : playerMarketValueStatistics) {
            statisticsHandler.getAverageMarketValue(sortedMarketValueHistory, toLocalDateTime(statistic.getFrom()), toLocalDateTime(statistic.getTo())).ifPresent(statistic::setMarketValue);
        }
        return playerMarketValueStatistics;
    }

    // Simulates the update of player market values
    public void updateMarketValues(String currentDateStr) throws FormatException, InvalidOperationException {
        updateMarketValues(toLocalDateTime(currentDateStr).toLocalDate());
    }

    @Transactional(propagation = REQUIRES_NEW)
    public void updateMarketValues(LocalDate currentDate) throws InvalidOperationException, FormatException {
        List<HmPlayerMarketValue> latestPlayerMarketValues =
                transactionHandler.runInNewTransactionReadOnly(() -> Util.toStream(marketValueRepository.findByCurrentValue(true)).toList());
        LocalDate lastDay = currentDate.minusDays(1);
        LocalDateTime latestValidTo = LocalDateTime.of(lastDay.getYear(), lastDay.getMonth(), lastDay.getDayOfMonth(), 23, 59, 59);
        String userId = userProfileService.getSystemUserId().toString();
        int lowestMarketValue = this.parameterService.getAsInteger(ParameterDefaults.PARAM_LOWEST_MARKET_VALUE, userId);
        List<HmPlayerMarketValue> currentPlayerMarketValues = new ArrayList<>();

        ProgressLogger progressLogger = new ProgressLogger(log, ProgressLogger.Level.INFO, latestPlayerMarketValues.size(), "Updating market values for " + currentDate, 10);
        progressLogger.logStart();
        for (HmPlayerMarketValue m : latestPlayerMarketValues) {
            progressLogger.increment();
            Integer latestMarketValue = m.getMarketValue();
            HmPlayer player = transactionHandler.runInNewTransactionReadOnly(() -> {
                try {
                    return getPlayerByMarketValue(m.getId());
                } catch (Exception e) {
                    return null;
                }
            });
            if (isNull(player)) {
                continue;
            }

            Integer currentValue = transferMarketService.getAveragePlayerTransferValuesInDateInterval(player.getId(), LocalDateTime.of(lastDay, LocalTime.MIN), LocalDateTime.of(lastDay, LocalTime.MAX));
            int marketValueToSave;
            if (nonNull(currentValue) && currentValue >= lowestMarketValue && currentValue.intValue() != latestMarketValue) {
                // adjust the market value to be in interval [85% -> 125%]
                int adjustedMarketValue = getAdjustedMarketValue(latestMarketValue, currentValue);
                // adjust the market value to be less that the maximum market value
                marketValueToSave = checkMaxMarketValue(adjustedMarketValue);
                // create a new entity with current value
                currentPlayerMarketValues.add(new HmPlayerMarketValue(player, LocalDateTime.of(currentDate, LocalTime.MIN), marketValueToSave, null, true));
            } else {
                // adjust the market value to be less that the maximum market value
                marketValueToSave = checkMaxMarketValue(latestMarketValue);
                // create new entity with the previous value
                currentPlayerMarketValues.add(new HmPlayerMarketValue(player, LocalDateTime.of(currentDate, LocalTime.MIN), marketValueToSave, null, true));
            }
            //Update  latest values
            marketValueRepository.resetCurrentValueById(m.getId(), latestValidTo);
        }
        progressLogger.logFinish();
        marketValueRepository.saveAll(currentPlayerMarketValues);
    }

    private int checkMaxMarketValue(int marketValue) {
        if (marketValue > MAX_MARKET_VALUE) {
            return new Random().nextInt(RANDOM_MARKET_VALUE_LOWER_LIMIT, MAX_MARKET_VALUE);
        } else {
            return marketValue;
        }
    }

    private HmPlayer getPlayerByMarketValue(UUID playerMarketValueId) throws EntityNotExistException, FormatException {
        UUID playerId = marketValueRepository.findPlayerIdById(playerMarketValueId);
        return getById(playerId);
    }

    private int getAdjustedMarketValue(int latestMarketValue, int currentMarketValue) {
        if (currentMarketValue > latestMarketValue && (currentMarketValue - latestMarketValue) > (latestMarketValue * 0.25)) {
            return Double.valueOf(latestMarketValue * 1.25).intValue();
        } else if (latestMarketValue > currentMarketValue && (latestMarketValue - currentMarketValue) > (latestMarketValue * 0.15)) {
            return Double.valueOf(latestMarketValue * 0.85).intValue();
        }
        return currentMarketValue;
    }

    public Optional<HmPlayerMarketValue> getMarketValueAtDate(UUID playerId, LocalDate date) {
        return marketValueRepository.findByPlayerIdValidInDay(playerId, LocalDateTime.of(date, LocalTime.MIN), LocalDateTime.of(date, LocalTime.MAX)).stream().findFirst();
    }

    @Transactional(readOnly = true, propagation = REQUIRES_NEW)
    public List<HmPlayerMarketValueDO> getAllPlayerMarketValuesAtDate(LocalDate date) {
        List<HmPlayerMarketValueDO> allPlayerMarketValuesAtDate = marketValueRepository.findAllPlayerMarketValuesAtDate(LocalDateTime.of(date, LocalTime.MIN), LocalDateTime.of(date, LocalTime.MAX));
        // In case more than one player market value by day is found, get the one with the newest validFrom date
        Map<String, Optional<HmPlayerMarketValueDO>> marketValueByPlayerIdMap = allPlayerMarketValuesAtDate.stream().collect(Collectors.groupingBy(HmPlayerMarketValueDO::getPlayerId, Collectors.maxBy(Comparator.comparing(HmPlayerMarketValueDO::getValidFrom))));
        return marketValueByPlayerIdMap.values().stream().filter(Optional::isPresent).map(Optional::get).collect(toList());
    }

    @Transactional(readOnly = true, propagation = REQUIRES_NEW)
    public Optional<HmPlayerMarketValue> getLatestMarketValue(UUID playerId) {
        return marketValueRepository.findLatestMarketValue(playerId);
    }

    public List<HmPlayerRoundScore> getPlayerRoundScores(List<UUID> playerIds, UUID roundId) {
        return playerScoreHandler.getPlayerRoundScore(playerIds, roundId);
    }

    public List<PlayerRoundScoreDetailsDto> getLineupScoreDetails(List<UUID> playerIds, UUID roundId, Locale locale) {
        return playerScoreHandler.getExistingPlayerScoreDetailsForStreaming(playerIds, roundId, locale);
    }

    public List<PlayerRoundScoreDetailsDto> getLineupScoreDetailsForStreaming(List<UUID> playerIds, UUID roundId, Locale locale) throws EntityNotExistException {
        return playerScoreHandler.getExistingPlayerScoreDetailsForStreaming(playerIds, roundId, locale);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public PlayerOfMonthDto getPlayerOfMonth(Integer month, Position position) throws EntityNotExistException {
        // get the top scorer of a given month by position or over all positions in case the position is null
        return playerOfMonthHandler.getPlayerOfMonth(month, position);
    }

    public PlayerOfMonthDto setPlayerOfMonth(@NotNull String playerId, @NotNull Integer month, Integer totalScore) throws EntityNotExistException {
        // set the player of month over all positions
        return playerAdminHandler.setPlayerOfMonth(getPlayerReadOnly(playerId), month, totalScore);
    }

    @Transactional(propagation = REQUIRES_NEW)
    public void setTopScorerByMonthAndPosition(LocalDate localDate) throws EntityNotExistException {
        // set the top scorer by position in the previous month of the given date
        int month = localDate.getMonthValue() == 1 ? 12 : localDate.getMonthValue() - 1;
        doSetTopScorerByMonthAndPosition(month);
    }

    public void doSetTopScorerByMonthAndPosition(int month) throws EntityNotExistException {
        HmSeason currentSeason = seasonService.getCurrentSeason();

        //remove existing top scorer of month
        int rows = playerOfMonthRepository.deleteBySeasonIdAndMonth(currentSeason.getId(), month);
        log.info("setTopScorersOfMonth: existing [" + rows + "] rows deleted for month [" + month + "] and season [" + currentSeason.getId() + "]");

        // calculate top scorer by position
        Map<Position, HmPlayerScoreStatisticsDO> topScorerByPosition = statisticsHandler.getAllTopScorerInMonth(month, currentSeason);
        if (isNull(topScorerByPosition) || topScorerByPosition.isEmpty()) {
            log.info("setTopScorersOfMonth: topScorerByPosition is null or empty. Skipping..");
            return;
        }
        Arrays.stream(Position.values()).forEach(position -> {
            HmPlayerScoreStatisticsDO scoreStatisticsDO = topScorerByPosition.get(position);
            if (isNull(scoreStatisticsDO)) {
                String message = "Top scorer not found for month [" + month + "] and position [" + position.name() + "]";
                log.info(message);
                logMessageService.logInfo("setTopScorersOfMonth", message);
            } else {
                HmPlayer player = transactionHandler.runInNewTransactionReadOnly(() -> {
                    try {
                        return getByIdFromCache(scoreStatisticsDO.playerId());
                    } catch (Exception e) {
                        log.info("setTopScorersOfMonth: player with id [" + scoreStatisticsDO.playerId() + "] not found. Skipping..");
                        return null;
                    }
                });
                if (nonNull(player)) {
                    HmPlayerOfMonth playerOfMonth = new HmPlayerOfMonth(player, currentSeason, month, scoreStatisticsDO.totalScore());
                    playerOfMonthRepository.save(playerOfMonth);
                }
            }
        });
    }

    public PlayerLobbyDto getPlayerLobbyDto(UUID playerId) throws EntityNotExistException, FormatException {
        HmPlayer player = getByIdFromCache(playerId);
        String picture = Objects.nonNull(player.getPicture()) ? player.getPicture().toString() : null;
        return new PlayerLobbyDto(player.getId().toString(), player.getFirstName(), player.getLastName(), player.getPosition(), player.getMarketValue(), picture);
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<AdminPlayerInfoDO> getAllAdminPlayerInfoDOs(Pageable pageable, String playerName) {
        Pageable sortedPageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by("firstName").ascending());
        if (playerName == null || playerName.isEmpty()) {
            return playerAdminHandler.getAllAdminPlayerInfoDOs(Util.toStream(playerRepository.findAll(sortedPageable)).toList());
        } else {
            return playerAdminHandler.getAllAdminPlayerInfoDOs(Util.toStream(playerRepository.findByFirstNameOrLastNameLike(playerName, sortedPageable)).toList());
        }
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<AdminPlayerInfoDO> getAllAdminPlayerInfoDOs(Pageable pageable) {
            return playerAdminHandler.getAllAdminPlayerInfoDOs(Util.toStream(playerRepository.findAll(pageable)).toList());
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public String getAllPlayerScoresByRound(String roundNumber, @NotNull Boolean specialRound) throws EntityNotExistException {
        return playerAdminHandler.getAllPlayerScoresByRound(seasonService.getByRoundNumber(Integer.parseInt(roundNumber), specialRound).getId());
    }

    public void addOrUpdatePlayerMarketValue(String playerId, int currentMarketValue, String adminUser) {
        playerAdminHandler.addOrUpdatePlayerMarketValue(playerId, currentMarketValue, adminUser);
    }

    private HmPlayer getPlayerReadOnly(String id) throws EntityNotExistException {
        return transactionHandler.runInNewTransactionReadOnly(() -> playerRepository.findById(UUID.fromString(id)))
                .orElseThrow(() -> new EntityNotExistException(HmPlayer.class, id));
    }

    @Lock(LockModeType.READ)
    @Transactional(readOnly = true)
    public List<PlayerHblDto> getAllPlayerMarketValueScoreByRoundDetails(String externalClient, String requestUrl) throws InvalidOperationException, FormatException, EntityNotExistException {
        return playerScoreHandler.getAllPlayerMarketValueScoreByRoundDetails(externalClient, requestUrl, allValidHmPlayers);
    }
}
