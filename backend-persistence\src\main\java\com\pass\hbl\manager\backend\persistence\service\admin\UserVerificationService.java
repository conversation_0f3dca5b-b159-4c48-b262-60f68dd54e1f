package com.pass.hbl.manager.backend.persistence.service.admin;

import com.pass.hbl.manager.backend.persistence.entity.admin.AdminUser;
import com.pass.hbl.manager.backend.persistence.repository.admin.AdminUserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
@Slf4j
public class UserVerificationService {

    private final AdminUserRepository adminUserRepository;

    @Autowired
    JavaMailSender javaMailSender;

    @Autowired
    Environment environment;

    public UserVerificationService(AdminUserRepository adminUserRepository) {
        this.adminUserRepository = adminUserRepository;
    }

    public boolean generateOtp(AdminUser user) {

        try {
            int randomPIN = (int) (Math.random() * 900000) + 100000;
            user.setOtp(randomPIN);
            adminUserRepository.save(user);
            SimpleMailMessage msg = new SimpleMailMessage();
            String mailSender = environment.getProperty("spring.mail.sender", "<EMAIL>");
            msg.setFrom(mailSender); // input the senders email ID
            msg.setTo(user.getEmailAddress());

            msg.setSubject("START7 Verification Code");
            msg.setText("Dear admin user " + user.getUsername() + ", " + "\n\n" + "Your Login OTP (one time password): " + randomPIN + "\n\nPlease use this code to verify you login. \n\n" + "Best Regards. \n\n" + "START7 Team");

            javaMailSender.send(msg);

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
