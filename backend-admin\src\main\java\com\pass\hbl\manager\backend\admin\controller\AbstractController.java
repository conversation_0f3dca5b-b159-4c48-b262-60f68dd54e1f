package com.pass.hbl.manager.backend.admin.controller;

import com.pass.hbl.manager.backend.admin.config.HandballManagerAdminConfigurationProperties;
import com.pass.hbl.manager.backend.admin.util.PaginatedResultsRetrievedEvent;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.util.PageableResult;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import lombok.Getter;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

public abstract class AbstractController {

    @Getter
    private final ApplicationEventPublisher eventPublisher;


    protected AbstractController(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    protected <T extends AbstractDto<T, ?>> List<T> getListFromPageableResult(Object source,
                                                                              UriComponentsBuilder uriBuilder,
                                                                              HttpServletRequest request,
                                                                              HttpServletResponse response,
                                                                              PageableResult<T> pageable) {
        eventPublisher.publishEvent(new PaginatedResultsRetrievedEvent(source, request.getRequestURI(), uriBuilder, response, pageable.page(), pageable.totalPages(), pageable.pageSize()));
        return pageable.content();
    }

    protected <T extends AbstractDto<T, ?>> List<T> getAllPaginated(PageableResult<T> pageable,UriComponentsBuilder uriBuilder,
                                                                   HttpServletRequest request,
                                                                   HttpServletResponse response) {
        getListFromPageableResult(this, uriBuilder, request, response, pageable);
        return pageable.content();
    }

    protected String[] getEnumValues(Class<? extends Enum<?>> e) throws InvalidOperationException {
        if (e.isEnum()) {
            return Arrays.stream(e.getEnumConstants()).map(String::valueOf).toArray(String[]::new);
        }
        throw new InvalidOperationException("Get enum values", ParameterDefaults.SYSTEM_USER, "Class " + e.getSimpleName() + " is not an enum");
    }

    public Optional<String> getRequesterEmail(HttpServletRequest request, HandballManagerAdminConfigurationProperties properties) {
        String authorizationHeader = request.getHeader("Authorization");

        if (authorizationHeader != null && authorizationHeader.startsWith("Basic")) {
            String credentials = authorizationHeader.substring("Basic ".length());
            String decodedCredentials = new String(Base64.getDecoder().decode(credentials));

            // Extract username and password from decodedCredentials
            String[] usernameAndPassword = decodedCredentials.split(":");
            String username = usernameAndPassword[0];
            return Optional.of(username);
            /*if (Objects.equals(username, properties.getSecurity().getAdminUser())) {
                return Optional.of(ROLE_ADMIN);
            } else if (Objects.equals(username, properties.getSecurity().getAdminWriteUser())) {
                return Optional.of(ROLE_ADMIN_WRITE);
            }*/
        }
        return Optional.empty();
    }

    public void handleUserEmailNotFound(HttpServletResponse response) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("text/plain");
        response.getWriter().write("Unauthorized. Please enter your username and password to access this endpoint");
    }
}
