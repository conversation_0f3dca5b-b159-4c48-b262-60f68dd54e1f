package com.pass.hbl.manager.backend.persistence.dto.hm;

import com.fasterxml.jackson.annotation.JsonRootName;
import com.pass.hbl.manager.backend.persistence.dto.AbstractDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;
import java.util.List;

@JsonRootName("TransferMarket")
@Getter
@Setter
@Schema(description = "Transfer market")
public class TransferMarketDto extends AbstractDto<TransferMarketDto, String> {

    @Schema(description = "Bid id", example = "005aa59d-81d9-41eb-9df7-94867a1f7542", required = true, accessMode = Schema.AccessMode.READ_ONLY)
    @NotBlank
    @Size(min = 36, max = 36)
    private String id;

    @NotNull
    @Schema(description = "User owning of the transfer item", required = true)
    private UserDto owner;

    @NotNull
    @Schema(description = "Id of the league related to the transfer item", required = true)
    private String leagueId;

    @NotNull
    @Schema(description = "Player offered for a transfer", required = true)
    private PlayerDto player;

    @Schema(description = "Bids of the transfer item")
    private List<BidDto> bids;

    @NotNull
    @Schema(description = "Transfer price", required = true)
    private int price;

    @NotNull
    @Schema(description = "Ending date of the auction", required = true)
    private ZonedDateTime auctionEnd;

    @Schema(description = "Id of the previous player added by the system, only relevant for system transfer items")
    private String previousPlayerId;

    @Schema(description = "Creation datetime of the transfer item")
    private ZonedDateTime createdAt;

    public TransferMarketDto(String id, UserDto owner, String leagueId, PlayerDto player, List<BidDto> bids, int price, ZonedDateTime auctionEnd, String previousPlayerId, ZonedDateTime createdAt) {
        this.id = id;
        this.owner = owner;
        this.leagueId = leagueId;
        this.player = player;
        this.bids = bids;
        this.price = price;
        this.auctionEnd = auctionEnd;
        this.previousPlayerId = previousPlayerId;
        this.createdAt = createdAt;
    }

    public TransferMarketDto() {
    }
}
