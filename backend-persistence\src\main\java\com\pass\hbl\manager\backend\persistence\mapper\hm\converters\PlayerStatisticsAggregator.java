package com.pass.hbl.manager.backend.persistence.mapper.hm.converters;

import com.pass.hbl.manager.backend.persistence.dto.hm.*;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerMatchEvent;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmPlayerRoundStatistics;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmRound;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerMatchEventRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmPlayerRoundStatisticsRepository;
import com.pass.hbl.manager.backend.persistence.service.hm.SeasonService;
import com.pass.hbl.manager.backend.persistence.service.hm.helpers.SeasonHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Stream;

import static com.pass.hbl.manager.backend.persistence.util.Util.toDoubleWithDecimals;
import static java.util.Collections.emptyList;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
@Component
public class PlayerStatisticsAggregator {

    private final SeasonService seasonService;

    private final HmPlayerRoundStatisticsRepository statisticsRepository;
    private final HmPlayerMatchEventRepository playerMatchEventRepository;


    public PlayerStatisticsAggregator(SeasonService seasonService, HmPlayerRoundStatisticsRepository statisticsRepository, HmPlayerMatchEventRepository playerMatchEventRepository) {
        this.seasonService = seasonService;
        this.statisticsRepository = statisticsRepository;
        this.playerMatchEventRepository = playerMatchEventRepository;
    }

    public PlayerStatisticsDto getPlayerStatistics(UUID playerId, Position position) {
        try {
            //get already played rounds of the current season
            List<HmPlayerRoundStatistics> playerRoundStatistics = this.getAlreadyPlayedRoundsStatistics(playerId);
            // aggregate statistics of already played rounds
            return aggregatePlayerRoundStatistics(position, playerRoundStatistics);
        } catch (EntityNotExistException | FormatException e) {
            log.error("Could not aggregate statistics for player [id=" + playerId.toString() + "]. Skipping ...", e);
            return null;
        }
    }

    public Integer getTotalScorePlayedRounds(UUID playerId) {
        try {
            return getTotalScore(getAlreadyPlayedRoundsStatistics(playerId));
        } catch (EntityNotExistException | FormatException e) {
            log.error("Could not aggregate statistics (total score played rounds) for player [id=" + playerId.toString() + "]. Skipping ...", e);
            return null;
        }
    }

    public Integer getGamesPlayed(UUID playerId) {
        try {
            return getAlreadyPlayedRoundsStatistics(playerId).size();
        } catch (EntityNotExistException | FormatException e) {
            log.error("Could not aggregate statistics (games played) for player [id=" + playerId.toString() + "]. Skipping ...", e);
            return null;
        }
    }

    public Integer getGamesPlayed(UUID playerId, List<HmRound> rounds) {
        try {
            return getAlreadyPlayedRoundsStatistics(playerId, rounds.stream().map(HmRound::getId).toList()).intValue();
        } catch (Exception e) {
            log.error("Could not aggregate statistics (games played) for player [id=" + playerId.toString() + "]. Skipping ...", e);
            return null;
        }
    }

    public void setPlayerOfMonthStatistics(PlayerOfMonthDto playerOfMonthDto, int monthNumber, Position position) {
        List<HmPlayerRoundStatistics> playerRoundStatistics = getPlayerMatchRoundStatistics(monthNumber, UUID.fromString(playerOfMonthDto.getPlayerId()));
        if (position.name().equals(Position.G.name())) {
            playerOfMonthDto.setSavedGoals(playerRoundStatistics.stream().filter(Objects::nonNull).filter(s -> Objects.nonNull(s.getGoalsSaved())).mapToInt(HmPlayerRoundStatistics::getGoalsSaved).sum());
            //TODO should be checked if it is still needed in the Frontend
            //playerOfMonthDto.setSavedSevenMeterGoals(playerStatisticsAggregator.getSavedSevenMeterGoalsByMonth(monthNumber, playerId));
            playerOfMonthDto.setSaveRate(getSaveRateByMonth(playerRoundStatistics));
        } else {
            playerOfMonthDto.setGoalsThrown(playerRoundStatistics.stream().filter(Objects::nonNull).filter(s -> Objects.nonNull(s.getGoalsThrown())).mapToInt(HmPlayerRoundStatistics::getGoalsThrown).sum());
            //TODO should be checked if it is still needed in the Frontend
            //playerOfMonthDto.setSevenMeterGoalsThrown(playerStatisticsAggregator.getSevenMeterGoalsByMonth(monthNumber, playerId));
            playerOfMonthDto.setThrowRate(getThrowRateByMonth(playerRoundStatistics));
        }
        // if the total_score is already calculated by the PlayerOfMonthJob no need re-calculate it
        if (isNull(playerOfMonthDto.getTotalScore())) {
            playerOfMonthDto.setTotalScore(playerRoundStatistics.stream().filter(Objects::nonNull).filter(s -> Objects.nonNull(s.getTotalScore())).mapToInt(HmPlayerRoundStatistics::getTotalScore).sum());
        }
    }

    public double getThrowRateByMonth(List<HmPlayerRoundStatistics> playerRoundStatistics) {
        int goalsThrown = playerRoundStatistics.stream()
                .filter(Objects::nonNull)
                .filter(s -> nonNull(s.getGoalsThrown()))
                .mapToInt(HmPlayerRoundStatistics::getGoalsThrown).sum();
        int throwsNumberSum = playerRoundStatistics.stream()
                .filter(Objects::nonNull)
                .filter(s -> nonNull(s.getThrowsNumber()))
                .mapToInt(HmPlayerRoundStatistics::getThrowsNumber).sum();
        return throwsNumberSum != 0 ? toDoubleWithDecimals(((double) goalsThrown / throwsNumberSum), 2) : 0;
    }

    public double getSaveRateByMonth(List<HmPlayerRoundStatistics> playerRoundStatistics) {
        int goalsSavedSum = playerRoundStatistics.stream()
                .filter(Objects::nonNull)
                .filter(s -> nonNull(s.getGoalsSaved()))
                .mapToInt(HmPlayerRoundStatistics::getGoalsSaved).sum();
        int goalsConcededSum = playerRoundStatistics.stream()
                .filter(Objects::nonNull)
                .filter(s -> nonNull(s.getGoalsConceded()))
                .mapToInt(HmPlayerRoundStatistics::getGoalsConceded).sum();
        int goalsSavedAndConceded = goalsSavedSum + goalsConcededSum;
        return goalsSavedAndConceded != 0 ?
                toDoubleWithDecimals(((double) goalsSavedSum / goalsSavedAndConceded), 2) : 0;
    }

    public int getSevenMeterGoalsByMonth(int monthNumber, UUID playerId) {
        List<HmPlayerMatchEvent> playerMatchEvents = getPlayerMatchEvents(monthNumber, playerId);
        return Long.valueOf(getMatchEventsWithCharacteristic(playerMatchEvents).filter(pme -> (pme.getEvent().name().equals(Event.SEVEN_M.name())
                && pme.getEventCharacteristic().name()
                .equals(EventCharacteristic.GOAL.name()))).count()).intValue();
    }

    public int getSavedSevenMeterGoalsByMonth(int monthNumber, UUID playerId) {
        List<HmPlayerMatchEvent> playerMatchEvents = getPlayerMatchEvents(monthNumber, playerId);
        return Long.valueOf(getMatchEventsWithCharacteristic(playerMatchEvents).filter(pme -> (pme.getEvent().name().equals(Event.SEVEN_M.name())
                && pme.getEventCharacteristic().name()
                .equals(EventCharacteristic.SAVE.name()))).count()).intValue();
    }

    private int countByEventCharacteristic(List<HmPlayerMatchEvent> playerMatchEvents, EventCharacteristic eventCharacteristic) {
        return Long.valueOf(getMatchEventsWithCharacteristic(playerMatchEvents).filter(playerMatchEvent -> playerMatchEvent.getEventCharacteristic().name()
                .equals(eventCharacteristic.name())).count()).intValue();
    }

    private Stream<HmPlayerMatchEvent> getMatchEventsWithCharacteristic(List<HmPlayerMatchEvent> playerMatchEvents) {
        return playerMatchEvents.stream().filter(pme -> nonNull(pme.getEventCharacteristic()));
    }

    private List<HmPlayerMatchEvent> getPlayerMatchEvents(int monthNumber, UUID playerId) {
        LocalDateTime now = LocalDateTime.now();
        int year = monthNumber > now.getMonthValue()? now.getYear() - 1: now.getYear() ;
        LocalDateTime start = LocalDateTime.of(LocalDate.of(year, monthNumber, 1), LocalTime.MIN);
        LocalDateTime end = LocalDateTime.of(start.with(TemporalAdjusters.lastDayOfMonth()).toLocalDate(), LocalTime.MAX);
        return playerMatchEventRepository.findByPlayerIdInInterval(playerId, start, end);
    }

    private List<HmPlayerRoundStatistics> getPlayerMatchRoundStatistics(int monthNumber, UUID playerId) {
        LocalDateTime now = LocalDateTime.now();
        int year = monthNumber > now.getMonthValue()? now.getYear() - 1: now.getYear() ;
        LocalDateTime start = LocalDateTime.of(LocalDate.of(year, monthNumber, 1), LocalTime.MIN);
        LocalDateTime end = LocalDateTime.of(start.with(TemporalAdjusters.lastDayOfMonth()).toLocalDate(), LocalTime.MAX);
        return  statisticsRepository.findByPlayerIdInInterval(playerId, start, end);
    }

    private List<HmPlayerRoundStatistics> getAlreadyPlayedRoundsStatistics(UUID playerId) throws EntityNotExistException, FormatException {
        //get already played rounds of the current season
        try {
            List<HmRound> rounds = seasonService.getSortedRoundsCurrentSeason();
            HmRound currentRound = seasonService.getCurrentRound();
            int currentRoundIndex = SeasonHelper.getRoundIndex(rounds, currentRound);
            if (currentRoundIndex > 0) {
                List<UUID> previousRounds = new ArrayList<>(rounds.subList(0, currentRoundIndex).stream().map(HmRound::getId).toList());
                // include the last round in the statistics if now is after the last round end.
                // This is used in case the round is reached but the round end is extended for technical reasons
                if (seasonService.isSummerBreak()) {
                    previousRounds.add(currentRound.getId());
                }
                return statisticsRepository.findByPlayerIdAndRoundIds(playerId, previousRounds);
            }
        } catch (EntityNotExistException | FormatException e) {
            log.warn("No current season or current round: " + e.getMessage());
        }
        return emptyList();
    }

    private Long getAlreadyPlayedRoundsStatistics(UUID playerId, List<UUID> rounds) {
        return statisticsRepository.countByPlayerIdAndRoundIds(playerId, rounds);
    }

    private PlayerStatisticsDto aggregatePlayerRoundStatistics(Position position, List<HmPlayerRoundStatistics> playerRoundStatistics) {
        int count = playerRoundStatistics.size();
        if (count > 0) {
            int totalScore = getTotalScore(playerRoundStatistics);
            //Position specific statistics
            boolean isGoalkeeper = Objects.equals(position.name(), Position.G.name());
            //Field Player
            int assistsNumberSum = isGoalkeeper ? 0 : playerRoundStatistics.stream()
                    .filter(Objects::nonNull)
                    .filter(s -> nonNull(s.getAssistsNumber()))
                    .mapToInt(HmPlayerRoundStatistics::getAssistsNumber).sum();
            int throwsNumberSum = isGoalkeeper ? 0 : playerRoundStatistics.stream()
                    .filter(Objects::nonNull)
                    // if number of throws is null, replace it with the goals thrown
                    .peek(urs -> urs.setThrowsNumber(isNull(urs.getThrowsNumber())? urs.getGoalsThrown(): urs.getThrowsNumber()))
                    .filter(s -> nonNull(s.getThrowsNumber()))
                    .mapToInt(HmPlayerRoundStatistics::getThrowsNumber).sum();
            Integer goalsThrown = isGoalkeeper ? null : playerRoundStatistics.stream()
                    .filter(Objects::nonNull)
                    .filter(s -> nonNull(s.getGoalsThrown()))
                    .mapToInt(HmPlayerRoundStatistics::getGoalsThrown).sum();
            Double throwRate = isGoalkeeper ? null :
                    (throwsNumberSum != 0 ? (goalsThrown / (double) throwsNumberSum * 100.0) : 0);
            // Adjust throw rate if it exceeds 100%
            if (nonNull(throwRate) && throwRate > 100d) {
                throwRate = 100d;
            }

            // Goalkeeper
            Integer goalsSavedSum = null;
            Double averageSavedGoals = null;
            Double saveRate = null;
            int averageSecondsPlayed = 0;
            if (isGoalkeeper) {
                goalsSavedSum = playerRoundStatistics.stream()
                        .filter(Objects::nonNull)
                        .filter(s -> nonNull(s.getGoalsSaved()))
                        .mapToInt(HmPlayerRoundStatistics::getGoalsSaved).sum();
                averageSavedGoals = goalsSavedSum / (double) count;
                int goalsConcededSum = playerRoundStatistics.stream()
                        .filter(Objects::nonNull)
                        .filter(s -> nonNull(s.getGoalsConceded()))
                        .mapToInt(HmPlayerRoundStatistics::getGoalsConceded).sum();
                int goalsSavedAndConceded = goalsSavedSum + goalsConcededSum;
                saveRate = goalsSavedAndConceded != 0 ? (goalsSavedSum / (double) goalsSavedAndConceded * 100.0) : 0;
                // Adjust save rate if it exceeds 100%
                if (saveRate > 100d) {
                    saveRate = 100d;
                }
                int secondsPlayedSum = playerRoundStatistics.stream()
                        .filter(Objects::nonNull)
                        .filter(s -> nonNull(s.getSecondsPlayed()))
                        .mapToInt(HmPlayerRoundStatistics::getSecondsPlayed).sum();
                averageSecondsPlayed = secondsPlayedSum / count;
            }
            return new PlayerStatisticsDto(StatisticsUnit.SEASON, totalScore, (totalScore / (double) count), count,
                    assistsNumberSum, averageSecondsPlayed, goalsThrown, throwRate, goalsSavedSum,
                    saveRate, averageSavedGoals);
        }
        return null;
    }

    private int getTotalScore(List<HmPlayerRoundStatistics> playerRoundStatistics) {
        return playerRoundStatistics.stream()
                .filter(Objects::nonNull)
                .filter(s -> nonNull(s.getTotalScore()))
                .mapToInt(HmPlayerRoundStatistics::getTotalScore)
                .sum();
    }
}
