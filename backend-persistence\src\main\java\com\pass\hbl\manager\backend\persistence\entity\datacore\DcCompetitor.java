package com.pass.hbl.manager.backend.persistence.entity.datacore;

import com.pass.hbl.manager.backend.persistence.dto.admin.ExternalEntity;
import com.pass.hbl.manager.backend.persistence.entity.sportradar.AbstractSportradarEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Table(name = "competitor", schema = "datacore", catalog = "handball_manager")
@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
public class DcCompetitor extends AbstractSportradarEntity {

    @NotNull
    @Column(name = "name")
    private String name;

    @Column(name = "short_name")
    private String shortName;

    @Column(name = "hbl_image_id")
    private String hblImageId;

    @Column(name = "abbreviation")
    private String abbreviation;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "season_id")
    private DcSeason season;

    @Override
    public ExternalEntity getExternalEntity() {
        return ExternalEntity.COMPETITOR;
    }
}
